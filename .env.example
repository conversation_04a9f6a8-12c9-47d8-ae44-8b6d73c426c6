# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Application Environment
NODE_ENV=development
PYTHON_ENV=development

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/xvion_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=xvion_db
DB_USER=username
DB_PASSWORD=password

# API Keys - External Services
DEEPL_AUTH_KEY=your_deepl_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Image APIs
UNSPLASH_API_KEY_1=your_primary_unsplash_key
UNSPLASH_API_KEY_2=your_backup_unsplash_key_1
UNSPLASH_API_KEY_3=your_backup_unsplash_key_2
PIXABAY_API_KEY=your_pixabay_api_key
PEXELS_API_KEY=your_pexels_api_key

# Application Security
SECRET_KEY=your_super_secret_key_here_min_32_chars
JWT_SECRET=your_jwt_secret_key_here
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Server Configuration
BACKEND_PORT=8001
FRONTEND_PORT=3000
API_BASE_URL=http://localhost:8001

# File Storage
UPLOAD_DIR=./uploads
OUTPUT_DIR=./output
ASSETS_DIR=./assets
MAX_FILE_SIZE=100MB

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# Monitoring and Analytics
SENTRY_DSN=your_sentry_dsn_here
ANALYTICS_ID=your_analytics_id_here

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Video Processing
MAX_VIDEO_DURATION=300
MAX_CONCURRENT_JOBS=3
CLEANUP_TEMP_FILES=true
