#!/bin/bash

# Xvion Production Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="./logs/deploy_$(date +%Y%m%d_%H%M%S).log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p logs backups uploads output ssl
    success "Directories created"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        warning ".env file not found. Creating from template..."
        cp .env.example .env
        warning "Please edit .env file with your configuration before continuing."
        read -p "Press Enter to continue after editing .env file..."
    fi
    
    success "Prerequisites check completed"
}

# Backup database
backup_database() {
    if [ "$ENVIRONMENT" = "production" ]; then
        log "Creating database backup..."
        mkdir -p "$BACKUP_DIR"
        
        # Export database
        docker-compose exec -T postgres pg_dump -U xvion_user xvion_db > "$BACKUP_DIR/database_backup.sql" || warning "Database backup failed"
        
        # Backup uploaded files
        cp -r uploads "$BACKUP_DIR/" 2>/dev/null || warning "No uploads directory to backup"
        cp -r output "$BACKUP_DIR/" 2>/dev/null || warning "No output directory to backup"
        
        success "Backup created at $BACKUP_DIR"
    fi
}

# Build and deploy
deploy() {
    log "Starting deployment for $ENVIRONMENT environment..."
    
    # Pull latest code (if in git repository)
    if [ -d .git ]; then
        log "Pulling latest code..."
        git pull origin main || warning "Git pull failed"
    fi
    
    # Build and start services
    log "Building and starting services..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.yml up --build -d
    else
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build -d
    fi
    
    success "Services started"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."

    # Wait for database to be ready
    sleep 10

    # Check if this is first deployment (no migrations exist)
    if [ ! -d "alembic/versions" ] || [ -z "$(ls -A alembic/versions 2>/dev/null)" ]; then
        log "First deployment detected - creating initial migration..."

        # Create initial migration
        docker-compose run --rm migrate python migrate.py initial || warning "Initial migration creation failed"

        # Run the migration
        docker-compose run --rm migrate python migrate.py upgrade || warning "Initial migration failed"
    else
        log "Running existing migrations..."

        # Run pending migrations
        docker-compose run --rm migrate python migrate.py upgrade || warning "Migration failed"
    fi

    success "Database migrations completed"
}

# Health checks
health_checks() {
    log "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check backend health
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        success "Backend health check passed"
    else
        error "Backend health check failed"
    fi
    
    # Check frontend health
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        success "Frontend health check passed"
    else
        error "Frontend health check failed"
    fi
    
    # Check database connection
    if docker-compose exec postgres pg_isready -U xvion_user > /dev/null 2>&1; then
        success "Database health check passed"
    else
        error "Database health check failed"
    fi
    
    success "All health checks passed"
}

# Cleanup old containers and images
cleanup() {
    log "Cleaning up old containers and images..."
    
    # Remove stopped containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes (be careful with this in production)
    if [ "$ENVIRONMENT" != "production" ]; then
        docker volume prune -f
    fi
    
    success "Cleanup completed"
}

# Setup SSL certificates (for production)
setup_ssl() {
    if [ "$ENVIRONMENT" = "production" ]; then
        log "Setting up SSL certificates..."
        
        if [ ! -f ssl/cert.pem ] || [ ! -f ssl/key.pem ]; then
            warning "SSL certificates not found. Please add your SSL certificates to the ssl/ directory:"
            warning "  ssl/cert.pem - Your SSL certificate"
            warning "  ssl/key.pem - Your SSL private key"
            warning "For development, you can generate self-signed certificates:"
            warning "  openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes"
        else
            success "SSL certificates found"
        fi
    fi
}

# Main deployment process
main() {
    log "Starting Xvion deployment process..."
    log "Environment: $ENVIRONMENT"
    
    create_directories
    check_prerequisites
    setup_ssl
    backup_database
    deploy
    run_migrations
    health_checks
    cleanup
    
    success "Deployment completed successfully!"
    log "Services are running:"
    log "  - Frontend: http://localhost:3000"
    log "  - Backend API: http://localhost:8001"
    log "  - API Documentation: http://localhost:8001/docs"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        log "  - Production URL: https://your-domain.com"
    fi
    
    log "Logs are available in: $LOG_FILE"
}

# Handle script interruption
trap 'error "Deployment interrupted"' INT TERM

# Run main function
main
