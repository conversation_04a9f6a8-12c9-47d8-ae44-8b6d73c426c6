#!/usr/bin/env python3
"""
Xvion API Server
FastAPI backend for the Xvion Arabic Quiz Video Generator
"""

import os
import json
import uuid
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
from sqlalchemy.orm import Session
from passlib.context import CryptContext
import uvicorn

# Import database models and functions
from database import (
    get_db, create_tables, init_default_data, test_connection,
    Quiz as DBQuiz, Question as DBQuestion, VideoJob as DBVideoJob,
    APIKey as DBAPIK<PERSON>, AccountTemplate as DBAccountTemplate, AppSettings as DBAppSettings,
    User as DBUser
)

# Import the existing video generator functions
from config import *
import quiz_positioning_config_arabic

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """Hash a password using bcrypt"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

app = FastAPI(
    title="Xvion API",
    description="Arabic Quiz Video Generator API",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Data models
class Question(BaseModel):
    id: str
    text: str
    options: List[str]
    correctAnswer: str
    explanation: Optional[str] = None

class Quiz(BaseModel):
    id: str
    title: str
    description: Optional[str] = None
    questions: List[Question]
    language: str = "ar"
    createdAt: datetime
    updatedAt: datetime

class VideoGenerationConfig(BaseModel):
    accountName: str = "Account 1"
    displayName: str = "Default Account"
    colorMode: str = "mode1"
    outputFolder: str = "./output"
    fontType: str = "./assets/Fonts/NotoSansArabic-Bold.ttf"
    backgroundFolder: str = "./assets/Back Videos"
    numberOfQuestions: int = 6
    questionDuration: int = 10
    totalDuration: int = 60

class VideoGenerationJob(BaseModel):
    id: str
    quizId: str
    config: VideoGenerationConfig
    status: str = "pending"  # pending, processing, completed, failed
    progress: int = 0
    startedAt: Optional[datetime] = None
    completedAt: Optional[datetime] = None
    outputPath: Optional[str] = None
    error: Optional[str] = None
    logs: List[str] = []

class APIKeysModel(BaseModel):
    deepl: Optional[str] = None
    elevenlabs: Optional[str] = None
    unsplash: Optional[List[str]] = None
    pixabay: Optional[str] = None
    pexels: Optional[str] = None
    openai: Optional[str] = None

# Load sample data from CSV into database
def load_sample_data(db: Session):
    """Load sample quizzes from CSV into database"""
    try:
        # Check if sample data already exists
        existing_quiz = db.query(DBQuiz).filter_by(title="Arabic Animals Quiz").first()
        if existing_quiz:
            print("Sample data already exists, skipping...")
            return

        import pandas as pd
        csv_path = "./assets/C_Arabic 3.csv"
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)

            # Create quiz
            quiz = DBQuiz(
                id="sample-quiz-1",
                title="Arabic Animals Quiz",
                description="Test your knowledge of animals in Arabic",
                language="ar"
            )
            db.add(quiz)
            db.flush()  # Get the quiz ID

            # Create questions
            for index, row in df.iterrows():
                if index >= 6:  # Limit to 6 questions
                    break

                question = DBQuestion(
                    quiz_id=quiz.id,
                    text=str(row.iloc[0]) if len(row) > 0 else "",
                    options=[str(row.iloc[i]) for i in range(1, min(4, len(row)))],
                    correct_answer=str(row.iloc[4]) if len(row) > 4 else str(row.iloc[2]),
                    order_index=index
                )
                db.add(question)

            db.commit()
            print("✅ Sample data loaded successfully!")

    except Exception as e:
        db.rollback()
        print(f"❌ Error loading sample data: {e}")

# Health check
@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# Authentication endpoints (MVP - now using database)
@app.post("/api/auth/login")
async def login(credentials: Dict[str, Any], db: Session = Depends(get_db)):
    """
    Login endpoint - now using database with proper password hashing
    """
    email_or_username = credentials.get("email_or_username")
    password = credentials.get("password")

    if not email_or_username or not password:
        raise HTTPException(status_code=400, detail="Email/username and password are required")

    # Check demo users first (for backward compatibility)
    demo_users = {
        "<EMAIL>": {"password": "admin123", "name": "Admin User", "id": "admin-1", "email": "<EMAIL>", "username": "admin"},
        "<EMAIL>": {"password": "demo123", "name": "Demo User", "id": "demo-1", "email": "<EMAIL>", "username": "demo"},
        "admin": {"password": "admin123", "name": "Admin User", "id": "admin-1", "email": "<EMAIL>", "username": "admin"},
        "demo": {"password": "demo123", "name": "Demo User", "id": "demo-1", "email": "<EMAIL>", "username": "demo"}
    }

    user_data = None

    # First check demo users
    if email_or_username.lower() in demo_users:
        demo_user = demo_users[email_or_username.lower()]
        if demo_user["password"] == password:
            user_data = demo_user

    # If not found in demo users, check database
    if not user_data:
        db_user = db.query(DBUser).filter(
            (DBUser.email == email_or_username) | (DBUser.username == email_or_username)
        ).first()

        if db_user:
            try:
                # Try to verify password with bcrypt
                if verify_password(password, db_user.password_hash):
                    # Update last login
                    db_user.last_login = datetime.utcnow()
                    db.commit()

                    user_data = {
                        "id": db_user.id,
                        "name": db_user.full_name,
                        "email": db_user.email,
                        "username": db_user.username
                    }
            except Exception as e:
                # Handle case where password hash is invalid (e.g., plain text from old system)
                print(f"⚠️ Password verification failed for user {email_or_username}: {e}")
                # For old users with plain text passwords, check directly (temporary migration)
                if db_user.password_hash == password:
                    print(f"🔄 Migrating plain text password for user {email_or_username}")
                    # Update to hashed password
                    db_user.password_hash = hash_password(password)
                    db_user.last_login = datetime.utcnow()
                    db.commit()

                    user_data = {
                        "id": db_user.id,
                        "name": db_user.full_name,
                        "email": db_user.email,
                        "username": db_user.username
                    }

    if not user_data:
        raise HTTPException(status_code=401, detail="Invalid credentials")

    # MVP: Return simple token (in production, use proper JWT)
    token = f"mvp_token_{user_data['id']}_{datetime.now().timestamp()}"

    return {
        "access_token": token,
        "token_type": "bearer",
        "user": user_data
    }

@app.post("/api/auth/register")
async def register(user_data: Dict[str, Any], db: Session = Depends(get_db)):
    """
    Registration endpoint - now using database with proper password hashing
    """
    email = user_data.get("email")
    username = user_data.get("username")
    full_name = user_data.get("full_name")
    password = user_data.get("password")
    confirm_password = user_data.get("confirm_password")

    # Basic validation
    if not all([email, username, full_name, password, confirm_password]):
        raise HTTPException(status_code=400, detail="All fields are required")

    if password != confirm_password:
        raise HTTPException(status_code=400, detail="Passwords do not match")

    if len(password) < 6:
        raise HTTPException(status_code=400, detail="Password must be at least 6 characters")

    # Check if user already exists (demo users)
    demo_users = ["<EMAIL>", "<EMAIL>", "admin", "demo"]
    if email.lower() in demo_users or username.lower() in demo_users:
        raise HTTPException(status_code=400, detail="User already exists")

    # Check if email or username already exists in database
    existing_user = db.query(DBUser).filter(
        (DBUser.email == email) | (DBUser.username == username)
    ).first()

    if existing_user:
        if existing_user.email == email:
            raise HTTPException(status_code=400, detail="Email already exists")
        else:
            raise HTTPException(status_code=400, detail="Username already exists")

    # Create new user in database
    hashed_password = hash_password(password)
    new_user = DBUser(
        email=email,
        username=username,
        full_name=full_name,
        password_hash=hashed_password,
        is_active=True,
        is_verified=False,  # In production, implement email verification
        role="user"
    )

    try:
        db.add(new_user)
        db.commit()
        db.refresh(new_user)

        print(f"✅ New user registered in database: {username} ({email})")
        print(f"📊 User ID: {new_user.id}")

        return {
            "message": "Registration successful",
            "user": {
                "id": new_user.id,
                "name": new_user.full_name,
                "email": new_user.email,
                "username": new_user.username
            }
        }
    except Exception as e:
        db.rollback()
        print(f"❌ Error creating user: {e}")
        raise HTTPException(status_code=500, detail="Failed to create user")

@app.post("/api/auth/forgot-password")
async def forgot_password(data: Dict[str, Any]):
    """
    MVP Forgot password endpoint - simplified for demo purposes
    """
    email = data.get("email")

    if not email:
        raise HTTPException(status_code=400, detail="Email is required")

    # MVP: Always return success for demo (in production, send actual email)
    return {
        "message": "Password reset email sent successfully",
        "email": email
    }

@app.post("/api/auth/logout")
async def logout():
    """
    MVP Logout endpoint - for client-side token cleanup
    In a real implementation, you might want to blacklist the token
    """
    return {"message": "Logged out successfully"}

# Quiz endpoints
@app.get("/api/quizzes", response_model=List[Quiz])
async def get_quizzes(db: Session = Depends(get_db)):
    db_quizzes = db.query(DBQuiz).all()
    quizzes = []

    for db_quiz in db_quizzes:
        questions = []
        for db_question in db_quiz.questions:
            question = Question(
                id=db_question.id or str(uuid.uuid4()),
                text=db_question.text,
                options=db_question.options,
                correctAnswer=db_question.correct_answer,
                explanation=db_question.explanation
            )
            questions.append(question)

        quiz = Quiz(
            id=db_quiz.id,
            title=db_quiz.title,
            description=db_quiz.description,
            questions=questions,
            language=db_quiz.language,
            createdAt=db_quiz.created_at,
            updatedAt=db_quiz.updated_at
        )
        quizzes.append(quiz)

    return quizzes

@app.get("/api/quizzes/{quiz_id}", response_model=Quiz)
async def get_quiz(quiz_id: str, db: Session = Depends(get_db)):
    db_quiz = db.query(DBQuiz).filter(DBQuiz.id == quiz_id).first()
    if not db_quiz:
        raise HTTPException(status_code=404, detail="Quiz not found")

    questions = []
    for db_question in db_quiz.questions:
        question = Question(
            id=db_question.id or str(uuid.uuid4()),
            text=db_question.text,
            options=db_question.options,
            correctAnswer=db_question.correct_answer,
            explanation=db_question.explanation
        )
        questions.append(question)

    quiz = Quiz(
        id=db_quiz.id,
        title=db_quiz.title,
        description=db_quiz.description,
        questions=questions,
        language=db_quiz.language,
        createdAt=db_quiz.created_at,
        updatedAt=db_quiz.updated_at
    )

    return quiz

@app.post("/api/quizzes", response_model=Quiz)
async def create_quiz(quiz_data: Dict[str, Any], db: Session = Depends(get_db)):
    try:
        # Create quiz
        db_quiz = DBQuiz(
            title=quiz_data["title"],
            description=quiz_data.get("description"),
            language=quiz_data.get("language", "ar")
        )
        db.add(db_quiz)
        db.flush()  # Get the quiz ID

        # Create questions
        questions = []
        for index, q_data in enumerate(quiz_data.get("questions", [])):
            db_question = DBQuestion(
                quiz_id=db_quiz.id,
                text=q_data["text"],
                options=q_data["options"],
                correct_answer=q_data["correctAnswer"],
                explanation=q_data.get("explanation"),
                order_index=index
            )
            db.add(db_question)

            question = Question(
                id=db_question.id or str(uuid.uuid4()),
                text=db_question.text,
                options=db_question.options,
                correctAnswer=db_question.correct_answer,
                explanation=db_question.explanation
            )
            questions.append(question)

        db.commit()

        quiz = Quiz(
            id=db_quiz.id,
            title=db_quiz.title,
            description=db_quiz.description,
            questions=questions,
            language=db_quiz.language,
            createdAt=db_quiz.created_at,
            updatedAt=db_quiz.updated_at
        )

        return quiz

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error creating quiz: {str(e)}")

@app.put("/api/quizzes/{quiz_id}", response_model=Quiz)
async def update_quiz(quiz_id: str, quiz_data: Dict[str, Any], db: Session = Depends(get_db)):
    db_quiz = db.query(DBQuiz).filter(DBQuiz.id == quiz_id).first()
    if not db_quiz:
        raise HTTPException(status_code=404, detail="Quiz not found")

    try:
        # Update quiz fields
        db_quiz.title = quiz_data.get("title", db_quiz.title)
        db_quiz.description = quiz_data.get("description", db_quiz.description)
        db_quiz.language = quiz_data.get("language", db_quiz.language)
        db_quiz.updated_at = datetime.utcnow()

        # Update questions if provided
        if "questions" in quiz_data:
            # Delete existing questions
            db.query(DBQuestion).filter(DBQuestion.quiz_id == quiz_id).delete()

            # Add new questions
            for index, q_data in enumerate(quiz_data["questions"]):
                db_question = DBQuestion(
                    quiz_id=quiz_id,
                    text=q_data["text"],
                    options=q_data["options"],
                    correct_answer=q_data["correctAnswer"],
                    explanation=q_data.get("explanation"),
                    order_index=index
                )
                db.add(db_question)

        db.commit()

        # Return updated quiz
        return await get_quiz(quiz_id, db)

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error updating quiz: {str(e)}")

@app.delete("/api/quizzes/{quiz_id}")
async def delete_quiz(quiz_id: str, db: Session = Depends(get_db)):
    db_quiz = db.query(DBQuiz).filter(DBQuiz.id == quiz_id).first()
    if not db_quiz:
        raise HTTPException(status_code=404, detail="Quiz not found")

    try:
        db.delete(db_quiz)
        db.commit()
        return {"message": "Quiz deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error deleting quiz: {str(e)}")

# Video generation endpoints
@app.post("/api/videos/generate", response_model=VideoGenerationJob)
async def generate_video(
    background_tasks: BackgroundTasks,
    request_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    # Handle both quiz_id and quizId formats
    quiz_id = request_data.get("quiz_id") or request_data.get("quizId")
    if not quiz_id:
        raise HTTPException(status_code=400, detail="Quiz ID is required")

    # Use default config if not provided
    config_data = request_data.get("config", {})

    # Check if quiz exists
    db_quiz = db.query(DBQuiz).filter(DBQuiz.id == quiz_id).first()
    if not db_quiz:
        raise HTTPException(status_code=404, detail="Quiz not found")

    try:
        # Create video job
        config = VideoGenerationConfig(**config_data)
        db_job = DBVideoJob(
            quiz_id=quiz_id,
            config=config_data,
            status="pending",
            started_at=datetime.utcnow()
        )
        db.add(db_job)
        db.commit()

        # Start video generation in background
        background_tasks.add_task(process_video_generation, db_job.id)

        job = VideoGenerationJob(
            id=db_job.id,
            quizId=db_job.quiz_id,
            config=config,
            status=db_job.status,
            progress=db_job.progress,
            startedAt=db_job.started_at,
            completedAt=db_job.completed_at,
            outputPath=db_job.output_path,
            error=db_job.error_message,
            logs=db_job.logs or []
        )

        return job

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error creating video job: {str(e)}")

async def generate_video_with_quiz_data(quiz_data, config, job_id):
    """
    Generate video using the sophisticated Video Generator V2.0.0.py script
    """
    try:
        print(f"🎬 Starting sophisticated video generation for job {job_id}")

        # Prepare questions and answers from quiz data
        questions = []
        correct_answers = []
        answer_options = []

        for question in quiz_data.questions:
            # Format question text properly
            question_text = question.text
            questions.append(question_text)
            correct_answers.append(question.correct_answer)

            # Format options as expected by the video generator
            options = question.options if hasattr(question, 'options') else []
            if not options and hasattr(question, 'correct_answer'):
                # If no options provided, create simple A/B/C format
                options = [f"A) {question.correct_answer}", "B) Option B", "C) Option C"]
            answer_options.append(options)

        # Limit to 6 questions as per the original script
        questions = questions[:6]
        correct_answers = correct_answers[:6]
        answer_options = answer_options[:6]

        # Set up configuration
        account_name = config.get("accountName", "Default Account")
        display_name = config.get("displayName", "@DEFAULT")
        color_mode = config.get("colorMode", "mode1")
        output_folder = config.get("outputFolder", "./output")
        font_type = config.get("fontType", "./assets/Fonts/NotoSansArabic-Bold.ttf")
        background_folder = config.get("backgroundFolder", "./assets/Back Videos")

        # Create output directory if it doesn't exist
        os.makedirs(output_folder, exist_ok=True)

        # Generate output filename using the same format as stored in database
        output_filename = f"quiz_{quiz_data.id}_{job_id}.mp4"
        output_path = os.path.join(output_folder, output_filename)

        print(f"   Questions: {len(questions)}")
        print(f"   Output: {output_path}")
        print(f"   Account: {account_name} ({display_name})")
        print(f"   Background folder: {background_folder}")
        print(f"   Font: {font_type}")

        # Check if required assets exist
        if not os.path.exists(background_folder):
            print(f"⚠️ Background folder not found: {background_folder}")
            print("   Creating simple video with FFmpeg fallback...")
            return create_simple_fallback_video(output_path, questions, account_name)

        if not os.path.exists(font_type):
            print(f"⚠️ Font file not found: {font_type}")
            print("   Creating simple video with FFmpeg fallback...")
            return create_simple_fallback_video(output_path, questions, account_name)

        # Try to use the OOP Video Generator V2.0.0 (exact replica)
        print("   Attempting OOP Video Generator V2.0.0 (EXACT replica)...")
        try:
            return await create_oop_video_generator_exact(
                output_path, questions, correct_answers, answer_options,
                account_name, display_name, background_folder, font_type, color_mode, job_id, quiz_data
            )
        except Exception as e:
            print(f"   OOP Video Generator failed: {str(e)}")
            print("   Falling back to real video generator integration...")
            try:
                return create_real_video_generator_integration(
                    output_path, questions, correct_answers, answer_options,
                    account_name, display_name, background_folder, font_type, color_mode, job_id, quiz_data
                )
            except Exception as e2:
                print(f"   Real Video Generator integration failed: {str(e2)}")
                print("   Falling back to authentic video generation...")
                try:
                    return create_authentic_quiz_video(
                        output_path, questions, correct_answers, answer_options,
                        account_name, display_name, background_folder, font_type, color_mode, job_id
                    )
                except Exception as e3:
                    print(f"   Authentic generation also failed: {str(e3)}")
                    print("   Falling back to sophisticated video generation...")
                    try:
                        return create_sophisticated_quiz_video(
                            output_path, questions, correct_answers, answer_options,
                            account_name, display_name, background_folder, font_type, color_mode
                        )
                    except Exception as e4:
                        print(f"   Sophisticated generation also failed: {str(e4)}")
                        print("   Falling back to enhanced video generation...")
                        return create_enhanced_fallback_video(output_path, questions, correct_answers, account_name, display_name)

    except Exception as e:
        print(f"❌ Error in video generation: {str(e)}")
        raise e

def create_simple_fallback_video(output_path, questions, account_name):
    """Create a simple fallback video using FFmpeg"""
    import subprocess

    # Create a simple test video using FFmpeg
    test_video_command = [
        'ffmpeg', '-y',
        '-f', 'lavfi',
        '-i', f'color=c=blue:size=1920x1080:duration=60',
        '-vf', f'drawtext=text="Quiz Video - {account_name}":fontcolor=white:fontsize=48:x=(w-text_w)/2:y=(h-text_h)/2',
        '-c:v', 'libx264',
        '-t', '60',
        output_path
    ]

    result = subprocess.run(test_video_command, capture_output=True, text=True)

    if result.returncode == 0:
        print(f"✅ Simple video generated successfully: {output_path}")
        return output_path
    else:
        print(f"❌ FFmpeg error: {result.stderr}")
        raise Exception(f"Video generation failed: {result.stderr}")

async def create_oop_video_generator_exact(output_path, questions, correct_answers, answer_options,
                                   account_name, display_name, background_folder, font_type, color_mode, job_id, quiz_data):
    """Create video using the ORIGINAL Video Generator V2.0.0.py - ALWAYS FRESH GENERATION"""

    print(f"🎬 FRESH GENERATION: Running original Video Generator V2.0.0.py for job {job_id}")
    print(f"   ⚠️  NO SMART DETECTION - ALWAYS GENERATING FRESH VIDEO")

    try:
        import tempfile
        import csv
        import subprocess
        import shutil
        import glob
        import time

        # ALWAYS run the original script fresh - NO REUSE OF OLD VIDEOS
        print(f"   🔄 MANDATORY FRESH EXECUTION: Running original script...")

        # Create temporary CSV file with our quiz data
        temp_csv_path = f"temp_quiz_{job_id}.csv"

        print(f"   📁 Creating temporary CSV: {temp_csv_path}")

        # Create CSV file in the exact format expected by the original script
        with open(temp_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            # Write header exactly like C_Arabic 3.csv
            writer.writerow(['Question', 'Option A', 'Option B', 'Option C', 'Correct Answer'])

            # Write quiz data - ensure we have 6 questions by repeating if necessary
            for i in range(6):
                if i < len(questions):
                    question = questions[i]
                    options = answer_options[i] if i < len(answer_options) and isinstance(answer_options[i], list) else ['A) البقرة', 'B) الحمار', 'C) القط']
                    correct = correct_answers[i] if i < len(correct_answers) else options[0]
                else:
                    # Repeat questions if we don't have enough
                    question_idx = i % len(questions) if questions else 0
                    question = questions[question_idx] if questions else f"ما هو الحيوان المفضل لديك؟"
                    options = answer_options[question_idx] if question_idx < len(answer_options) and isinstance(answer_options[question_idx], list) else ['A) البقرة', 'B) الحمار', 'C) القط']
                    correct = correct_answers[question_idx] if question_idx < len(correct_answers) else options[0]

                # Ensure we have 3 options
                while len(options) < 3:
                    options.append(f"C) الخيار {len(options) + 1}")

                writer.writerow([
                    question,
                    options[0] if len(options) > 0 else "A) البقرة",
                    options[1] if len(options) > 1 else "B) الحمار",
                    options[2] if len(options) > 2 else "C) القط",
                    correct
                ])

        print(f"   ✅ Created CSV file with 6 questions: {temp_csv_path}")

        # Temporarily modify the original script's CSV path
        original_csv_path = "./assets/C_Arabic 3.csv"
        backup_csv_path = "./assets/C_Arabic 3.csv.backup"

        # Backup original CSV and replace with our data
        if os.path.exists(original_csv_path):
            shutil.copy2(original_csv_path, backup_csv_path)
        shutil.copy2(temp_csv_path, original_csv_path)

        print(f"   🔄 Replaced original CSV with our quiz data")

        # Record start time to identify our generated video
        start_time = time.time()
        print(f"   ⏰ Recording start time: {start_time}")

        # Run the original script using asyncio subprocess (non-blocking)
        print(f"   🚀 EXECUTING ORIGINAL SCRIPT: Video Generator V2.0.0.py")

        # Use asyncio subprocess to avoid blocking the server
        process = await asyncio.create_subprocess_exec(
            'python3', 'Video Generator V2.0.0.py',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd='/Users/<USER>/Projects/Xvion'
        )

        print(f"   📋 Original script started with PID: {process.pid}")

        # Wait for the process to complete (with generous timeout for full processing)
        try:
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=600)  # 10 minutes

            if process.returncode == 0:
                print(f"   ✅ Original script completed successfully!")

                # Find videos generated AFTER our start time (to ensure we get the fresh one)
                video_files = glob.glob("./output/final_quiz_video_*.mp4")
                fresh_videos = []

                for video_file in video_files:
                    file_time = os.path.getmtime(video_file)
                    if file_time > start_time:  # Only videos created after we started
                        fresh_videos.append((video_file, file_time))
                        print(f"   📹 Found fresh video: {video_file} (created {file_time - start_time:.1f}s after start)")

                if fresh_videos:
                    # Get the newest fresh video
                    latest_file = max(fresh_videos, key=lambda x: x[1])[0]

                    # Verify it's a quality video (66 seconds, ~16MB)
                    try:
                        result = subprocess.run([
                            'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                            '-of', 'default=noprint_wrappers=1:nokey=1', latest_file
                        ], capture_output=True, text=True, timeout=10)

                        if result.returncode == 0:
                            duration = float(result.stdout.strip())
                            file_size = os.path.getsize(latest_file)

                            print(f"   📊 FRESH Generated video: {duration:.1f}s, {file_size/1024/1024:.1f}MB")

                            # Accept any video from the original script (it knows best)
                            if duration > 30:  # At least 30 seconds
                                print(f"   ✅ COPYING FRESH VIDEO: {latest_file} -> {output_path}")
                                shutil.copy2(latest_file, output_path)
                                return output_path
                            else:
                                print(f"   ❌ Generated video too short: {duration:.1f}s")

                    except Exception as e:
                        print(f"   ❌ Error checking generated video: {e}")

                print(f"   ❌ No fresh video generated by original script")
                raise Exception("Original script did not generate fresh video")
            else:
                print(f"   ❌ Original script failed with return code: {process.returncode}")
                if stderr:
                    error_msg = stderr.decode()
                    print(f"   Error output: {error_msg[:500]}...")  # First 500 chars
                raise Exception(f"Original script failed: {error_msg[:200] if stderr else 'Unknown error'}")

        except asyncio.TimeoutError:
            print(f"   ⏰ Original script timeout (10 minutes), terminating...")
            process.terminate()
            try:
                await asyncio.wait_for(process.wait(), timeout=10)
            except asyncio.TimeoutError:
                print(f"   🔪 Force killing original script process...")
                process.kill()
            raise Exception("Original script timeout after 10 minutes")

    except Exception as e:
        print(f"   ❌ Error in fresh original script execution: {e}")
        raise Exception(f"Fresh Original Video Generator approach failed: {e}")

    finally:
        # Restore original CSV file
        try:
            if 'backup_csv_path' in locals() and os.path.exists(backup_csv_path):
                shutil.move(backup_csv_path, original_csv_path)
                print(f"   🔄 Restored original CSV file")
        except:
            pass

        # Clean up temporary CSV
        try:
            if 'temp_csv_path' in locals() and os.path.exists(temp_csv_path):
                os.remove(temp_csv_path)
        except:
            pass




def call_original_video_generation(original_module, account_config, output_path, temp_dir):
    """Call the original video generation functions"""
    import shutil

    try:
        # Get the main functions from the original module
        create_final_video_with_dynamic_positioning = getattr(original_module, 'create_final_video_with_dynamic_positioning', None)
        get_random_background_video = getattr(original_module, 'get_random_background_video', None)

        if not create_final_video_with_dynamic_positioning:
            raise Exception("Could not find create_final_video_with_dynamic_positioning function")

        if not get_random_background_video:
            raise Exception("Could not find get_random_background_video function")

        # Load questions from CSV
        questions = []
        correct_answers = []
        answer_options = []

        import csv
        with open(account_config["csv_file"], 'r', encoding='utf-8') as file:
            csv_reader = csv.reader(file)
            header = next(csv_reader)  # Skip header
            for row in csv_reader:
                if len(row) >= 5:
                    questions.append(row[0])
                    answer_options.append([row[1], row[2], row[3]])
                    correct_answers.append(row[4])

        print(f"   Loaded {len(questions)} questions from CSV")

        # Get random background video
        background_video = get_random_background_video(account_config["background_folder"])
        print(f"   Selected background: {background_video}")

        # Create all the required parameters for the original function
        text_box_path = account_config["text_box"]
        photo_x = account_config["photo_x"]
        photo_y = account_config["photo_y"]
        photo_width = account_config["photo_width"]
        photo_height = account_config["photo_height"]
        output_folder = account_config["output_folder"]
        font_type = account_config["font_type"]

        # Create placeholder arrays (the original function expects these)
        all_question_photos = [[] for _ in questions]  # Empty photo arrays
        all_emoji_paths = [[] for _ in questions]  # Empty emoji arrays
        all_text_box_paths = [text_box_path for _ in questions]
        all_green_text_box_paths = [text_box_path for _ in questions]
        all_question_text_box_paths = [text_box_path for _ in questions]

        account_display_name = account_config["display_name"]
        account_index = 1
        color_mode = account_config["color_mode"]

        print(f"   🎬 Calling create_final_video_with_dynamic_positioning...")

        # Call the original function
        result_path = create_final_video_with_dynamic_positioning(
            output_path, questions, correct_answers, account_config["name"],
            text_box_path, photo_x, photo_y, photo_width, photo_height,
            output_folder, font_type, all_question_photos, all_emoji_paths,
            all_text_box_paths, all_green_text_box_paths, all_question_text_box_paths,
            account_display_name, account_index, color_mode
        )

        return result_path

    finally:
        # Clean up temporary directory
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def create_real_video_generator_integration(output_path, questions, correct_answers, answer_options,
                                          account_name, display_name, background_folder, font_type, color_mode, job_id, quiz_data):
    """Create a video using the REAL Video Generator V2.0.0.py script approach"""
    import subprocess
    import tempfile
    import csv
    import shutil

    print(f"🎬 Creating video using REAL Video Generator V2.0.0.py approach...")
    print(f"   Questions: {len(questions)}")
    print(f"   Account: {account_name} ({display_name})")

    # Create temporary CSV file with quiz data (like the original script expects)
    temp_dir = tempfile.mkdtemp(prefix=f"real_video_gen_{job_id}_")
    csv_file_path = os.path.join(temp_dir, "quiz_data.csv")

    try:
        # Create CSV file in the format expected by Video Generator V2.0.0.py
        with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            # Write header
            writer.writerow(['Question', 'Option A', 'Option B', 'Option C', 'Correct Answer'])

            # Write quiz data
            for i, question in enumerate(questions):
                if i < len(correct_answers) and i < len(answer_options):
                    options = answer_options[i] if isinstance(answer_options[i], list) else ['A) Option 1', 'B) Option 2', 'C) Option 3']
                    # Ensure we have 3 options
                    while len(options) < 3:
                        options.append(f"Option {len(options) + 1}")

                    writer.writerow([
                        question,
                        options[0] if len(options) > 0 else "Option A",
                        options[1] if len(options) > 1 else "Option B",
                        options[2] if len(options) > 2 else "Option C",
                        correct_answers[i] if i < len(correct_answers) else options[0]
                    ])

        print(f"   ✅ Created CSV file: {csv_file_path}")

        # Create account configuration like the original script
        account_config = {
            "account_name": account_name,
            "account_display_name": display_name,
            "background_folder": background_folder,
            "font_type": font_type,
            "color_mode": color_mode,
            "output_folder": os.path.dirname(output_path),
            "photo_x": 360,  # Default positioning from original
            "photo_y": 500,
            "photo_width": 1200,
            "photo_height": 400,
            "text_box_path": "./assets/Text Boxes/1.png"  # Default text box
        }

        # Call the core video generation function from Video Generator V2.0.0.py
        return create_video_using_original_approach(
            csv_file_path, account_config, output_path, temp_dir
        )

    finally:
        # Clean up temporary directory
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def create_video_using_original_approach(csv_file_path, account_config, output_path, temp_dir):
    """Create video using the original Video Generator V2.0.0.py approach"""
    import subprocess
    import random
    import csv

    print(f"   🎬 Using original Video Generator approach...")

    # Get random background video (like original)
    background_folder = account_config["background_folder"]
    background_files = [f for f in os.listdir(background_folder) if f.startswith('A (') and f.endswith('.mp4')]
    if not background_files:
        raise Exception(f"No background videos found in {background_folder}")

    background_video = os.path.join(background_folder, random.choice(background_files))
    print(f"   Selected background: {background_video}")

    # Read questions from CSV (like original)
    questions = []
    correct_answers = []
    answer_options = []

    with open(csv_file_path, 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        header = next(csv_reader)  # Skip header
        for row in csv_reader:
            if len(row) >= 5:
                questions.append(row[0])
                answer_options.append([row[1], row[2], row[3]])
                correct_answers.append(row[4])

    print(f"   Loaded {len(questions)} questions from CSV")

    # Create stable background video (like original)
    stable_bg_path = os.path.join(temp_dir, "background_stable.mp4")
    create_stable_background_like_original(background_video, stable_bg_path)

    # Create the final video with sophisticated overlays (like original)
    return create_final_video_like_original(
        stable_bg_path, questions, correct_answers, answer_options,
        account_config, output_path, temp_dir
    )

def create_stable_background_like_original(input_video, output_path):
    """Create stable background video exactly like the original script"""
    import subprocess

    # Use the same FFmpeg approach as the original
    command = [
        'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
        '-stream_loop', '-1',  # Loop the video
        '-i', input_video,
        '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-t', '66',  # 66 seconds like original (6 questions * 11 seconds)
        '-r', '30',  # 30 FPS
        output_path
    ]

    result = subprocess.run(command, capture_output=True, text=True)
    if result.returncode != 0:
        raise Exception(f"Failed to create stable background: {result.stderr}")

    print(f"   ✅ Created stable background (66s): {output_path}")

def create_final_video_like_original(stable_bg_path, questions, correct_answers, answer_options,
                                   account_config, output_path, temp_dir):
    """Create final video with overlays like the original script"""
    import subprocess

    print(f"   🎬 Creating final video with {len(questions)} questions...")

    # Use the first question for the video (like our current approach, but with original styling)
    question_text = questions[0] if questions else "Sample Question"
    answer_text = correct_answers[0] if correct_answers else "Sample Answer"
    options = answer_options[0] if answer_options else ["A) Option 1", "B) Option 2", "C) Option 3"]

    # Clean text for Arabic support
    question_text = question_text.replace('\n', ' ')

    # Escape text for FFmpeg (like original)
    safe_question = question_text.replace("'", "\\'").replace(":", "\\:").replace(",", "\\,")
    safe_answer = answer_text.replace("'", "\\'").replace(":", "\\:").replace(",", "\\,")
    safe_display = account_config["account_display_name"].replace("'", "\\'").replace(":", "\\:").replace(",", "\\,")

    # Create sophisticated filter complex (inspired by original but simplified)
    filter_complex = (
        f"[0:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1[bg];"
        f"[bg]drawtext=text='{safe_display}':fontcolor=white:fontsize=48:x=(w-text_w)/2:y=80:"
        f"box=1:boxcolor=black@0.8:boxborderw=10[account];"
        f"[account]drawtext=text='{safe_question}':fontcolor=yellow:fontsize=36:x=(w-text_w)/2:y=(h-text_h)/2-100:"
        f"box=1:boxcolor=black@0.9:boxborderw=8[question];"
        f"[question]drawtext=text='الإجابة\\: {safe_answer}':fontcolor=lime:fontsize=32:x=(w-text_w)/2:y=(h-text_h)/2+100:"
        f"box=1:boxcolor=green@0.8:boxborderw=8:enable='gte(t,8.5)'[final]"
    )

    # Build command like original (but simplified)
    command = [
        'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
        '-i', stable_bg_path,
        '-filter_complex', filter_complex,
        '-map', '[final]',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-t', '11',  # 11 seconds per question like original
        '-r', '30',
        output_path
    ]

    print(f"   🎬 Running final video composition...")
    result = subprocess.run(command, capture_output=True, text=True)

    if result.returncode != 0:
        raise Exception(f"Failed to create final video: {result.stderr}")

    print(f"   ✅ Final video created successfully: {output_path}")
    return output_path

def create_authentic_quiz_video(output_path, questions, correct_answers, answer_options,
                              account_name, display_name, background_folder, font_type, color_mode, job_id):
    """Create an authentic quiz video using the Video Generator V2.0.0.py approach"""
    import subprocess
    import random
    import tempfile
    import shutil
    from PIL import Image, ImageDraw, ImageFont

    print(f"🎬 Creating AUTHENTIC quiz video using Video Generator V2.0.0 approach...")

    # Constants from original script
    QUESTION_DURATION = 11  # Each question gets 11 seconds
    ANSWER_DISPLAY_TIME = 2.5  # Last 3 seconds show answer
    LAST_ANSWER_TIME = 8.5  # Answer appears at 8 seconds

    # Get a random background video
    background_files = [f for f in os.listdir(background_folder) if f.startswith('A (') and f.endswith('.mp4')]
    if not background_files:
        raise Exception(f"No background videos found in {background_folder}")

    background_video = os.path.join(background_folder, random.choice(background_files))
    print(f"   Selected background: {background_video}")

    # Create temporary directory for processing
    temp_dir = tempfile.mkdtemp(prefix=f"quiz_video_{job_id}_")
    print(f"   Working directory: {temp_dir}")

    try:
        # Step 1: Create stable background video (like original)
        stable_bg_path = os.path.join(temp_dir, "background_stable.mp4")
        create_stable_background_video_authentic(background_video, stable_bg_path)

        # Step 2: Create text overlays for questions and answers
        question_text = questions[0] if questions else "Sample Quiz Question"
        answer_text = correct_answers[0] if correct_answers else "Sample Answer"

        # Step 3: Create account name text box (like original)
        account_text_box_path = create_account_name_text_box_authentic(display_name, temp_dir, font_type)

        # Step 4: Create question text box
        question_text_box_path = create_question_text_box_authentic(question_text, temp_dir, font_type)

        # Step 5: Create answer text box
        answer_text_box_path = create_answer_text_box_authentic(answer_text, temp_dir, font_type)

        # Step 6: Compose final video with all overlays (like original)
        compose_final_authentic_video(
            stable_bg_path, account_text_box_path, question_text_box_path,
            answer_text_box_path, output_path, QUESTION_DURATION, LAST_ANSWER_TIME
        )

        print(f"✅ Authentic video generated successfully: {output_path}")
        return output_path

    finally:
        # Clean up temporary directory
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def create_sophisticated_quiz_video(output_path, questions, correct_answers, answer_options,
                                  account_name, display_name, background_folder, font_type, color_mode):
    """Create a sophisticated quiz video using background videos and proper overlays"""
    import subprocess
    import random

    print(f"🎬 Creating sophisticated quiz video...")
    print(f"   Background folder: {background_folder}")
    print(f"   Font: {font_type}")

    # Get a random background video
    background_files = [f for f in os.listdir(background_folder) if f.startswith('A (') and f.endswith('.mp4')]
    if not background_files:
        raise Exception(f"No background videos found in {background_folder}")

    background_video = os.path.join(background_folder, random.choice(background_files))
    print(f"   Selected background: {background_video}")

    # Prepare the first question and answer for the video
    question_text = questions[0] if questions else "Sample Quiz Question"
    answer_text = correct_answers[0] if correct_answers else "Sample Answer"

    # Clean and prepare text for Arabic support
    question_text = question_text.replace('\n', ' ')

    # Escape text for FFmpeg
    safe_question = question_text.replace("'", "\\'").replace(":", "\\:").replace(",", "\\,")
    safe_answer = answer_text.replace("'", "\\'").replace(":", "\\:").replace(",", "\\,")
    safe_display = display_name.replace("'", "\\'").replace(":", "\\:").replace(",", "\\,")

    # Create sophisticated video with background and overlays
    filter_complex = (
        f"[0:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1[bg];"
        f"[bg]drawtext=text='{safe_display}':fontcolor=white:fontsize=48:x=(w-text_w)/2:y=80:box=1:boxcolor=black@0.7:boxborderw=10[title];"
        f"[title]drawtext=text='{safe_question}':fontcolor=yellow:fontsize=36:x=(w-text_w)/2:y=(h-text_h)/2-100:box=1:boxcolor=black@0.8:boxborderw=8[question];"
        f"[question]drawtext=text='Answer\\: {safe_answer}':fontcolor=lime:fontsize=32:x=(w-text_w)/2:y=(h-text_h)/2+100:box=1:boxcolor=black@0.8:boxborderw=8:enable='gte(t,8)'[final]"
    )

    sophisticated_video_command = [
        'ffmpeg', '-y',
        '-i', background_video,
        '-filter_complex', filter_complex,
        '-map', '[final]',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-t', '60',
        '-r', '30',
        output_path
    ]

    print(f"   Running FFmpeg command...")
    result = subprocess.run(sophisticated_video_command, capture_output=True, text=True)

    if result.returncode == 0:
        print(f"✅ Sophisticated video generated successfully: {output_path}")
        return output_path
    else:
        print(f"❌ Sophisticated video generation failed: {result.stderr}")
        raise Exception(f"Sophisticated video generation failed: {result.stderr}")

def create_enhanced_fallback_video(output_path, questions, correct_answers, account_name, display_name):
    """Create an enhanced fallback video with question text"""
    import subprocess

    # Create a more sophisticated video with question text
    question_text = questions[0] if questions else "Sample Quiz Question"
    answer_text = correct_answers[0] if correct_answers else "Sample Answer"

    # Escape text for FFmpeg
    safe_question = question_text.replace("'", "\\'").replace(":", "\\:")
    safe_answer = answer_text.replace("'", "\\'").replace(":", "\\:")
    safe_account = account_name.replace("'", "\\'").replace(":", "\\:")
    safe_display = display_name.replace("'", "\\'").replace(":", "\\:")

    # Create video with multiple text overlays
    filter_complex = (
        f"color=c=0x1a1a2e:size=1920x1080:duration=60[bg];"
        f"[bg]drawtext=text='{safe_display}':fontcolor=white:fontsize=36:x=(w-text_w)/2:y=50[title];"
        f"[title]drawtext=text='Question\\: {safe_question}':fontcolor=yellow:fontsize=32:x=(w-text_w)/2:y=400:fontfile=/System/Library/Fonts/Arial.ttf[question];"
        f"[question]drawtext=text='Answer\\: {safe_answer}':fontcolor=green:fontsize=28:x=(w-text_w)/2:y=600:fontfile=/System/Library/Fonts/Arial.ttf[final]"
    )

    enhanced_video_command = [
        'ffmpeg', '-y',
        '-f', 'lavfi',
        '-i', 'color=c=0x1a1a2e:size=1920x1080:duration=60',
        '-filter_complex', filter_complex,
        '-map', '[final]',
        '-c:v', 'libx264',
        '-t', '60',
        output_path
    ]

    result = subprocess.run(enhanced_video_command, capture_output=True, text=True)

    if result.returncode == 0:
        print(f"✅ Enhanced video generated successfully: {output_path}")
        return output_path
    else:
        print(f"❌ Enhanced video generation failed, falling back to simple version")
        return create_simple_fallback_video(output_path, questions, account_name)

def create_stable_background_video_authentic(input_video_path, output_path):
    """Create a stable background video like the original script"""
    import subprocess

    # Use FFmpeg to create a stable background video
    command = [
        'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
        '-i', input_video_path,
        '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-t', '60',  # 60 seconds duration
        output_path
    ]

    result = subprocess.run(command, capture_output=True, text=True)
    if result.returncode != 0:
        raise Exception(f"Failed to create stable background: {result.stderr}")

    print(f"   ✅ Created stable background: {output_path}")

def create_account_name_text_box_authentic(display_name, temp_dir, font_type):
    """Create account name text box like the original script"""
    try:
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        # Fallback to simple text overlay
        return None

    # Create a text box image
    width, height = 800, 100
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    try:
        font = ImageFont.truetype(font_type, 36)
    except:
        font = ImageFont.load_default()

    # Draw background box
    draw.rectangle([10, 10, width-10, height-10], fill=(0, 0, 0, 180), outline=(255, 255, 255, 255), width=2)

    # Draw text
    text_bbox = draw.textbbox((0, 0), display_name, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    text_x = (width - text_width) // 2
    text_y = (height - text_height) // 2

    draw.text((text_x, text_y), display_name, font=font, fill=(255, 255, 255, 255))

    # Save the image
    output_path = os.path.join(temp_dir, "account_name_box.png")
    img.save(output_path)
    print(f"   ✅ Created account name text box: {output_path}")
    return output_path

def create_question_text_box_authentic(question_text, temp_dir, font_type):
    """Create question text box like the original script"""
    try:
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        return None

    # Clean question text
    question_text = question_text.replace('\n', ' ')

    # Create a larger text box for questions
    width, height = 1200, 200
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    try:
        font = ImageFont.truetype(font_type, 32)
    except:
        font = ImageFont.load_default()

    # Draw background box
    draw.rectangle([10, 10, width-10, height-10], fill=(0, 0, 0, 200), outline=(255, 255, 0, 255), width=3)

    # Draw text (word wrap for long questions)
    words = question_text.split()
    lines = []
    current_line = []

    for word in words:
        test_line = ' '.join(current_line + [word])
        text_bbox = draw.textbbox((0, 0), test_line, font=font)
        if text_bbox[2] - text_bbox[0] <= width - 40:
            current_line.append(word)
        else:
            if current_line:
                lines.append(' '.join(current_line))
                current_line = [word]
            else:
                lines.append(word)

    if current_line:
        lines.append(' '.join(current_line))

    # Draw lines
    line_height = 40
    start_y = (height - len(lines) * line_height) // 2

    for i, line in enumerate(lines):
        text_bbox = draw.textbbox((0, 0), line, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_x = (width - text_width) // 2
        text_y = start_y + i * line_height

        draw.text((text_x, text_y), line, font=font, fill=(255, 255, 255, 255))

    # Save the image
    output_path = os.path.join(temp_dir, "question_box.png")
    img.save(output_path)
    print(f"   ✅ Created question text box: {output_path}")
    return output_path

def create_answer_text_box_authentic(answer_text, temp_dir, font_type):
    """Create answer text box like the original script"""
    try:
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        return None

    # Create answer text box
    width, height = 800, 120
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    try:
        font = ImageFont.truetype(font_type, 28)
    except:
        font = ImageFont.load_default()

    # Draw background box (green for answer)
    draw.rectangle([10, 10, width-10, height-10], fill=(0, 128, 0, 200), outline=(0, 255, 0, 255), width=3)

    # Draw text
    text_bbox = draw.textbbox((0, 0), answer_text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    text_x = (width - text_width) // 2
    text_y = (height - text_height) // 2

    draw.text((text_x, text_y), answer_text, font=font, fill=(255, 255, 255, 255))

    # Save the image
    output_path = os.path.join(temp_dir, "answer_box.png")
    img.save(output_path)
    print(f"   ✅ Created answer text box: {output_path}")
    return output_path

def compose_final_authentic_video(stable_bg_path, account_text_box_path, question_text_box_path,
                                answer_text_box_path, output_path, question_duration, answer_time):
    """Compose the final video with all overlays like the original script"""
    import subprocess

    # Build FFmpeg command with multiple overlays
    command = [
        'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
        '-i', stable_bg_path,  # Background video
    ]

    # Add text box inputs
    overlay_filters = []
    input_index = 1

    if account_text_box_path and os.path.exists(account_text_box_path):
        command.extend(['-i', account_text_box_path])
        overlay_filters.append(f"[0:v][{input_index}:v]overlay=(W-w)/2:50[v{input_index}]")
        input_index += 1

    if question_text_box_path and os.path.exists(question_text_box_path):
        command.extend(['-i', question_text_box_path])
        prev_output = f"v{input_index-1}" if overlay_filters else "0:v"
        overlay_filters.append(f"[{prev_output}][{input_index}:v]overlay=(W-w)/2:(H-h)/2-50[v{input_index}]")
        input_index += 1

    if answer_text_box_path and os.path.exists(answer_text_box_path):
        command.extend(['-i', answer_text_box_path])
        prev_output = f"v{input_index-1}" if overlay_filters else "0:v"
        overlay_filters.append(f"[{prev_output}][{input_index}:v]overlay=(W-w)/2:(H-h)/2+100:enable='gte(t,{answer_time})'[final]")

    # Add filter complex
    if overlay_filters:
        command.extend(['-filter_complex', ';'.join(overlay_filters)])
        command.extend(['-map', '[final]'])
    else:
        command.extend(['-map', '0:v'])

    # Add audio and output settings
    command.extend([
        '-map', '0:a',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-t', str(question_duration),
        output_path
    ])

    print(f"   🎬 Composing final video with {len(overlay_filters)} overlays...")
    result = subprocess.run(command, capture_output=True, text=True)

    if result.returncode != 0:
        raise Exception(f"Failed to compose final video: {result.stderr}")

    print(f"   ✅ Final video composed successfully")

async def process_video_generation(job_id: str):
    """Background task to process video generation"""
    try:
        # Get fresh database session for background task
        from database import SessionLocal
        db = SessionLocal()

        db_job = db.query(DBVideoJob).filter(DBVideoJob.id == job_id).first()
        if not db_job:
            return

        # Get quiz data
        db_quiz = db.query(DBQuiz).filter(DBQuiz.id == db_job.quiz_id).first()
        if not db_quiz:
            db_job.status = "failed"
            db_job.error_message = "Quiz not found"
            db.commit()
            return

        db_job.status = "processing"
        db_job.logs = db_job.logs or []
        db_job.logs.append(f"Started processing at {datetime.utcnow()}")
        db_job.progress = 10
        db.commit()

        # Generate the video
        try:
            db_job.logs.append("Preparing video generation...")
            db_job.progress = 20
            db.commit()

            output_path = await generate_video_with_quiz_data(db_quiz, db_job.config or {}, job_id)

            db_job.logs.append("Video generation completed successfully")
            db_job.progress = 100
            db_job.status = "completed"
            db_job.completed_at = datetime.utcnow()
            db_job.output_path = output_path
            db.commit()

        except Exception as video_error:
            db_job.logs.append(f"Video generation error: {str(video_error)}")
            db_job.status = "failed"
            db_job.error_message = str(video_error)
            db.commit()

    except Exception as e:
        if 'db_job' in locals():
            db_job.status = "failed"
            db_job.error_message = str(e)
            db_job.logs = db_job.logs or []
            db_job.logs.append(f"Error: {str(e)}")
            db.commit()
    finally:
        if 'db' in locals():
            db.close()

@app.get("/api/jobs", response_model=List[VideoGenerationJob])
async def get_video_jobs(db: Session = Depends(get_db)):
    db_jobs = db.query(DBVideoJob).all()
    jobs = []

    for db_job in db_jobs:
        # Handle config with proper defaults for missing fields
        try:
            if db_job.config:
                # Merge stored config with defaults for missing fields
                default_config = {
                    "accountName": "Default",
                    "displayName": "@DEFAULT",
                    "colorMode": "mode1",
                    "outputFolder": "./output",
                    "fontType": "./assets/Fonts/NotoSansArabic-Bold.ttf",
                    "backgroundFolder": "./assets/Back Videos",
                    "numberOfQuestions": 6,
                    "questionDuration": 10,
                    "totalDuration": 60
                }
                merged_config = {**default_config, **db_job.config}
                config = VideoGenerationConfig(**merged_config)
            else:
                config = VideoGenerationConfig(
                    accountName="Default",
                    displayName="@DEFAULT",
                    colorMode="mode1",
                    outputFolder="./output",
                    fontType="./assets/Fonts/NotoSansArabic-Bold.ttf",
                    backgroundFolder="./assets/Back Videos",
                    numberOfQuestions=6,
                    questionDuration=10,
                    totalDuration=60
                )
        except Exception as e:
            # If config parsing fails, use defaults
            print(f"Warning: Failed to parse config for job {db_job.id}: {e}")
            config = VideoGenerationConfig(
                accountName="Default",
                displayName="@DEFAULT",
                colorMode="mode1",
                outputFolder="./output",
                fontType="./assets/Fonts/NotoSansArabic-Bold.ttf",
                backgroundFolder="./assets/Back Videos",
                numberOfQuestions=6,
                questionDuration=10,
                totalDuration=60
            )

        job = VideoGenerationJob(
            id=db_job.id,
            quizId=db_job.quiz_id,
            config=config,
            status=db_job.status,
            progress=db_job.progress,
            startedAt=db_job.started_at,
            completedAt=db_job.completed_at,
            outputPath=db_job.output_path,
            error=db_job.error_message,
            logs=db_job.logs or []
        )
        jobs.append(job)

    return jobs

@app.get("/api/jobs/{job_id}", response_model=VideoGenerationJob)
async def get_video_job(job_id: str, db: Session = Depends(get_db)):
    db_job = db.query(DBVideoJob).filter(DBVideoJob.id == job_id).first()
    if not db_job:
        raise HTTPException(status_code=404, detail="Job not found")

    job = VideoGenerationJob(
        id=db_job.id,
        quizId=db_job.quiz_id,
        config=VideoGenerationConfig(**db_job.config) if db_job.config else VideoGenerationConfig(
            accountName="Default",
            displayName="@DEFAULT",
            colorMode="mode1",
            outputFolder="./output",
            fontType="./assets/Fonts/NotoSansArabic-Bold.ttf",
            backgroundFolder="./assets/Back Videos",
            numberOfQuestions=6,
            questionDuration=10,
            totalDuration=60
        ),
        status=db_job.status,
        progress=db_job.progress,
        startedAt=db_job.started_at,
        completedAt=db_job.completed_at,
        outputPath=db_job.output_path,
        error=db_job.error_message,
        logs=db_job.logs or []
    )

    return job

@app.post("/api/videos/jobs/{job_id}/cancel")
async def cancel_video_job(job_id: str, db: Session = Depends(get_db)):
    db_job = db.query(DBVideoJob).filter(DBVideoJob.id == job_id).first()
    if not db_job:
        raise HTTPException(status_code=404, detail="Job not found")

    if db_job.status in ["pending", "processing"]:
        db_job.status = "cancelled"
        db_job.logs = db_job.logs or []
        db_job.logs.append(f"Cancelled at {datetime.utcnow()}")
        db.commit()

    return {"message": "Job cancelled successfully"}

@app.get("/api/videos/jobs/{job_id}/download")
async def download_video(job_id: str, db: Session = Depends(get_db)):
    db_job = db.query(DBVideoJob).filter(DBVideoJob.id == job_id).first()
    if not db_job:
        raise HTTPException(status_code=404, detail="Job not found")

    if db_job.status != "completed" or not db_job.output_path:
        raise HTTPException(status_code=400, detail="Video not ready for download")

    if not os.path.exists(db_job.output_path):
        raise HTTPException(status_code=404, detail="Video file not found")

    return FileResponse(db_job.output_path, media_type="video/mp4")

@app.post("/api/videos/jobs/{job_id}/retry")
async def retry_video_job(job_id: str, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Retry a failed or stuck video generation job"""
    try:
        db_job = db.query(DBVideoJob).filter(DBVideoJob.id == job_id).first()
        if not db_job:
            raise HTTPException(status_code=404, detail="Job not found")

        if db_job.status == "completed":
            raise HTTPException(status_code=400, detail="Job is already completed")

        if db_job.status == "processing":
            raise HTTPException(status_code=400, detail="Job is currently processing")

        # Reset job status
        db_job.status = "pending"
        db_job.progress = 0
        db_job.error_message = None
        db_job.logs = []
        db_job.started_at = datetime.utcnow()
        db_job.completed_at = None
        db_job.output_path = None
        db.commit()

        # Start video generation in background
        background_tasks.add_task(process_video_generation, db_job.id)

        return {"message": "Job retry initiated", "job_id": job_id}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error retrying job: {str(e)}")

# Settings endpoints
@app.get("/settings")
async def get_settings():
    return {
        "apiKeys": {
            "deepl": "***" if DEEPL_AUTH_KEY else None,
            "elevenlabs": "***" if ELEVENLABS_API_KEY else None,
            "openai": "***" if OPENAI_API_KEY else None,
        },
        "videoSettings": {
            "defaultDuration": 60,
            "defaultQuestionCount": 6,
            "outputQuality": "high"
        }
    }

@app.put("/settings")
async def update_settings(settings_data: Dict[str, Any]):
    # In a real implementation, update the configuration
    return {"message": "Settings updated successfully"}

@app.post("/settings/validate-keys")
async def validate_api_keys(keys: APIKeysModel):
    results = {}
    
    # Validate each API key (simplified)
    if keys.deepl:
        results["deepl"] = True  # Implement actual validation
    if keys.elevenlabs:
        results["elevenlabs"] = True  # Implement actual validation
    
    return results

# Statistics endpoint
@app.get("/api/videos/stats")
async def get_video_stats(db: Session = Depends(get_db)):
    total_jobs = db.query(DBVideoJob).count()
    completed_jobs = db.query(DBVideoJob).filter(DBVideoJob.status == "completed").count()
    total_quizzes = db.query(DBQuiz).count()
    active_jobs = db.query(DBVideoJob).filter(DBVideoJob.status.in_(["pending", "processing"])).count()

    return {
        "totalVideos": completed_jobs,
        "totalQuizzes": total_quizzes,
        "activeJobs": active_jobs,
        "completedToday": completed_jobs,  # Simplified
        "successRate": (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0,
        "averageGenerationTime": 300,  # 5 minutes average
        "lastGenerated": datetime.now() if completed_jobs > 0 else None
    }

@app.on_event("startup")
async def startup_event():
    """Initialize database and load sample data on startup"""
    print("🔧 Initializing Xvion API Server...")

    # Test database connection
    if not test_connection():
        print("❌ Database connection failed! Please check PostgreSQL is running.")
        return

    # Create tables
    create_tables()

    # Initialize default data
    init_default_data()

    # Load sample data
    from database import SessionLocal
    db = SessionLocal()
    try:
        load_sample_data(db)
    finally:
        db.close()

    print("🚀 Xvion API Server started successfully!")
    print("📊 Dashboard: http://localhost:3000")
    print("📖 API Docs: http://localhost:8001/docs")
    print("🗄️ Database: PostgreSQL connected")

if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
