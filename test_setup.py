#!/usr/bin/env python3
"""
Test script to verify the Xvion Video Generator setup
"""

import sys
import os

def test_imports():
    """Test all required imports"""
    print("🔍 Testing imports...")
    try:
        import os
        import subprocess
        import json
        import shutil
        import random
        import string
        import cv2
        import csv
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        import matplotlib.pyplot as plt
        import inflect
        import re
        from langdetect import detect, DetectorFactory
        from multiprocessing import Pool, Semaphore, freeze_support
        from dotenv import load_dotenv
        from elevenlabs import ElevenLabs, VoiceSettings
        import soundfile as sf
        import requests
        import time
        import urllib.parse
        import hashlib
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        import deepl
        import arabic_reshaper
        import bidi.algorithm
        from bidi.algorithm import get_display
        print("✅ All imports successful!")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_assets():
    """Test if assets are properly linked"""
    print("\n🔍 Testing assets...")
    
    required_assets = [
        "assets/Fonts/NotoSansArabic-Bold.ttf",
        "assets/C_Arabic 3.csv",
        "assets/T1.png",
        "assets/Timer V4.mp4",
        "assets/Back Videos",
        "output"  # Output directory
    ]
    
    all_good = True
    for asset in required_assets:
        if os.path.exists(asset):
            print(f"✅ Found: {asset}")
        else:
            print(f"❌ Missing: {asset}")
            all_good = False
    
    return all_good

def test_script_loading():
    """Test if main script can be loaded"""
    print("\n🔍 Testing script loading...")
    try:
        # Test config import first
        from config import DEEPL_AUTH_KEY, ELEVENLABS_API_KEY, API_KEYS
        print("✅ Config imported successfully!")
        print(f"   - DEEPL_AUTH_KEY: {DEEPL_AUTH_KEY[:10]}...")
        print(f"   - ELEVENLABS_API_KEY: {ELEVENLABS_API_KEY[:10]}...")
        print(f"   - API_KEYS services: {len(API_KEYS)}")

        import quiz_positioning_config_arabic
        print("✅ quiz_positioning_config_arabic loaded successfully!")

        # Load main script functions without executing
        with open('Video Generator V2.0.0.py', 'r') as f:
            script_content = f.read()

        # Split at main execution to avoid running the full script
        script_functions = script_content.split('if __name__')[0]
        exec(script_functions)

        print("✅ Main script functions loaded successfully!")

        # Test Arabic font loading
        try:
            font = load_arabic_font(50)
            if font:
                print("✅ Arabic font loading works!")
            else:
                print("⚠️ Arabic font loading failed, using fallback")
        except NameError:
            print("⚠️ Arabic font function not in scope, but script loaded successfully")

        return True
    except Exception as e:
        print(f"❌ Script loading error: {e}")
        return False

def test_csv_data():
    """Test if CSV data can be read"""
    print("\n🔍 Testing CSV data...")
    try:
        import csv
        csv_file = "assets/C_Arabic 3.csv"
        if os.path.exists(csv_file):
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                rows = list(reader)
                print(f"✅ CSV file loaded with {len(rows)} rows")
                if len(rows) > 1:
                    print(f"✅ Sample row: {rows[1][:3]}...")  # Show first 3 columns
                return True
        else:
            print(f"❌ CSV file not found: {csv_file}")
            return False
    except Exception as e:
        print(f"❌ CSV reading error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Xvion Video Generator Setup Test\n")
    
    tests = [
        ("Imports", test_imports),
        ("Assets", test_assets),
        ("Script Loading", test_script_loading),
        ("CSV Data", test_csv_data)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if not result:
            all_passed = False
    
    print("="*50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! The project is ready to run.")
        print("\nNext steps:")
        print("1. Configure your API keys in config.py")
        print("2. Run: python3 'Video Generator V2.0.0.py'")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
