'use client'

import { VideoCameraIcon, PlayIcon, CogIcon } from '@heroicons/react/24/outline'

export function VideoGenerator() {
  return (
    <div className="animate-fade-in">
      {/* Header */}
      <div className="d-flex flex-column flex-sm-row align-items-start align-items-sm-center justify-content-between mb-4">
        <div className="mb-3 mb-sm-0">
          <h1 className="h2 fw-bold mb-2">Video Generator</h1>
          <p className="text-muted mb-0">Generate engaging Arabic quiz videos with AI</p>
        </div>
        <button className="btn btn-primary d-flex align-items-center gap-2">
          <PlayIcon style={{ width: '16px', height: '16px' }} />
          Generate Video
        </button>
      </div>

      {/* Content */}
      <div className="row g-4">
        <div className="col-lg-8">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-transparent border-bottom">
              <h5 className="card-title mb-0">Video Configuration</h5>
            </div>
            <div className="card-body p-4">
              <div className="text-center py-5">
                <VideoCameraIcon style={{ width: '64px', height: '64px' }} className="text-muted mb-3" />
                <h4 className="fw-bold mb-2">Video Generator</h4>
                <p className="text-muted mb-4">
                  This feature will allow you to generate Arabic quiz videos with custom templates, 
                  voiceovers, and dynamic content positioning.
                </p>
                <button 
                  className="btn btn-primary"
                  onClick={() => alert('Video generation functionality will be implemented here!')}
                >
                  Start Creating
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-4">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-transparent border-bottom">
              <h6 className="card-title mb-0">Quick Settings</h6>
            </div>
            <div className="card-body p-4">
              <div className="d-flex align-items-center gap-3 mb-3">
                <CogIcon style={{ width: '20px', height: '20px' }} className="text-muted" />
                <span className="fw-medium">Video Quality: HD</span>
              </div>
              <div className="d-flex align-items-center gap-3 mb-3">
                <CogIcon style={{ width: '20px', height: '20px' }} className="text-muted" />
                <span className="fw-medium">Language: Arabic</span>
              </div>
              <div className="d-flex align-items-center gap-3">
                <CogIcon style={{ width: '20px', height: '20px' }} className="text-muted" />
                <span className="fw-medium">Duration: Auto</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
