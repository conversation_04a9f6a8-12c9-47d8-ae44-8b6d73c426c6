'use client'

import { QueueListIcon, ClockIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'

export function VideoJobs() {
  const mockJobs = [
    {
      id: '1',
      title: 'Arabic Animals Quiz Video',
      status: 'completed',
      progress: 100,
      createdAt: '2 hours ago'
    },
    {
      id: '2',
      title: 'Islamic History Quiz Video',
      status: 'processing',
      progress: 65,
      createdAt: '30 minutes ago'
    },
    {
      id: '3',
      title: 'Geography Quiz Video',
      status: 'failed',
      progress: 0,
      createdAt: '1 hour ago'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon style={{ width: '20px', height: '20px' }} className="text-success" />
      case 'processing':
        return <ClockIcon style={{ width: '20px', height: '20px' }} className="text-warning" />
      case 'failed':
        return <ExclamationTriangleIcon style={{ width: '20px', height: '20px' }} className="text-danger" />
      default:
        return <ClockIcon style={{ width: '20px', height: '20px' }} className="text-muted" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <span className="badge bg-success">Completed</span>
      case 'processing':
        return <span className="badge bg-warning">Processing</span>
      case 'failed':
        return <span className="badge bg-danger">Failed</span>
      default:
        return <span className="badge bg-secondary">Unknown</span>
    }
  }

  return (
    <div className="animate-fade-in">
      {/* Header */}
      <div className="d-flex flex-column flex-sm-row align-items-start align-items-sm-center justify-content-between mb-4">
        <div className="mb-3 mb-sm-0">
          <h1 className="h2 fw-bold mb-2">Video Jobs</h1>
          <p className="text-muted mb-0">Monitor your video generation progress</p>
        </div>
        <button 
          className="btn btn-outline-primary"
          onClick={() => window.location.reload()}
        >
          Refresh
        </button>
      </div>

      {/* Jobs List */}
      <div className="card border-0 shadow-sm">
        <div className="card-header bg-transparent border-bottom">
          <h5 className="card-title mb-0">Recent Jobs</h5>
        </div>
        <div className="card-body p-0">
          {mockJobs.map((job, index) => (
            <div key={job.id} className={`d-flex align-items-center justify-content-between p-4 ${index < mockJobs.length - 1 ? 'border-bottom' : ''}`}>
              <div className="d-flex align-items-center gap-3">
                {getStatusIcon(job.status)}
                <div>
                  <h6 className="mb-1 fw-semibold">{job.title}</h6>
                  <small className="text-muted">Created {job.createdAt}</small>
                </div>
              </div>
              <div className="d-flex align-items-center gap-3">
                {job.status === 'processing' && (
                  <div className="d-flex align-items-center gap-2">
                    <div className="progress" style={{ width: '100px', height: '6px' }}>
                      <div 
                        className="progress-bar" 
                        role="progressbar" 
                        style={{ width: `${job.progress}%` }}
                      ></div>
                    </div>
                    <small className="text-muted">{job.progress}%</small>
                  </div>
                )}
                {getStatusBadge(job.status)}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Empty State */}
      {mockJobs.length === 0 && (
        <div className="card border-0 shadow-sm">
          <div className="card-body text-center py-5">
            <QueueListIcon style={{ width: '64px', height: '64px' }} className="text-muted mb-3" />
            <h4 className="fw-bold mb-2">No Jobs Yet</h4>
            <p className="text-muted mb-4">
              Start generating videos to see your jobs here.
            </p>
            <button 
              className="btn btn-primary"
              onClick={() => alert('Navigate to Video Generator to create your first video!')}
            >
              Generate First Video
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
