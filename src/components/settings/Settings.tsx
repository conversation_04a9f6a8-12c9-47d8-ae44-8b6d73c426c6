'use client'

import { Cog6ToothIcon, KeyIcon, UserIcon, BellIcon } from '@heroicons/react/24/outline'

export function Settings() {
  return (
    <div className="animate-fade-in">
      {/* Header */}
      <div className="mb-4">
        <h1 className="h2 fw-bold mb-2">Settings</h1>
        <p className="text-muted mb-0">Configure your application preferences</p>
      </div>

      <div className="row g-4">
        {/* API Keys */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-transparent border-bottom">
              <div className="d-flex align-items-center gap-2">
                <KeyIcon style={{ width: '20px', height: '20px' }} className="text-muted" />
                <h5 className="card-title mb-0">API Keys</h5>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="mb-3">
                <label className="form-label fw-semibold">ElevenLabs API Key</label>
                <input 
                  type="password" 
                  className="form-control" 
                  placeholder="Enter your ElevenLabs API key"
                  defaultValue="sk-..."
                />
              </div>
              <div className="mb-3">
                <label className="form-label fw-semibold">DeepL API Key</label>
                <input 
                  type="password" 
                  className="form-control" 
                  placeholder="Enter your DeepL API key"
                  defaultValue="..."
                />
              </div>
              <div className="mb-3">
                <label className="form-label fw-semibold">Unsplash API Key</label>
                <input 
                  type="password" 
                  className="form-control" 
                  placeholder="Enter your Unsplash API key"
                  defaultValue="..."
                />
              </div>
              <button className="btn btn-primary">Save API Keys</button>
            </div>
          </div>
        </div>

        {/* User Profile */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-transparent border-bottom">
              <div className="d-flex align-items-center gap-2">
                <UserIcon style={{ width: '20px', height: '20px' }} className="text-muted" />
                <h5 className="card-title mb-0">User Profile</h5>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="mb-3">
                <label className="form-label fw-semibold">Name</label>
                <input 
                  type="text" 
                  className="form-control" 
                  defaultValue="Admin User"
                />
              </div>
              <div className="mb-3">
                <label className="form-label fw-semibold">Email</label>
                <input 
                  type="email" 
                  className="form-control" 
                  defaultValue="<EMAIL>"
                />
              </div>
              <div className="mb-3">
                <label className="form-label fw-semibold">Language</label>
                <select className="form-select">
                  <option value="ar">Arabic</option>
                  <option value="en">English</option>
                </select>
              </div>
              <button className="btn btn-primary">Update Profile</button>
            </div>
          </div>
        </div>

        {/* Application Settings */}
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-transparent border-bottom">
              <div className="d-flex align-items-center gap-2">
                <Cog6ToothIcon style={{ width: '20px', height: '20px' }} className="text-muted" />
                <h5 className="card-title mb-0">Application Settings</h5>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="row g-4">
                <div className="col-md-6">
                  <label className="form-label fw-semibold">Default Video Quality</label>
                  <select className="form-select">
                    <option value="hd">HD (1280x720)</option>
                    <option value="fhd">Full HD (1920x1080)</option>
                    <option value="4k">4K (3840x2160)</option>
                  </select>
                </div>
                <div className="col-md-6">
                  <label className="form-label fw-semibold">Default Question Count</label>
                  <input 
                    type="number" 
                    className="form-control" 
                    defaultValue="10"
                    min="1"
                    max="50"
                  />
                </div>
                <div className="col-md-6">
                  <label className="form-label fw-semibold">Video Duration (seconds)</label>
                  <input 
                    type="number" 
                    className="form-control" 
                    defaultValue="30"
                    min="10"
                    max="300"
                  />
                </div>
                <div className="col-md-6">
                  <div className="form-check form-switch mt-4">
                    <input className="form-check-input" type="checkbox" id="notifications" defaultChecked />
                    <label className="form-check-label fw-semibold" htmlFor="notifications">
                      Enable Notifications
                    </label>
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <button 
                  className="btn btn-primary me-2"
                  onClick={() => alert('Settings saved successfully!')}
                >
                  Save Settings
                </button>
                <button className="btn btn-outline-secondary">Reset to Defaults</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
