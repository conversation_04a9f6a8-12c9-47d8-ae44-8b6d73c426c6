export interface Question {
  id: string
  text: string
  options: string[]
  correctAnswer: string
  explanation?: string
}

export interface Quiz {
  id: string
  title: string
  description: string
  questions: Question[]
  language: string
  createdAt: Date
  updatedAt: Date
}

export interface VideoJob {
  id: string
  quizId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  outputPath?: string
  errorMessage?: string
  createdAt: Date
  updatedAt: Date
}

export interface ApiKey {
  id: string
  serviceName: string
  apiKey: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface AppSettings {
  id: string
  key: string
  value: string
  description?: string
  createdAt: Date
  updatedAt: Date
}
