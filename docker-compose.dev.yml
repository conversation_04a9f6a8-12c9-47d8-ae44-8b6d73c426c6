# Development overrides for docker-compose
version: '3.8'

services:
  # Backend with development settings
  backend:
    build:
      target: development
    environment:
      - PYTHON_ENV=development
      - LOG_LEVEL=DEBUG
    volumes:
      - .:/app
      - /app/__pycache__
    ports:
      - "8001:8001"
    command: ["uvicorn", "api_server_production:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]

  # Frontend with development settings
  frontend:
    build:
      context: ./xvion-frontend
      dockerfile: Dockerfile.dev
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8001
    volumes:
      - ./xvion-frontend:/app
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    command: ["npm", "run", "dev"]

  # Remove nginx in development
  nginx:
    profiles:
      - production

  # Remove celery services in development
  celery-worker:
    profiles:
      - production

  celery-beat:
    profiles:
      - production
