# 🎬 Xvion - Arabic Quiz Video Generator

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Next.js](https://img.shields.io/badge/Next.js-15.5+-black.svg)](https://nextjs.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

**Xvion** is a comprehensive Arabic quiz video generator that creates engaging, professional-quality quiz videos with AI-powered features. The system combines a powerful Python backend with a modern Next.js frontend to provide a complete video generation solution.

## ✨ Features

### 🎥 Video Generation
- **Automated Video Creation**: Generate quiz videos from CSV data or dynamic quiz management
- **Arabic Text Support**: Full RTL text rendering with proper Arabic font handling
- **Multiple Visual Styles**: 4 different color modes and visual themes
- **Dynamic Positioning**: Smart text and element positioning system
- **Background Integration**: Automatic background video selection and processing

### 🧠 AI-Powered Content
- **Multi-API Photo Integration**: Unsplash, Pexels, and Pixabay for relevant images
- **Text-to-Speech**: ElevenLabs integration for natural Arabic voiceovers
- **Translation Support**: DeepL integration for multilingual content
- **Smart Content Processing**: Automatic text processing and optimization

### 🖥️ Modern Web Interface
- **React-based Dashboard**: Modern, responsive web interface
- **Real-time Monitoring**: Live progress tracking for video generation
- **Quiz Management**: Create, edit, and manage quizzes dynamically
- **Settings Configuration**: Easy API key and configuration management

### 🔧 Technical Features
- **FastAPI Backend**: High-performance API server with async support
- **Background Processing**: Non-blocking video generation with job queuing
- **File Management**: Organized asset structure and output handling
- **Error Handling**: Comprehensive error tracking and logging

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- Node.js 18 or higher
- FFmpeg installed on your system

### 1. Clone the Repository

```bash
git clone <repository-url>
cd xvion
```

### 2. Backend Setup

```bash
# Install Python dependencies
pip install -r requirements.txt

# Configure API keys
cp config.py.template config.py
# Edit config.py with your API keys
```

### 3. Frontend Setup

```bash
# Navigate to frontend directory
cd xvion-frontend

# Install dependencies
npm install

# Build the frontend
npm run build
```

### 4. Start the Services

```bash
# Terminal 1: Start the API server
python api_server.py

# Terminal 2: Start the frontend (in xvion-frontend directory)
npm run dev
```

### 5. Access the Application

- **Frontend Dashboard**: http://localhost:3000
- **API Documentation**: http://localhost:8001/docs
- **API Health Check**: http://localhost:8001/health

## 📁 Project Structure

```
xvion/
├── 📁 assets/                    # Static assets
│   ├── 📁 Back Videos/          # Background videos
│   ├── 📁 Fonts/                # Arabic fonts
│   └── 📁 C_Arabic 3.csv        # Sample quiz data
├── 📁 xvion-frontend/           # Next.js frontend
│   ├── 📁 src/
│   │   ├── 📁 app/              # Next.js app router
│   │   ├── 📁 components/       # React components
│   │   ├── 📁 lib/              # Utilities and API client
│   │   └── 📁 types/            # TypeScript definitions
│   └── 📄 package.json
├── 📄 Video Generator V2.0.0.py # Main video generator
├── 📄 api_server.py             # FastAPI backend
├── 📄 config.py                 # Configuration and API keys
├── 📄 quiz_positioning_config_arabic.py # Dynamic positioning
├── 📄 requirements.txt          # Python dependencies
└── 📄 README.md                 # This file
```

## 🗄️ Database Setup

The application uses PostgreSQL as the primary database.

### PostgreSQL Setup
1. **Install PostgreSQL 14:**
   ```bash
   brew install postgresql@14
   ```

2. **Start PostgreSQL service:**
   ```bash
   brew services start postgresql@14
   ```

3. **Create the database:**
   ```bash
   export PATH="/usr/local/opt/postgresql@14/bin:$PATH"
   createdb xvion_db
   ```

4. **Install Python dependencies:**
   ```bash
   pip install psycopg[binary] sqlalchemy alembic
   ```

5. **Initialize the database:**
   ```bash
   python database.py
   ```

### Quick Setup Script
Use the provided setup script for easy environment configuration:
```bash
source setup_postgres.sh
```

## 🔑 API Keys Configuration

Create a `config.py` file with your API keys:

```python
# Translation API
DEEPL_AUTH_KEY = "your-deepl-key"

# Text-to-Speech API
ELEVENLABS_API_KEY = "your-elevenlabs-key"

# Image APIs
UNSPLASH_ACCESS_KEYS = ["key1", "key2", "key3"]
PEXELS_API_KEY = "your-pexels-key"
PIXABAY_API_KEY = "your-pixabay-key"

# AI API (optional)
OPENAI_API_KEY = "your-openai-key"
```

## 🎯 Usage Guide

### Creating a Quiz

1. **Via Web Interface**:
   - Navigate to the "Quizzes" section
   - Click "Create Quiz"
   - Add questions with multiple choice options
   - Save the quiz

2. **Via CSV Import**:
   - Prepare a CSV file with questions and answers
   - Use the import feature in the web interface
   - Review and edit imported questions

### Generating Videos

1. **Select Quiz**: Choose from your created quizzes
2. **Configure Settings**:
   - Account name and display settings
   - Color mode selection
   - Video duration and question count
3. **Start Generation**: Click "Generate Video"
4. **Monitor Progress**: Track generation status in real-time
5. **Download**: Download completed videos

### Managing Settings

- **API Keys**: Configure all required API keys
- **Account Templates**: Set up different account profiles
- **Video Settings**: Adjust default video parameters
- **General Settings**: Configure application preferences

## 🛠️ Development

### Backend Development

```bash
# Run with auto-reload
uvicorn api_server:app --reload --host 0.0.0.0 --port 8000

# Run tests (when available)
pytest tests/

# Format code
black *.py
```

### Frontend Development

```bash
cd xvion-frontend

# Development server
npm run dev

# Type checking
npm run type-check

# Linting
npm run lint

# Build for production
npm run build
```

## 📊 API Endpoints

### Quiz Management
- `GET /quizzes` - List all quizzes
- `POST /quizzes` - Create new quiz
- `GET /quizzes/{id}` - Get specific quiz
- `PUT /quizzes/{id}` - Update quiz
- `DELETE /quizzes/{id}` - Delete quiz

### Video Generation
- `POST /videos/generate` - Start video generation
- `GET /videos/jobs` - List all jobs
- `GET /videos/jobs/{id}` - Get job status
- `POST /videos/jobs/{id}/cancel` - Cancel job
- `GET /videos/jobs/{id}/download` - Download video

### Settings & Configuration
- `GET /settings` - Get current settings
- `PUT /settings` - Update settings
- `POST /settings/validate-keys` - Validate API keys

## 🔧 Configuration Options

### Video Generation Settings
- **Duration**: Total video length (default: 60 seconds)
- **Questions**: Number of questions per video (default: 6)
- **Quality**: Output video quality (high/medium/low)
- **Color Modes**: 4 different visual themes

### Account Templates
- **Display Names**: Custom account names for videos
- **Color Schemes**: Associated color modes
- **Branding**: Custom styling options

## 🐛 Troubleshooting

### Common Issues

1. **FFmpeg Not Found**
   ```bash
   # Install FFmpeg
   # macOS: brew install ffmpeg
   # Ubuntu: sudo apt install ffmpeg
   # Windows: Download from https://ffmpeg.org/
   ```

2. **API Key Errors**
   - Verify all API keys are correctly configured
   - Check API key validity and quotas
   - Ensure proper permissions for each service

3. **Font Issues**
   - Ensure Arabic fonts are properly installed
   - Check font file paths in configuration
   - Verify font file permissions

4. **Build Errors**
   ```bash
   # Clear Next.js cache
   cd xvion-frontend
   rm -rf .next
   npm run build
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Arabic Text Processing**: arabic-reshaper and python-bidi libraries
- **UI Components**: Tailwind CSS and Headless UI
- **Icons**: Heroicons
- **APIs**: ElevenLabs, DeepL, Unsplash, Pexels, Pixabay

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

---

**Made with ❤️ for the Arabic content creation community**