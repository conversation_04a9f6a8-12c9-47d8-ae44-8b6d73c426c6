"""
Monitoring and Health Check System for Xvion
Provides comprehensive monitoring, metrics, and alerting
"""
import psutil
import time
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

import redis
from sqlalchemy import create_engine, text
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

from config_production import current_config as config
from logging_config import app_logger, perf_logger

# Prometheus metrics
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
ACTIVE_CONNECTIONS = Gauge('active_connections', 'Active database connections')
VIDEO_GENERATION_TIME = Histogram('video_generation_duration_seconds', 'Video generation time')
SYSTEM_CPU_USAGE = Gauge('system_cpu_usage_percent', 'System CPU usage percentage')
SYSTEM_MEMORY_USAGE = Gauge('system_memory_usage_bytes', 'System memory usage in bytes')
SYSTEM_DISK_USAGE = Gauge('system_disk_usage_bytes', 'System disk usage in bytes')

class HealthStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"

@dataclass
class HealthCheck:
    name: str
    status: HealthStatus
    message: str
    response_time: float
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None

class MonitoringSystem:
    """Comprehensive monitoring system"""
    
    def __init__(self):
        self.redis_client = None
        self.db_engine = None
        self.health_checks: Dict[str, HealthCheck] = {}
        self.setup_sentry()
        self.setup_prometheus()
    
    def setup_sentry(self):
        """Setup Sentry for error tracking"""
        if config.SENTRY_DSN:
            sentry_sdk.init(
                dsn=config.SENTRY_DSN,
                integrations=[
                    FastApiIntegration(auto_enabling_integrations=False),
                    SqlalchemyIntegration(),
                ],
                traces_sample_rate=0.1,
                environment=config.ENVIRONMENT,
            )
            app_logger.logger.info("Sentry monitoring initialized")
    
    def setup_prometheus(self):
        """Setup Prometheus metrics server"""
        try:
            start_http_server(9090)
            app_logger.logger.info("Prometheus metrics server started on port 9090")
        except Exception as e:
            app_logger.logger.warning(f"Failed to start Prometheus server: {e}")
    
    async def initialize(self):
        """Initialize monitoring connections"""
        try:
            # Setup Redis connection
            if config.REDIS_URL:
                self.redis_client = redis.from_url(config.REDIS_URL)
            
            # Setup database connection
            self.db_engine = create_engine(config.DATABASE_URL)
            
            app_logger.logger.info("Monitoring system initialized")
        except Exception as e:
            app_logger.logger.error(f"Failed to initialize monitoring: {e}")
    
    async def check_database_health(self) -> HealthCheck:
        """Check database connectivity and performance"""
        start_time = time.time()
        
        try:
            with self.db_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            response_time = time.time() - start_time
            
            return HealthCheck(
                name="database",
                status=HealthStatus.HEALTHY,
                message="Database connection successful",
                response_time=response_time,
                timestamp=datetime.utcnow()
            )
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheck(
                name="database",
                status=HealthStatus.UNHEALTHY,
                message=f"Database connection failed: {str(e)}",
                response_time=response_time,
                timestamp=datetime.utcnow()
            )
    
    async def check_redis_health(self) -> HealthCheck:
        """Check Redis connectivity and performance"""
        start_time = time.time()
        
        try:
            if not self.redis_client:
                raise Exception("Redis client not initialized")
            
            self.redis_client.ping()
            response_time = time.time() - start_time
            
            return HealthCheck(
                name="redis",
                status=HealthStatus.HEALTHY,
                message="Redis connection successful",
                response_time=response_time,
                timestamp=datetime.utcnow()
            )
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheck(
                name="redis",
                status=HealthStatus.UNHEALTHY,
                message=f"Redis connection failed: {str(e)}",
                response_time=response_time,
                timestamp=datetime.utcnow()
            )
    
    async def check_disk_space(self) -> HealthCheck:
        """Check available disk space"""
        start_time = time.time()
        
        try:
            disk_usage = psutil.disk_usage('/')
            free_space_gb = disk_usage.free / (1024**3)
            total_space_gb = disk_usage.total / (1024**3)
            usage_percent = (disk_usage.used / disk_usage.total) * 100
            
            # Update Prometheus metric
            SYSTEM_DISK_USAGE.set(disk_usage.used)
            
            response_time = time.time() - start_time
            
            if usage_percent > 90:
                status = HealthStatus.UNHEALTHY
                message = f"Disk space critically low: {usage_percent:.1f}% used"
            elif usage_percent > 80:
                status = HealthStatus.DEGRADED
                message = f"Disk space running low: {usage_percent:.1f}% used"
            else:
                status = HealthStatus.HEALTHY
                message = f"Disk space healthy: {usage_percent:.1f}% used"
            
            return HealthCheck(
                name="disk_space",
                status=status,
                message=message,
                response_time=response_time,
                timestamp=datetime.utcnow(),
                details={
                    "free_gb": free_space_gb,
                    "total_gb": total_space_gb,
                    "usage_percent": usage_percent
                }
            )
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheck(
                name="disk_space",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check disk space: {str(e)}",
                response_time=response_time,
                timestamp=datetime.utcnow()
            )
    
    async def check_memory_usage(self) -> HealthCheck:
        """Check system memory usage"""
        start_time = time.time()
        
        try:
            memory = psutil.virtual_memory()
            usage_percent = memory.percent
            
            # Update Prometheus metric
            SYSTEM_MEMORY_USAGE.set(memory.used)
            
            response_time = time.time() - start_time
            
            if usage_percent > 90:
                status = HealthStatus.UNHEALTHY
                message = f"Memory usage critically high: {usage_percent:.1f}%"
            elif usage_percent > 80:
                status = HealthStatus.DEGRADED
                message = f"Memory usage high: {usage_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Memory usage normal: {usage_percent:.1f}%"
            
            return HealthCheck(
                name="memory",
                status=status,
                message=message,
                response_time=response_time,
                timestamp=datetime.utcnow(),
                details={
                    "total_gb": memory.total / (1024**3),
                    "used_gb": memory.used / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "usage_percent": usage_percent
                }
            )
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheck(
                name="memory",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check memory usage: {str(e)}",
                response_time=response_time,
                timestamp=datetime.utcnow()
            )
    
    async def check_cpu_usage(self) -> HealthCheck:
        """Check system CPU usage"""
        start_time = time.time()
        
        try:
            # Get CPU usage over 1 second interval
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Update Prometheus metric
            SYSTEM_CPU_USAGE.set(cpu_percent)
            
            response_time = time.time() - start_time
            
            if cpu_percent > 90:
                status = HealthStatus.UNHEALTHY
                message = f"CPU usage critically high: {cpu_percent:.1f}%"
            elif cpu_percent > 80:
                status = HealthStatus.DEGRADED
                message = f"CPU usage high: {cpu_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"CPU usage normal: {cpu_percent:.1f}%"
            
            return HealthCheck(
                name="cpu",
                status=status,
                message=message,
                response_time=response_time,
                timestamp=datetime.utcnow(),
                details={
                    "usage_percent": cpu_percent,
                    "cpu_count": psutil.cpu_count()
                }
            )
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheck(
                name="cpu",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check CPU usage: {str(e)}",
                response_time=response_time,
                timestamp=datetime.utcnow()
            )
    
    async def run_all_health_checks(self) -> Dict[str, HealthCheck]:
        """Run all health checks concurrently"""
        checks = await asyncio.gather(
            self.check_database_health(),
            self.check_redis_health(),
            self.check_disk_space(),
            self.check_memory_usage(),
            self.check_cpu_usage(),
            return_exceptions=True
        )
        
        results = {}
        for check in checks:
            if isinstance(check, HealthCheck):
                results[check.name] = check
                self.health_checks[check.name] = check
        
        return results
    
    def get_overall_health_status(self) -> HealthStatus:
        """Get overall system health status"""
        if not self.health_checks:
            return HealthStatus.UNHEALTHY
        
        statuses = [check.status for check in self.health_checks.values()]
        
        if any(status == HealthStatus.UNHEALTHY for status in statuses):
            return HealthStatus.UNHEALTHY
        elif any(status == HealthStatus.DEGRADED for status in statuses):
            return HealthStatus.DEGRADED
        else:
            return HealthStatus.HEALTHY
    
    def record_request_metrics(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics"""
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status=status_code).inc()
        REQUEST_DURATION.observe(duration)
    
    def record_video_generation_time(self, duration: float):
        """Record video generation time"""
        VIDEO_GENERATION_TIME.observe(duration)
        perf_logger.log_timing("video_generation", duration)

# Global monitoring instance
monitoring = MonitoringSystem()

# Middleware for request monitoring
class MonitoringMiddleware:
    """Middleware to monitor HTTP requests"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            start_time = time.time()
            
            # Process request
            await self.app(scope, receive, send)
            
            # Record metrics
            duration = time.time() - start_time
            method = scope["method"]
            path = scope["path"]
            
            # This would need to be integrated with FastAPI to get status code
            monitoring.record_request_metrics(method, path, 200, duration)
        else:
            await self.app(scope, receive, send)
