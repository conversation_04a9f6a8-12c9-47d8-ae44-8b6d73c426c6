version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: xvion-postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-xvion_db}
      POSTGRES_USER: ${DB_USER:-xvion_user}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-xvion_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-xvion_user} -d ${DB_NAME:-xvion_db}"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - xvion-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: xvion-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - xvion-network

  # Database Migration Service
  migrate:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    container_name: xvion-migrate
    environment:
      - PYTHON_ENV=production
      - DATABASE_URL=postgresql://${DB_USER:-xvion_user}:${DB_PASSWORD:-xvion_password}@postgres:5432/${DB_NAME:-xvion_db}
    env_file:
      - .env
    volumes:
      - ./alembic:/app/alembic
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    command: ["python", "migrate.py", "upgrade"]
    networks:
      - xvion-network

  # Backend API Server
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    container_name: xvion-backend
    environment:
      - PYTHON_ENV=production
      - DATABASE_URL=postgresql://${DB_USER:-xvion_user}:${DB_PASSWORD:-xvion_password}@postgres:5432/${DB_NAME:-xvion_db}
      - REDIS_URL=redis://redis:6379/0
      - CORS_ORIGINS=http://localhost:3000,https://your-domain.com
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
      - ./output:/app/output
      - ./assets:/app/assets
      - ./logs:/app/logs
      - ./alembic:/app/alembic
    ports:
      - "8001:8001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      migrate:
        condition: service_completed_successfully
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - xvion-network

  # Frontend Next.js Application
  frontend:
    build:
      context: ./xvion-frontend
      dockerfile: Dockerfile
    container_name: xvion-frontend
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:8001
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - xvion-network

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: xvion-nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - xvion-network

  # Celery Worker for Background Tasks
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    container_name: xvion-celery-worker
    command: celery -A celery_app worker --loglevel=info
    environment:
      - PYTHON_ENV=production
      - DATABASE_URL=postgresql://${DB_USER:-xvion_user}:${DB_PASSWORD:-xvion_password}@postgres:5432/${DB_NAME:-xvion_db}
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
      - ./output:/app/output
      - ./assets:/app/assets
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - xvion-network

  # Celery Beat for Scheduled Tasks
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    container_name: xvion-celery-beat
    command: celery -A celery_app beat --loglevel=info
    environment:
      - PYTHON_ENV=production
      - DATABASE_URL=postgresql://${DB_USER:-xvion_user}:${DB_PASSWORD:-xvion_password}@postgres:5432/${DB_NAME:-xvion_db}
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - xvion-network

volumes:
  postgres_data:
  redis_data:

networks:
  xvion-network:
    driver: bridge
