"""
Authentication Tests for Xvion Application
Tests all authentication endpoints and functionality
"""
import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import uuid
import time

from api_server_production import app
from database import get_db, Base, User
from config_production import TestingConfig

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_auth.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="session")
def setup_database():
    """Setup test database"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(setup_database):
    """Test client fixture"""
    with TestClient(app) as client:
        yield client

@pytest.fixture
def test_user_data():
    """Test user data fixture"""
    return {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "password": "TestPassword123",
        "confirm_password": "TestPassword123"
    }

@pytest.fixture
def authenticated_user(client, test_user_data):
    """Create and authenticate a test user"""
    # Register user
    response = client.post("/api/auth/register", json=test_user_data)
    assert response.status_code == 201
    
    # Login user
    login_response = client.post("/api/auth/login", json={
        "email_or_username": test_user_data["email"],
        "password": test_user_data["password"]
    })
    assert login_response.status_code == 200
    
    token_data = login_response.json()
    return {
        "token": token_data["access_token"],
        "user": token_data["user"],
        "headers": {"Authorization": f"Bearer {token_data['access_token']}"}
    }

class TestUserRegistration:
    """Test user registration functionality"""
    
    def test_register_valid_user(self, client, test_user_data):
        """Test registering a valid user"""
        response = client.post("/api/auth/register", json=test_user_data)
        assert response.status_code == 201
        
        data = response.json()
        assert "user" in data
        assert data["user"]["email"] == test_user_data["email"]
        assert data["user"]["username"] == test_user_data["username"]
        assert data["user"]["full_name"] == test_user_data["full_name"]
        assert "password" not in data["user"]  # Password should not be returned
    
    def test_register_duplicate_email(self, client, test_user_data):
        """Test registering with duplicate email"""
        # Register first user
        response1 = client.post("/api/auth/register", json=test_user_data)
        assert response1.status_code == 201
        
        # Try to register with same email
        duplicate_data = test_user_data.copy()
        duplicate_data["username"] = "different_username"
        response2 = client.post("/api/auth/register", json=duplicate_data)
        assert response2.status_code == 400
        assert "email" in response2.json()["detail"].lower()
    
    def test_register_duplicate_username(self, client, test_user_data):
        """Test registering with duplicate username"""
        # Register first user
        response1 = client.post("/api/auth/register", json=test_user_data)
        assert response1.status_code == 201
        
        # Try to register with same username
        duplicate_data = test_user_data.copy()
        duplicate_data["email"] = "<EMAIL>"
        response2 = client.post("/api/auth/register", json=duplicate_data)
        assert response2.status_code == 400
        assert "username" in response2.json()["detail"].lower()
    
    def test_register_invalid_email(self, client, test_user_data):
        """Test registering with invalid email"""
        invalid_data = test_user_data.copy()
        invalid_data["email"] = "invalid-email"
        
        response = client.post("/api/auth/register", json=invalid_data)
        assert response.status_code == 422
    
    def test_register_weak_password(self, client, test_user_data):
        """Test registering with weak password"""
        weak_data = test_user_data.copy()
        weak_data["password"] = "weak"
        weak_data["confirm_password"] = "weak"
        
        response = client.post("/api/auth/register", json=weak_data)
        assert response.status_code == 422
    
    def test_register_password_mismatch(self, client, test_user_data):
        """Test registering with password mismatch"""
        mismatch_data = test_user_data.copy()
        mismatch_data["confirm_password"] = "DifferentPassword123"
        
        response = client.post("/api/auth/register", json=mismatch_data)
        assert response.status_code == 422

class TestUserLogin:
    """Test user login functionality"""
    
    def test_login_with_email(self, client, authenticated_user):
        """Test login with email"""
        # Login is already tested in authenticated_user fixture
        assert "token" in authenticated_user
        assert "user" in authenticated_user
    
    def test_login_with_username(self, client, test_user_data):
        """Test login with username"""
        # Register user first
        client.post("/api/auth/register", json=test_user_data)
        
        # Login with username
        response = client.post("/api/auth/login", json={
            "email_or_username": test_user_data["username"],
            "password": test_user_data["password"]
        })
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert "user" in data
    
    def test_login_invalid_credentials(self, client, test_user_data):
        """Test login with invalid credentials"""
        # Register user first
        client.post("/api/auth/register", json=test_user_data)
        
        # Try login with wrong password
        response = client.post("/api/auth/login", json={
            "email_or_username": test_user_data["email"],
            "password": "WrongPassword123"
        })
        assert response.status_code == 401
    
    def test_login_nonexistent_user(self, client):
        """Test login with nonexistent user"""
        response = client.post("/api/auth/login", json={
            "email_or_username": "<EMAIL>",
            "password": "SomePassword123"
        })
        assert response.status_code == 401

class TestProtectedEndpoints:
    """Test protected endpoints require authentication"""
    
    def test_get_current_user_authenticated(self, client, authenticated_user):
        """Test getting current user with valid token"""
        response = client.get("/api/auth/me", headers=authenticated_user["headers"])
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == authenticated_user["user"]["email"]
        assert data["username"] == authenticated_user["user"]["username"]
    
    def test_get_current_user_unauthenticated(self, client):
        """Test getting current user without token"""
        response = client.get("/api/auth/me")
        assert response.status_code == 401
    
    def test_get_current_user_invalid_token(self, client):
        """Test getting current user with invalid token"""
        response = client.get("/api/auth/me", headers={"Authorization": "Bearer invalid_token"})
        assert response.status_code == 401
    
    def test_update_profile_authenticated(self, client, authenticated_user):
        """Test updating profile with valid token"""
        update_data = {
            "full_name": "Updated Name",
            "email": authenticated_user["user"]["email"],  # Keep same email
            "username": authenticated_user["user"]["username"]  # Keep same username
        }
        
        response = client.patch("/api/auth/profile", json=update_data, headers=authenticated_user["headers"])
        assert response.status_code == 200
        
        data = response.json()
        assert data["full_name"] == "Updated Name"
    
    def test_change_password_authenticated(self, client, authenticated_user, test_user_data):
        """Test changing password with valid token"""
        change_data = {
            "current_password": test_user_data["password"],
            "new_password": "NewPassword123"
        }
        
        response = client.patch("/api/auth/change-password", json=change_data, headers=authenticated_user["headers"])
        assert response.status_code == 200
        
        # Test login with new password
        login_response = client.post("/api/auth/login", json={
            "email_or_username": authenticated_user["user"]["email"],
            "password": "NewPassword123"
        })
        assert login_response.status_code == 200

class TestPasswordReset:
    """Test password reset functionality"""
    
    def test_request_password_reset(self, client, authenticated_user):
        """Test requesting password reset"""
        response = client.post("/api/auth/request-password-reset", json={
            "email": authenticated_user["user"]["email"]
        })
        assert response.status_code == 200
        assert "message" in response.json()
    
    def test_request_password_reset_nonexistent_email(self, client):
        """Test requesting password reset for nonexistent email"""
        response = client.post("/api/auth/request-password-reset", json={
            "email": "<EMAIL>"
        })
        # Should still return 200 for security (don't reveal if email exists)
        assert response.status_code == 200

class TestQuizEndpointsWithAuth:
    """Test quiz endpoints with authentication"""
    
    def test_create_quiz_authenticated(self, client, authenticated_user):
        """Test creating quiz with authentication"""
        quiz_data = {
            "title": "Test Quiz",
            "description": "Test Description",
            "questions": [
                {
                    "text": "What is 2+2?",
                    "options": ["3", "4", "5"],
                    "correct_answer": "4"
                }
            ],
            "language": "en"
        }
        
        response = client.post("/api/quizzes", json=quiz_data, headers=authenticated_user["headers"])
        assert response.status_code == 201
        
        data = response.json()
        assert data["title"] == quiz_data["title"]
        assert data["created_by"] == authenticated_user["user"]["id"]
    
    def test_get_user_quizzes(self, client, authenticated_user):
        """Test getting user's quizzes"""
        # Create a quiz first
        quiz_data = {
            "title": "User Quiz",
            "description": "User's quiz",
            "questions": [{"text": "Test?", "options": ["A", "B"], "correct_answer": "A"}],
            "language": "en"
        }
        client.post("/api/quizzes", json=quiz_data, headers=authenticated_user["headers"])
        
        # Get user's quizzes
        response = client.get("/api/quizzes", headers=authenticated_user["headers"])
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert len(data["items"]) > 0
        assert all(quiz["created_by"] == authenticated_user["user"]["id"] for quiz in data["items"])

class TestVideoGenerationWithAuth:
    """Test video generation with authentication"""
    
    def test_generate_video_authenticated(self, client, authenticated_user):
        """Test generating video with authentication"""
        # Create a quiz first
        quiz_data = {
            "title": "Video Quiz",
            "description": "Quiz for video",
            "questions": [{"text": "Test?", "options": ["A", "B"], "correct_answer": "A"}],
            "language": "en"
        }
        quiz_response = client.post("/api/quizzes", json=quiz_data, headers=authenticated_user["headers"])
        quiz_id = quiz_response.json()["id"]
        
        # Generate video
        video_data = {"quiz_id": quiz_id}
        response = client.post("/api/videos/generate", json=video_data, headers=authenticated_user["headers"])
        assert response.status_code == 200
        
        data = response.json()
        assert "job_id" in data
    
    def test_get_video_jobs_authenticated(self, client, authenticated_user):
        """Test getting video jobs with authentication"""
        response = client.get("/api/jobs", headers=authenticated_user["headers"])
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data

class TestTokenValidation:
    """Test JWT token validation"""
    
    def test_token_expiration(self, client, authenticated_user):
        """Test token expiration handling"""
        # This would require mocking time or using short-lived tokens
        # For now, just test that valid tokens work
        response = client.get("/api/auth/me", headers=authenticated_user["headers"])
        assert response.status_code == 200
    
    def test_malformed_token(self, client):
        """Test handling of malformed tokens"""
        malformed_tokens = [
            "Bearer malformed.token",
            "Bearer not.a.jwt.token",
            "Bearer ",
            "InvalidBearer token",
        ]
        
        for token in malformed_tokens:
            response = client.get("/api/auth/me", headers={"Authorization": token})
            assert response.status_code == 401

class TestSecurityFeatures:
    """Test security features"""
    
    def test_password_hashing(self, client, test_user_data):
        """Test that passwords are properly hashed"""
        # Register user
        response = client.post("/api/auth/register", json=test_user_data)
        assert response.status_code == 201
        
        # Check that password is not stored in plain text
        db = TestingSessionLocal()
        try:
            user = db.query(User).filter(User.email == test_user_data["email"]).first()
            assert user is not None
            assert user.password_hash != test_user_data["password"]
            assert len(user.password_hash) > 50  # Hashed password should be long
        finally:
            db.close()
    
    def test_sql_injection_protection(self, client):
        """Test protection against SQL injection"""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users --"
        ]
        
        for malicious_input in malicious_inputs:
            response = client.post("/api/auth/login", json={
                "email_or_username": malicious_input,
                "password": "password"
            })
            # Should not cause server error, should return 401 or 422
            assert response.status_code in [401, 422]

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
