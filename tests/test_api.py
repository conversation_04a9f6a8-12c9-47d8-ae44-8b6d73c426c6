"""
API Tests for Xvion Application
Tests all API endpoints and functionality
"""
import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from api_server_production import app
from database import get_db, Base
from config_production import TestingConfig

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="session")
def setup_database():
    """Setup test database"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(setup_database):
    """Test client fixture"""
    with TestClient(app) as client:
        yield client

@pytest.fixture
async def async_client(setup_database):
    """Async test client fixture"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

class TestHealthEndpoint:
    """Test health check endpoint"""
    
    def test_health_check(self, client):
        """Test health check returns 200"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data

class TestQuizEndpoints:
    """Test quiz-related endpoints"""
    
    def test_get_quizzes_unauthorized(self, client):
        """Test getting quizzes without authentication"""
        response = client.get("/api/quizzes")
        assert response.status_code == 401
    
    def test_create_quiz_unauthorized(self, client):
        """Test creating quiz without authentication"""
        quiz_data = {
            "title": "Test Quiz",
            "description": "Test Description",
            "questions": [
                {
                    "text": "What is 2+2?",
                    "options": ["3", "4", "5"],
                    "correct_answer": "4"
                }
            ],
            "language": "en"
        }
        response = client.post("/api/quizzes", json=quiz_data)
        assert response.status_code == 401
    
    def test_create_quiz_invalid_data(self, client):
        """Test creating quiz with invalid data"""
        # Mock authentication for this test
        with client:
            quiz_data = {
                "title": "",  # Invalid: empty title
                "questions": []  # Invalid: no questions
            }
            # This would need proper authentication setup
            # response = client.post("/api/quizzes", json=quiz_data, headers={"Authorization": "Bearer valid_token"})
            # assert response.status_code == 400

class TestSecurity:
    """Test security features"""
    
    def test_cors_headers(self, client):
        """Test CORS headers are present"""
        response = client.options("/health")
        # CORS headers would be tested here
    
    def test_rate_limiting(self, client):
        """Test rate limiting"""
        # Make multiple requests to test rate limiting
        for i in range(15):  # Assuming limit is 10/minute
            response = client.get("/health")
            if i < 10:
                assert response.status_code == 200
            # After limit, should get 429
    
    def test_input_validation(self, client):
        """Test input validation"""
        # Test various malicious inputs
        malicious_inputs = [
            {"title": "<script>alert('xss')</script>"},
            {"title": "'; DROP TABLE quizzes; --"},
            {"title": "../../../etc/passwd"},
        ]
        
        for malicious_input in malicious_inputs:
            response = client.post("/api/quizzes", json=malicious_input)
            # Should be rejected (401 for auth, 422 for validation)
            assert response.status_code in [401, 422]

class TestVideoGeneration:
    """Test video generation functionality"""
    
    @pytest.mark.asyncio
    async def test_video_generation_flow(self, async_client):
        """Test complete video generation flow"""
        # This would test the full video generation process
        # 1. Create quiz
        # 2. Generate video
        # 3. Check status
        # 4. Download result
        pass

class TestDatabase:
    """Test database operations"""
    
    def test_database_connection(self):
        """Test database connection"""
        db = TestingSessionLocal()
        try:
            # Simple query to test connection
            result = db.execute("SELECT 1")
            assert result.fetchone()[0] == 1
        finally:
            db.close()
    
    def test_quiz_model_creation(self):
        """Test quiz model creation and retrieval"""
        # This would test CRUD operations on Quiz model
        pass

class TestPerformance:
    """Test performance characteristics"""
    
    @pytest.mark.asyncio
    async def test_response_time(self, async_client):
        """Test API response times"""
        import time
        
        start_time = time.time()
        response = await async_client.get("/health")
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 1.0  # Should respond within 1 second
    
    def test_concurrent_requests(self, client):
        """Test handling concurrent requests"""
        import threading
        import time
        
        results = []
        
        def make_request():
            response = client.get("/health")
            results.append(response.status_code)
        
        # Create multiple threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert all(status == 200 for status in results)
        assert len(results) == 10

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
