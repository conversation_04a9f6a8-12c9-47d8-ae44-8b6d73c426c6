"""
Authentication and Authorization Middleware for Xvion
Provides JWT token validation, role-based access control, and route protection
"""

from typing import Optional, List, Callable
from functools import wraps
import logging

from fastapi import HTTPException, status, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from database import get_db, User as DBUser
from security import SecurityManager

# Initialize security manager and logger
security = SecurityManager()
logger = logging.getLogger(__name__)
security_scheme = HTTPBearer(auto_error=False)

class AuthMiddleware:
    """Authentication and authorization middleware"""
    
    def __init__(self):
        self.security = SecurityManager()
    
    async def get_current_user(
        self,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_scheme),
        db: Session = Depends(get_db)
    ) -> Optional[DBUser]:
        """Get current authenticated user from JWT token"""
        
        if not credentials:
            return None
        
        try:
            # Verify JWT token
            payload = self.security.verify_token(credentials.credentials)
            user_id = payload.get('user_id')
            
            if not user_id:
                return None
            
            # Get user from database
            user = db.query(DBUser).filter(DBUser.id == user_id).first()
            
            if not user or not user.is_active:
                return None
            
            return user
            
        except Exception as e:
            logger.warning(f"Token validation failed: {e}")
            return None
    
    async def require_auth(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(security_scheme),
        db: Session = Depends(get_db)
    ) -> DBUser:
        """Require authentication - raises exception if not authenticated"""
        
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        try:
            # Verify JWT token
            payload = self.security.verify_token(credentials.credentials)
            user_id = payload.get('user_id')
            
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Get user from database
            user = db.query(DBUser).filter(DBUser.id == user_id).first()
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User account is disabled",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            logger.warning(f"Authentication failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    async def require_role(
        self,
        required_roles: List[str],
        user: DBUser = Depends(require_auth)
    ) -> DBUser:
        """Require specific role(s) - raises exception if user doesn't have required role"""
        
        if user.role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {', '.join(required_roles)}"
            )
        
        return user
    
    async def require_admin(
        self,
        user: DBUser = Depends(require_auth)
    ) -> DBUser:
        """Require admin role"""
        return await self.require_role(['admin'], user)
    
    async def require_verified_email(
        self,
        user: DBUser = Depends(require_auth)
    ) -> DBUser:
        """Require verified email"""
        
        if not user.is_verified:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Email verification required"
            )
        
        return user

# Create global middleware instance
auth_middleware = AuthMiddleware()

# Convenience dependency functions
async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_scheme),
    db: Session = Depends(get_db)
) -> Optional[DBUser]:
    """Get current user (optional)"""
    return await auth_middleware.get_current_user(credentials, db)

async def require_auth(
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme),
    db: Session = Depends(get_db)
) -> DBUser:
    """Require authentication"""
    return await auth_middleware.require_auth(credentials, db)

async def require_admin(
    user: DBUser = Depends(require_auth)
) -> DBUser:
    """Require admin role"""
    return await auth_middleware.require_admin(user)

async def require_verified_email(
    user: DBUser = Depends(require_auth)
) -> DBUser:
    """Require verified email"""
    return await auth_middleware.require_verified_email(user)

def require_roles(*roles: str):
    """Decorator factory for requiring specific roles"""
    async def dependency(user: DBUser = Depends(require_auth)) -> DBUser:
        return await auth_middleware.require_role(list(roles), user)
    return dependency

# Decorator functions for route protection
def protected(func: Callable) -> Callable:
    """Decorator to protect routes with authentication"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # The actual authentication is handled by the dependency injection
        # This decorator is mainly for documentation purposes
        return await func(*args, **kwargs)
    return wrapper

def admin_only(func: Callable) -> Callable:
    """Decorator to protect routes for admin users only"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # The actual role checking is handled by the dependency injection
        return await func(*args, **kwargs)
    return wrapper

def verified_only(func: Callable) -> Callable:
    """Decorator to protect routes for verified users only"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # The actual verification checking is handled by the dependency injection
        return await func(*args, **kwargs)
    return wrapper

# Rate limiting decorator (placeholder for future implementation)
def rate_limit(requests_per_minute: int = 60):
    """Rate limiting decorator (placeholder)"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # TODO: Implement actual rate limiting logic
            # For now, just pass through
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# CORS and security headers middleware
class SecurityHeadersMiddleware:
    """Middleware to add security headers"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            # Add security headers
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    headers = dict(message.get("headers", []))
                    
                    # Add security headers
                    security_headers = {
                        b"x-content-type-options": b"nosniff",
                        b"x-frame-options": b"DENY",
                        b"x-xss-protection": b"1; mode=block",
                        b"strict-transport-security": b"max-age=31536000; includeSubDomains",
                        b"referrer-policy": b"strict-origin-when-cross-origin",
                        b"permissions-policy": b"geolocation=(), microphone=(), camera=()"
                    }
                    
                    for key, value in security_headers.items():
                        if key not in headers:
                            headers[key] = value
                    
                    message["headers"] = list(headers.items())
                
                await send(message)
            
            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)

# User context utilities
class UserContext:
    """Utility class for user context management"""
    
    @staticmethod
    def is_owner_or_admin(user: DBUser, resource_owner_id: str) -> bool:
        """Check if user is the owner of a resource or an admin"""
        return user.role == 'admin' or user.id == resource_owner_id
    
    @staticmethod
    def can_modify_resource(user: DBUser, resource_owner_id: str) -> bool:
        """Check if user can modify a resource"""
        return UserContext.is_owner_or_admin(user, resource_owner_id)
    
    @staticmethod
    def can_view_resource(user: DBUser, resource_owner_id: str, is_public: bool = False) -> bool:
        """Check if user can view a resource"""
        if is_public:
            return True
        return UserContext.is_owner_or_admin(user, resource_owner_id)

# Export commonly used dependencies
__all__ = [
    'auth_middleware',
    'get_current_user',
    'require_auth',
    'require_admin',
    'require_verified_email',
    'require_roles',
    'protected',
    'admin_only',
    'verified_only',
    'rate_limit',
    'SecurityHeadersMiddleware',
    'UserContext'
]
