"""
Logging Configuration for Xvion Application
Provides structured logging with rotation and different levels
"""
import os
import logging
import logging.handlers
from datetime import datetime
from config_production import current_config as config

def setup_logging():
    """Setup application logging configuration"""
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(config.LOG_FILE)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create detailed formatter for file logging
    detailed_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.LOG_LEVEL.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if config.LOG_FILE:
        file_handler = logging.handlers.RotatingFileHandler(
            config.LOG_FILE,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(getattr(logging, config.LOG_LEVEL.upper()))
        file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(file_handler)
    
    # Error file handler
    error_log_file = config.LOG_FILE.replace('.log', '_error.log') if config.LOG_FILE else 'logs/error.log'
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)
    
    # Set specific logger levels
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('uvicorn.access').setLevel(logging.INFO)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    
    # Application loggers
    app_logger = logging.getLogger('xvion')
    app_logger.setLevel(getattr(logging, config.LOG_LEVEL.upper()))
    
    return root_logger

class StructuredLogger:
    """Structured logger for application events"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_request(self, method: str, path: str, status_code: int, duration: float, user_id: str = None):
        """Log HTTP request"""
        self.logger.info(
            f"HTTP Request - Method: {method}, Path: {path}, Status: {status_code}, "
            f"Duration: {duration:.3f}s, User: {user_id or 'anonymous'}"
        )
    
    def log_video_generation(self, quiz_id: str, status: str, duration: float = None, error: str = None):
        """Log video generation events"""
        message = f"Video Generation - Quiz: {quiz_id}, Status: {status}"
        if duration:
            message += f", Duration: {duration:.3f}s"
        if error:
            message += f", Error: {error}"
        
        if status == 'error':
            self.logger.error(message)
        else:
            self.logger.info(message)
    
    def log_database_operation(self, operation: str, table: str, record_id: str = None, error: str = None):
        """Log database operations"""
        message = f"Database - Operation: {operation}, Table: {table}"
        if record_id:
            message += f", ID: {record_id}"
        if error:
            message += f", Error: {error}"
            self.logger.error(message)
        else:
            self.logger.info(message)
    
    def log_api_key_usage(self, service: str, key_index: int, success: bool, error: str = None):
        """Log API key usage"""
        status = "success" if success else "failed"
        message = f"API Key Usage - Service: {service}, Key Index: {key_index}, Status: {status}"
        if error:
            message += f", Error: {error}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.warning(message)
    
    def log_security_event(self, event_type: str, details: str, severity: str = "info"):
        """Log security events"""
        message = f"Security Event - Type: {event_type}, Details: {details}"
        
        if severity == "critical":
            self.logger.critical(message)
        elif severity == "error":
            self.logger.error(message)
        elif severity == "warning":
            self.logger.warning(message)
        else:
            self.logger.info(message)

# Performance monitoring logger
class PerformanceLogger:
    """Logger for performance monitoring"""
    
    def __init__(self):
        self.logger = logging.getLogger('xvion.performance')
    
    def log_timing(self, operation: str, duration: float, metadata: dict = None):
        """Log operation timing"""
        message = f"Performance - Operation: {operation}, Duration: {duration:.3f}s"
        if metadata:
            message += f", Metadata: {metadata}"
        self.logger.info(message)
    
    def log_resource_usage(self, cpu_percent: float, memory_mb: float, disk_usage_mb: float):
        """Log resource usage"""
        self.logger.info(
            f"Resource Usage - CPU: {cpu_percent:.1f}%, Memory: {memory_mb:.1f}MB, "
            f"Disk: {disk_usage_mb:.1f}MB"
        )

# Initialize logging on import
setup_logging()

# Create application loggers
app_logger = StructuredLogger('xvion.app')
perf_logger = PerformanceLogger()

# Export loggers
__all__ = ['app_logger', 'perf_logger', 'StructuredLogger', 'PerformanceLogger']
