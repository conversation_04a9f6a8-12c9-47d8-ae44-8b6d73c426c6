"""
Authentication endpoints and utilities for Xvion
Handles user registration, login, logout, password reset, and profile management
"""

import os
import uuid
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib

from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from fastapi.security import H<PERSON><PERSON>Bearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, validator
from sqlalchemy.orm import Session
from sqlalchemy import or_

from database import get_db, User as DBUser
from security import SecurityManager
from config_production import current_config as config

# Initialize security manager
security = SecurityManager()
security_scheme = HTTPBearer()

# Create router
auth_router = APIRouter(prefix="/auth", tags=["authentication"])

# Pydantic models for request/response
class UserRegister(BaseModel):
    email: EmailStr
    username: str
    full_name: str
    password: str
    confirm_password: str
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v.lower()
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'password' in values and v != values['password']:
            raise ValueError('Passwords do not match')
        return v

class UserLogin(BaseModel):
    email_or_username: str
    password: str

class UserResponse(BaseModel):
    id: str
    email: str
    username: str
    full_name: str
    is_active: bool
    is_verified: bool
    role: str
    avatar_url: Optional[str] = None
    last_login: Optional[datetime] = None
    created_at: datetime

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: UserResponse

class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordReset(BaseModel):
    token: str
    new_password: str
    confirm_password: str
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v

class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None

class PasswordChange(BaseModel):
    current_password: str
    new_password: str
    confirm_password: str
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v

# Dependency to get current user
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme),
    db: Session = Depends(get_db)
) -> DBUser:
    """Get current authenticated user"""
    try:
        payload = security.verify_token(credentials.credentials)
        user_id = payload.get('user_id')
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
        
        user = db.query(DBUser).filter(DBUser.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is disabled"
            )
        
        return user
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )

# Optional dependency for current user (doesn't raise error if not authenticated)
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_scheme),
    db: Session = Depends(get_db)
) -> Optional[DBUser]:
    """Get current user if authenticated, None otherwise"""
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials, db)
    except:
        return None

# Email utility functions
async def send_verification_email(email: str, token: str, background_tasks: BackgroundTasks):
    """Send email verification email"""
    # This is a placeholder - implement actual email sending
    # You would use your preferred email service (SendGrid, AWS SES, etc.)
    print(f"Sending verification email to {email} with token {token}")

async def send_password_reset_email(email: str, token: str, background_tasks: BackgroundTasks):
    """Send password reset email"""
    # This is a placeholder - implement actual email sending
    print(f"Sending password reset email to {email} with token {token}")

# Authentication endpoints

@auth_router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserRegister,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Register a new user"""
    
    # Check if user already exists
    existing_user = db.query(DBUser).filter(
        or_(DBUser.email == user_data.email, DBUser.username == user_data.username)
    ).first()
    
    if existing_user:
        if existing_user.email == user_data.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )
    
    # Hash password
    password_hash = security.hash_password(user_data.password)
    
    # Generate email verification token
    verification_token = secrets.token_urlsafe(32)
    
    # Create user
    db_user = DBUser(
        email=user_data.email,
        username=user_data.username,
        full_name=user_data.full_name,
        password_hash=password_hash,
        email_verification_token=verification_token,
        is_active=True,  # Set to False if you want email verification required
        is_verified=False,
        role="user"
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Send verification email
    await send_verification_email(user_data.email, verification_token, background_tasks)
    
    return UserResponse(
        id=db_user.id,
        email=db_user.email,
        username=db_user.username,
        full_name=db_user.full_name,
        is_active=db_user.is_active,
        is_verified=db_user.is_verified,
        role=db_user.role,
        avatar_url=db_user.avatar_url,
        last_login=db_user.last_login,
        created_at=db_user.created_at
    )

@auth_router.post("/login", response_model=LoginResponse)
async def login_user(
    user_data: UserLogin,
    db: Session = Depends(get_db)
):
    """Login user and return access token"""
    
    # Find user by email or username
    user = db.query(DBUser).filter(
        or_(
            DBUser.email == user_data.email_or_username,
            DBUser.username == user_data.email_or_username
        )
    ).first()
    
    if not user or not security.verify_password(user_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email/username or password"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is disabled"
        )
    
    # Update last login
    user.last_login = datetime.utcnow()
    db.commit()
    
    # Generate access token
    token_data = {
        'user_id': user.id,
        'email': user.email,
        'role': user.role
    }
    access_token = security.generate_token(token_data)
    
    return LoginResponse(
        access_token=access_token,
        user=UserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            full_name=user.full_name,
            is_active=user.is_active,
            is_verified=user.is_verified,
            role=user.role,
            avatar_url=user.avatar_url,
            last_login=user.last_login,
            created_at=user.created_at
        )
    )

@auth_router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: DBUser = Depends(get_current_user)
):
    """Get current user information"""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        username=current_user.username,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        role=current_user.role,
        avatar_url=current_user.avatar_url,
        last_login=current_user.last_login,
        created_at=current_user.created_at
    )

@auth_router.put("/me", response_model=UserResponse)
async def update_user_profile(
    user_update: UserUpdate,
    current_user: DBUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update current user profile"""

    if user_update.full_name is not None:
        current_user.full_name = user_update.full_name

    if user_update.avatar_url is not None:
        current_user.avatar_url = user_update.avatar_url

    current_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(current_user)

    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        username=current_user.username,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        role=current_user.role,
        avatar_url=current_user.avatar_url,
        last_login=current_user.last_login,
        created_at=current_user.created_at
    )

@auth_router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: DBUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Change user password"""

    # Verify current password
    if not security.verify_password(password_data.current_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )

    # Hash new password
    new_password_hash = security.hash_password(password_data.new_password)

    # Update password
    current_user.password_hash = new_password_hash
    current_user.updated_at = datetime.utcnow()
    db.commit()

    return {"message": "Password changed successfully"}

@auth_router.post("/request-password-reset")
async def request_password_reset(
    reset_request: PasswordResetRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Request password reset"""

    user = db.query(DBUser).filter(DBUser.email == reset_request.email).first()

    if not user:
        # Don't reveal if email exists or not for security
        return {"message": "If the email exists, a password reset link has been sent"}

    # Generate reset token
    reset_token = secrets.token_urlsafe(32)
    reset_expires = datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour

    # Update user with reset token
    user.password_reset_token = reset_token
    user.password_reset_expires = reset_expires
    user.updated_at = datetime.utcnow()
    db.commit()

    # Send reset email
    await send_password_reset_email(user.email, reset_token, background_tasks)

    return {"message": "If the email exists, a password reset link has been sent"}

@auth_router.post("/reset-password")
async def reset_password(
    reset_data: PasswordReset,
    db: Session = Depends(get_db)
):
    """Reset password using token"""

    user = db.query(DBUser).filter(
        DBUser.password_reset_token == reset_data.token
    ).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )

    # Check if token is expired
    if not user.password_reset_expires or user.password_reset_expires < datetime.utcnow():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Reset token has expired"
        )

    # Hash new password
    new_password_hash = security.hash_password(reset_data.new_password)

    # Update password and clear reset token
    user.password_hash = new_password_hash
    user.password_reset_token = None
    user.password_reset_expires = None
    user.updated_at = datetime.utcnow()
    db.commit()

    return {"message": "Password reset successfully"}

@auth_router.post("/verify-email")
async def verify_email(
    token: str,
    db: Session = Depends(get_db)
):
    """Verify email address"""

    user = db.query(DBUser).filter(
        DBUser.email_verification_token == token
    ).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification token"
        )

    # Mark email as verified
    user.is_verified = True
    user.email_verification_token = None
    user.updated_at = datetime.utcnow()
    db.commit()

    return {"message": "Email verified successfully"}

@auth_router.post("/resend-verification")
async def resend_verification_email(
    email: EmailStr,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Resend email verification"""

    user = db.query(DBUser).filter(DBUser.email == email).first()

    if not user:
        # Don't reveal if email exists or not for security
        return {"message": "If the email exists and is not verified, a verification link has been sent"}

    if user.is_verified:
        return {"message": "Email is already verified"}

    # Generate new verification token
    verification_token = secrets.token_urlsafe(32)
    user.email_verification_token = verification_token
    user.updated_at = datetime.utcnow()
    db.commit()

    # Send verification email
    await send_verification_email(user.email, verification_token, background_tasks)

    return {"message": "If the email exists and is not verified, a verification link has been sent"}

@auth_router.post("/logout")
async def logout_user(
    current_user: DBUser = Depends(get_current_user)
):
    """Logout user (client should discard token)"""
    # In a stateless JWT system, logout is handled client-side by discarding the token
    # For more security, you could implement a token blacklist
    return {"message": "Logged out successfully"}
