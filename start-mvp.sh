#!/bin/bash

# Xvion MVP Startup Script
# This script starts both the backend API server and frontend development server

echo "🚀 Starting Xvion MVP..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "api_server.py" ] || [ ! -d "xvion-frontend" ]; then
    print_error "Please run this script from the Xvion project root directory"
    exit 1
fi

# Check Python dependencies
print_status "Checking Python dependencies..."
python3 -c "import fastapi, uvicorn, sqlalchemy, psycopg" 2>/dev/null
if [ $? -ne 0 ]; then
    print_error "Missing Python dependencies. Please install requirements:"
    echo "pip3 install -r requirements.txt"
    exit 1
fi

# Check Node.js dependencies
print_status "Checking Node.js dependencies..."
if [ ! -d "xvion-frontend/node_modules" ]; then
    print_warning "Frontend dependencies not installed. Installing now..."
    cd xvion-frontend
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install frontend dependencies"
        exit 1
    fi
    cd ..
fi

# Check database connection
print_status "Testing database connection..."
python3 -c "from database import test_connection; test_connection()" 2>/dev/null
if [ $? -ne 0 ]; then
    print_error "Database connection failed. Please ensure PostgreSQL is running and configured."
    print_status "Quick setup commands:"
    echo "  brew install postgresql@14"
    echo "  brew services start postgresql@14"
    echo "  createdb xvion_db"
    exit 1
fi

# Initialize database if needed
print_status "Initializing database..."
python3 database.py > /dev/null 2>&1

# Create log directory
mkdir -p logs

print_success "All checks passed! Starting services..."
echo ""

# Start backend server in background
print_status "Starting backend API server on http://localhost:8001..."
python3 api_server.py > logs/backend.log 2>&1 &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Check if backend started successfully
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    print_error "Backend server failed to start. Check logs/backend.log"
    exit 1
fi

# Start frontend server in background
print_status "Starting frontend development server on http://localhost:3000..."
cd xvion-frontend
npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# Wait a moment for frontend to start
sleep 5

# Check if frontend started successfully
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    print_error "Frontend server failed to start. Check logs/frontend.log"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

print_success "🎉 Xvion MVP is now running!"
echo ""
echo "📊 Frontend Dashboard: http://localhost:3000 (or 3001 if 3000 is busy)"
echo "📖 API Documentation: http://localhost:8001/docs"
echo "🔍 API Health Check: http://localhost:8001/health"
echo ""
echo "📝 Logs:"
echo "  Backend: logs/backend.log"
echo "  Frontend: logs/frontend.log"
echo ""
echo "🛑 To stop the servers, run: ./stop-mvp.sh"
echo "   Or press Ctrl+C and run: kill $BACKEND_PID $FRONTEND_PID"

# Save PIDs for stop script
echo $BACKEND_PID > .backend.pid
echo $FRONTEND_PID > .frontend.pid

# Keep script running and handle Ctrl+C
trap 'echo ""; print_status "Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; rm -f .backend.pid .frontend.pid; print_success "Servers stopped."; exit 0' INT

print_status "Press Ctrl+C to stop all servers"
wait
