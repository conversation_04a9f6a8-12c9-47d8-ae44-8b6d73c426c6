# API Configuration File
# Replace the placeholder values with your actual API keys

# DeepL Translation API Key
# Get from: https://www.deepl.com/pro-api
DEEPL_AUTH_KEY = "cbbf5b5a-76cf-48c5-babd-9316da80307a:fx"

# ElevenLabs API Key for Text-to-Speech
# Get from: https://elevenlabs.io/
ELEVENLABS_API_KEY = "***************************************************"

# Multiple Unsplash API Keys for Photo Search (Primary + Backups)
# Get from: https://unsplash.com/developers
UNSPLASH_API_KEYS = [
    "*******************************************",  # Primary key
    "Replace with your Unsplash API key",   # Backup key 1
    "Replace with your Unsplash API key",    # Backup key 2
    "Replace with your Unsplash API key",     # Backup key 3
    "Replace with your Unsplash API key",      # Backup key 4
    "Replace with your Unsplash API key",       # Backup key 5
    "Replace with your Unsplash API key",        # Backup key 6
    "Replace with your Unsplash API key",         # Backup key 7
    "Replace with your Unsplash API key",          # Backup key 8
    "Replace with your Unsplash API key",           # Backup key 9
    "Replace with your Unsplash API key",            # Backup key 10
]

# Pixabay API Key
# Get from: https://pixabay.com/api/docs/
PIXABAY_API_KEY = "**********************************"

# Pexels API Key
# Get from: https://www.pexels.com/api/
PEXELS_API_KEY = "AVuuzPtVI88itmxrxxDmJdWwwoJEsjtdX0c66wtTVzQ9tubyWdA2t9Vs"

# Emoji API Key (if using external emoji service)
EMOJI_API_KEY = "Replace with your emoji_api API key"

# OpenAI API Key (if using GPT features)
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

# Consolidated API Keys Dictionary (for backward compatibility)
API_KEYS = {
    "unsplash": UNSPLASH_API_KEYS,
    "pixabay": PIXABAY_API_KEY,
    "pexels": PEXELS_API_KEY,
    "emoji_api": EMOJI_API_KEY,
    "openai": OPENAI_API_KEY,
}
