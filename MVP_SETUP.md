# 🎬 Xvion MVP - Quick Setup Guide

Welcome to the Xvion Arabic Quiz Video Generator MVP! This guide will help you get the system up and running quickly for demonstration purposes.

## 🚀 Quick Start (Recommended)

### 1. Prerequisites
- **Python 3.8+** (check with `python3 --version`)
- **Node.js 18+** (check with `node --version`)
- **PostgreSQL 14+** (for database)
- **FFmpeg** (for video processing)

### 2. One-Command Setup
```bash
# Make the startup script executable and run it
chmod +x start-mvp.sh
./start-mvp.sh
```

The script will:
- ✅ Check all dependencies
- ✅ Install missing Node.js packages
- ✅ Initialize the database
- ✅ Start both backend and frontend servers
- ✅ Show you the access URLs

### 3. Access the Application
Once started, you can access:
- **🖥️ Frontend Dashboard**: http://localhost:3000
- **📖 API Documentation**: http://localhost:8001/docs
- **🔍 Health Check**: http://localhost:8001/health

### 4. Stop the Servers
```bash
./stop-mvp.sh
```

## 🛠️ Manual Setup (If Needed)

### Database Setup
```bash
# Install PostgreSQL (macOS)
brew install postgresql@14
brew services start postgresql@14

# Create database
createdb xvion_db

# Initialize database
python3 database.py
```

### Backend Setup
```bash
# Install Python dependencies
pip3 install -r requirements.txt

# Start API server
python3 api_server.py
```

### Frontend Setup
```bash
# Navigate to frontend directory
cd xvion-frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

## 🎯 MVP Features Available

### ✅ Working Features
1. **Dashboard**: Overview of system status and statistics
2. **Quiz Management**: View and manage quiz data
3. **Database Integration**: PostgreSQL with sample data
4. **API Endpoints**: RESTful API with documentation
5. **Modern UI**: React-based frontend with Bootstrap styling
6. **Real-time Updates**: Live data from backend

### 🚧 MVP Limitations
- Video generation is not fully implemented (requires additional setup)
- Authentication is basic (for demo purposes)
- Some advanced features are placeholders

## 📊 Sample Data

The MVP comes with sample quiz data:
- **Arabic Animals Quiz**: 6 questions about animals in Arabic
- **Account Templates**: Pre-configured account settings
- **App Settings**: Default configuration values

## 🔧 Configuration

### API Keys (Optional for MVP)
The system includes API key placeholders in `config.py`:
- DeepL (translation)
- ElevenLabs (text-to-speech)
- Unsplash/Pexels/Pixabay (images)
- OpenAI (AI features)

For the MVP demo, these are not required for basic functionality.

### Database Configuration
Default connection: `postgresql://mohamedshady@localhost:5432/xvion_db`

To change, update the `DATABASE_URL` in `database.py`.

## 🐛 Troubleshooting

### Common Issues

1. **"Database connection failed"**
   ```bash
   # Start PostgreSQL
   brew services start postgresql@14
   
   # Create database if it doesn't exist
   createdb xvion_db
   ```

2. **"Port already in use"**
   ```bash
   # Kill processes on ports 3000 and 8001
   ./stop-mvp.sh
   ```

3. **"Python dependencies missing"**
   ```bash
   pip3 install -r requirements.txt
   ```

4. **"Node modules not found"**
   ```bash
   cd xvion-frontend
   npm install
   ```

### Logs
Check the logs for detailed error information:
- Backend: `logs/backend.log`
- Frontend: `logs/frontend.log`

## 📱 Using the MVP

### Dashboard
- View system statistics
- Monitor recent activity
- Quick navigation to other sections

### Quiz Management
- Browse existing quizzes
- View quiz details
- See question counts and metadata

### API Documentation
Visit http://localhost:8001/docs to explore:
- All available endpoints
- Request/response schemas
- Interactive API testing

## 🎉 Success Indicators

Your MVP is working correctly if you can:
1. ✅ Access the dashboard at http://localhost:3000
2. ✅ See sample quiz data in the Quiz Management section
3. ✅ View API documentation at http://localhost:8001/docs
4. ✅ Get a successful response from http://localhost:8001/health

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the logs in the `logs/` directory
3. Ensure all prerequisites are installed
4. Try the manual setup steps

---

**🎬 Xvion MVP - Ready for Demo!**
