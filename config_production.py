"""
Production Configuration Module
Handles environment variables and production settings
"""
import os
from typing import List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Base configuration class"""
    
    # Environment
    ENVIRONMENT = os.getenv('PYTHON_ENV', 'development')
    DEBUG = ENVIRONMENT == 'development'
    
    # Security
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    JWT_SECRET = os.getenv('JWT_SECRET', 'jwt-secret-change-in-production')
    
    # Database
    DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql+psycopg://mohamedshady@localhost:5432/xvion_db')
    
    # API Keys
    DEEPL_AUTH_KEY = os.getenv('DEEPL_AUTH_KEY', '')
    ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY', '')
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    
    # Image API Keys
    UNSPLASH_API_KEYS = [
        os.getenv('UNSPLASH_API_KEY_1', ''),
        os.getenv('UNSPLASH_API_KEY_2', ''),
        os.getenv('UNSPLASH_API_KEY_3', ''),
    ]
    PIXABAY_API_KEY = os.getenv('PIXABAY_API_KEY', '')
    PEXELS_API_KEY = os.getenv('PEXELS_API_KEY', '')
    
    # Server Configuration
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('BACKEND_PORT', 8001))
    
    # CORS
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', 'http://localhost:3000').split(',')
    
    # File Storage
    UPLOAD_DIR = os.getenv('UPLOAD_DIR', './uploads')
    OUTPUT_DIR = os.getenv('OUTPUT_DIR', './output')
    ASSETS_DIR = os.getenv('ASSETS_DIR', './assets')
    MAX_FILE_SIZE = os.getenv('MAX_FILE_SIZE', '100MB')
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', './logs/app.log')
    
    # Redis
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS = int(os.getenv('RATE_LIMIT_REQUESTS', 100))
    RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', 3600))
    
    # Video Processing
    MAX_VIDEO_DURATION = int(os.getenv('MAX_VIDEO_DURATION', 300))
    MAX_CONCURRENT_JOBS = int(os.getenv('MAX_CONCURRENT_JOBS', 3))
    CLEANUP_TEMP_FILES = os.getenv('CLEANUP_TEMP_FILES', 'true').lower() == 'true'
    
    # Email Configuration
    SMTP_HOST = os.getenv('SMTP_HOST', 'smtp.gmail.com')
    SMTP_PORT = int(os.getenv('SMTP_PORT', 587))
    SMTP_USER = os.getenv('SMTP_USER', '')
    SMTP_PASSWORD = os.getenv('SMTP_PASSWORD', '')
    FROM_EMAIL = os.getenv('FROM_EMAIL', '<EMAIL>')
    
    # Monitoring
    SENTRY_DSN = os.getenv('SENTRY_DSN', '')
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """Validate required configuration values"""
        errors = []
        
        if not cls.SECRET_KEY or cls.SECRET_KEY == 'dev-secret-key-change-in-production':
            errors.append("SECRET_KEY must be set in production")
            
        if not cls.DATABASE_URL:
            errors.append("DATABASE_URL must be set")
            
        if cls.ENVIRONMENT == 'production':
            if not cls.DEEPL_AUTH_KEY:
                errors.append("DEEPL_AUTH_KEY is required in production")
            if not cls.ELEVENLABS_API_KEY:
                errors.append("ELEVENLABS_API_KEY is required in production")
            if not any(cls.UNSPLASH_API_KEYS):
                errors.append("At least one UNSPLASH_API_KEY is required in production")
                
        return errors

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'
    
    # Additional production-specific settings
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DATABASE_URL = 'postgresql://localhost:5432/xvion_test_db'

# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
}

def get_config() -> Config:
    """Get configuration based on environment"""
    env = os.getenv('PYTHON_ENV', 'development')
    return config_map.get(env, DevelopmentConfig)

# Export current config
current_config = get_config()
