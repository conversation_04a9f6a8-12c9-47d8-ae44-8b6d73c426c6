# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
output/
photos/
text_boxes/
*.mp4
*.avi
*.mov
*.mkv
background_video_stable.mp4

# Logs
*.log

# Temporary files
*.tmp
*.temp

# API Keys (keep template)
.env
config_local.py

# Large assets (optional - uncomment if you want to exclude)
# assets/Back Videos/
# assets/Fonts/
