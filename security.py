"""
Security Manager for Xvion Application
Handles authentication, authorization, input validation, and security monitoring
"""
import hashlib
import secrets
import jwt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import re
from functools import wraps
import logging

from config_production import current_config as config
from logging_config import app_logger

logger = logging.getLogger(__name__)

class SecurityManager:
    """Centralized security management"""
    
    def __init__(self):
        self.secret_key = config.SECRET_KEY
        self.jwt_secret = config.JWT_SECRET
        self.token_expiry = timedelta(hours=24)
    
    def generate_token(self, user_data: Dict[str, Any]) -> str:
        """Generate JWT token for user"""
        try:
            payload = {
                'user_id': user_data.get('user_id'),
                'email': user_data.get('email'),
                'role': user_data.get('role', 'user'),
                'exp': datetime.utcnow() + self.token_expiry,
                'iat': datetime.utcnow(),
                'iss': 'xvion-api'
            }
            
            token = jwt.encode(payload, self.jwt_secret, algorithm='HS256')
            app_logger.log_security_event('token_generated', f"User: {user_data.get('user_id')}")
            return token
            
        except Exception as e:
            app_logger.log_security_event('token_generation_failed', str(e), 'error')
            raise
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            
            # Check if token is expired
            if datetime.utcnow() > datetime.fromtimestamp(payload['exp']):
                raise jwt.ExpiredSignatureError("Token has expired")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            app_logger.log_security_event('token_expired', 'Token verification failed', 'warning')
            raise
        except jwt.InvalidTokenError as e:
            app_logger.log_security_event('invalid_token', str(e), 'warning')
            raise
    
    def hash_password(self, password: str) -> str:
        """Hash password with salt"""
        salt = secrets.token_hex(32)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        try:
            salt, stored_hash = hashed_password.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash.hex() == stored_hash
        except Exception:
            return False
    
    def generate_api_key(self) -> str:
        """Generate secure API key"""
        return secrets.token_urlsafe(32)
    
    def validate_input(self, input_data: Dict[str, Any], rules: Dict[str, Dict]) -> List[str]:
        """Validate input data against rules"""
        errors = []
        
        for field, rule in rules.items():
            value = input_data.get(field)
            
            # Required field check
            if rule.get('required', False) and not value:
                errors.append(f"{field} is required")
                continue
            
            if value is None:
                continue
            
            # Type validation
            expected_type = rule.get('type')
            if expected_type and not isinstance(value, expected_type):
                errors.append(f"{field} must be of type {expected_type.__name__}")
                continue
            
            # String validations
            if isinstance(value, str):
                min_length = rule.get('min_length')
                max_length = rule.get('max_length')
                pattern = rule.get('pattern')
                
                if min_length and len(value) < min_length:
                    errors.append(f"{field} must be at least {min_length} characters")
                
                if max_length and len(value) > max_length:
                    errors.append(f"{field} must be at most {max_length} characters")
                
                if pattern and not re.match(pattern, value):
                    errors.append(f"{field} format is invalid")
            
            # Numeric validations
            if isinstance(value, (int, float)):
                min_value = rule.get('min_value')
                max_value = rule.get('max_value')
                
                if min_value is not None and value < min_value:
                    errors.append(f"{field} must be at least {min_value}")
                
                if max_value is not None and value > max_value:
                    errors.append(f"{field} must be at most {max_value}")
        
        return errors
    
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename to prevent path traversal"""
        # Remove path separators and dangerous characters
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        filename = re.sub(r'\.\.', '', filename)  # Remove parent directory references
        filename = filename.strip('. ')  # Remove leading/trailing dots and spaces
        
        # Ensure filename is not empty
        if not filename:
            filename = f"file_{secrets.token_hex(8)}"
        
        return filename
    
    def check_file_type(self, filename: str, allowed_types: List[str]) -> bool:
        """Check if file type is allowed"""
        if not filename:
            return False
        
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        return file_ext in [ext.lower() for ext in allowed_types]
    
    def rate_limit_key(self, identifier: str, action: str) -> str:
        """Generate rate limit key"""
        return f"rate_limit:{action}:{identifier}"
    
    def log_security_violation(self, violation_type: str, details: Dict[str, Any]):
        """Log security violations"""
        app_logger.log_security_event(
            violation_type,
            f"Details: {details}",
            'warning'
        )

class InputValidator:
    """Input validation rules and methods"""
    
    # Common validation patterns
    EMAIL_PATTERN = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    PHONE_PATTERN = r'^\+?1?\d{9,15}$'
    UUID_PATTERN = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    
    @staticmethod
    def quiz_validation_rules() -> Dict[str, Dict]:
        """Validation rules for quiz creation"""
        return {
            'title': {
                'required': True,
                'type': str,
                'min_length': 1,
                'max_length': 200
            },
            'description': {
                'type': str,
                'max_length': 1000
            },
            'language': {
                'type': str,
                'pattern': r'^[a-z]{2}$'
            },
            'questions': {
                'required': True,
                'type': list
            }
        }
    
    @staticmethod
    def video_generation_rules() -> Dict[str, Dict]:
        """Validation rules for video generation"""
        return {
            'quiz_id': {
                'required': True,
                'type': str,
                'pattern': InputValidator.UUID_PATTERN
            },
            'style': {
                'type': str,
                'max_length': 50
            },
            'quality': {
                'type': str,
                'pattern': r'^(sd|hd|fhd|4k)$'
            }
        }
    
    @staticmethod
    def user_registration_rules() -> Dict[str, Dict]:
        """Validation rules for user registration"""
        return {
            'email': {
                'required': True,
                'type': str,
                'pattern': InputValidator.EMAIL_PATTERN
            },
            'password': {
                'required': True,
                'type': str,
                'min_length': 8,
                'max_length': 128
            },
            'name': {
                'required': True,
                'type': str,
                'min_length': 1,
                'max_length': 100
            }
        }

def require_auth(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # This would be implemented based on your authentication system
        return f(*args, **kwargs)
    return decorated_function

def require_role(role: str):
    """Decorator to require specific role"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # This would be implemented based on your authorization system
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Export security manager instance
security_manager = SecurityManager()
input_validator = InputValidator()
