"""
Production-Ready API Server for Xvion Video Generator
Includes security, logging, error handling, and monitoring
"""
import os
import logging
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, Request, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
import uvicorn
from sqlalchemy.orm import Session
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware

# Import our modules
from config_production import current_config as config
from database import get_db, engine, Base
import logging_config
from security import SecurityManager
from auth import auth_router
from auth_middleware import SecurityHeadersMiddleware, require_auth, get_current_user

# Initialize logging
logger = logging.getLogger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Security manager
security = SecurityManager()

# Video processor (placeholder - implement actual video processing)
# video_processor = VideoProcessor()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Xvion API Server...")
    
    # Note: Database tables are now managed by Alembic migrations
    # Run migrations before starting the server: python migrate.py upgrade
    
    # Validate configuration
    config_errors = config.validate_config()
    if config_errors:
        logger.error(f"Configuration errors: {config_errors}")
        if config.ENVIRONMENT == 'production':
            raise RuntimeError("Invalid production configuration")
    
    # Initialize video processor (placeholder)
    # await video_processor.initialize()

    logger.info("Xvion API Server started successfully")
    yield

    # Shutdown
    logger.info("Shutting down Xvion API Server...")
    # await video_processor.cleanup()
    logger.info("Xvion API Server shutdown complete")

# Create FastAPI app
app = FastAPI(
    title="Xvion Video Generator API",
    description="Production-ready API for Arabic quiz video generation",
    version="2.0.0",
    docs_url="/docs" if config.DEBUG else None,
    redoc_url="/redoc" if config.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
app.add_middleware(SlowAPIMiddleware)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Security headers middleware
app.add_middleware(SecurityHeadersMiddleware)

# Trusted host middleware for production
if config.ENVIRONMENT == 'production':
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*.xvion.com", "localhost"]
    )

# Include authentication router
app.include_router(auth_router, prefix="/api")

# Request/Response models
class QuizCreateRequest(BaseModel):
    title: str
    description: str
    questions: List[Dict[str, Any]]
    language: str = "ar"

class VideoGenerateRequest(BaseModel):
    quiz_id: str
    style: str = "default"
    quality: str = "hd"

class ErrorResponse(BaseModel):
    error: str
    message: str
    timestamp: datetime
    request_id: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Dependency for authentication
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
    """Validate JWT token and return user info"""
    try:
        payload = security.verify_token(credentials.credentials)
        return payload
    except Exception as e:
        logger.warning(f"Authentication failed: {e}")
        raise HTTPException(status_code=401, detail="Invalid authentication credentials")

# Error handlers
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    logger.warning(f"Validation error: {exc}")
    return JSONResponse(
        status_code=422,
        content=ErrorResponse(
            error="validation_error",
            message="Invalid request data",
            timestamp=datetime.utcnow(),
            request_id=getattr(request.state, 'request_id', None)
        ).dict()
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    logger.warning(f"HTTP exception: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error="http_error",
            message=exc.detail,
            timestamp=datetime.utcnow(),
            request_id=getattr(request.state, 'request_id', None)
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="internal_error",
            message="An internal server error occurred",
            timestamp=datetime.utcnow(),
            request_id=getattr(request.state, 'request_id', None)
        ).dict()
    )

# Middleware for request logging and tracking
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all requests and add request ID"""
    import uuid
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    start_time = datetime.utcnow()
    logger.info(f"Request {request_id}: {request.method} {request.url}")
    
    response = await call_next(request)
    
    duration = (datetime.utcnow() - start_time).total_seconds()
    logger.info(f"Request {request_id} completed in {duration:.3f}s with status {response.status_code}")
    
    response.headers["X-Request-ID"] = request_id
    return response

# Health check endpoint
@app.get("/health")
@limiter.limit("10/minute")
async def health_check(request: Request):
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": "2.0.0",
        "environment": config.ENVIRONMENT
    }

# API Routes
@app.get("/api/quizzes")
@limiter.limit("30/minute")
async def get_quizzes(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(require_auth)
):
    """Get all quizzes with pagination"""
    try:
        # Get quizzes created by the current user
        from database import Quiz as DBQuiz
        quizzes = db.query(DBQuiz).filter(DBQuiz.created_by == current_user.id).offset(skip).limit(limit).all()
        total = db.query(DBQuiz).filter(DBQuiz.created_by == current_user.id).count()
        return {"quizzes": quizzes, "total": total}
    except Exception as e:
        logger.error(f"Error fetching quizzes: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch quizzes")

@app.post("/api/quizzes")
@limiter.limit("10/minute")
async def create_quiz(
    request: Request,
    quiz_data: QuizCreateRequest,
    db: Session = Depends(get_db),
    current_user = Depends(require_auth)
):
    """Create a new quiz"""
    try:
        # Validate quiz data
        if not quiz_data.title or not quiz_data.questions:
            raise HTTPException(status_code=400, detail="Title and questions are required")
        
        # Create quiz using the database model
        from database import Quiz as DBQuiz, Question as DBQuestion

        db_quiz = DBQuiz(
            title=quiz_data.title,
            description=quiz_data.description,
            language=quiz_data.language,
            created_by=current_user.id
        )
        
        db.add(db_quiz)
        db.flush()  # Get the quiz ID

        # Create questions
        for i, question_data in enumerate(quiz_data.questions):
            db_question = DBQuestion(
                quiz_id=db_quiz.id,
                text=question_data.get('text', ''),
                options=question_data.get('options', []),
                correct_answer=question_data.get('correct_answer', ''),
                explanation=question_data.get('explanation', ''),
                order_index=i
            )
            db.add(db_question)

        db.commit()
        db.refresh(db_quiz)

        logger.info(f"Quiz created: {db_quiz.id} by user: {current_user.id}")
        return {"quiz": db_quiz, "message": "Quiz created successfully"}
        
    except Exception as e:
        logger.error(f"Error creating quiz: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create quiz")

# Video generation endpoints
@app.post("/api/videos/generate")
@limiter.limit("5/minute")
async def generate_video(
    request: Request,
    video_request: dict,
    db: Session = Depends(get_db),
    current_user = Depends(require_auth)
):
    """Generate video from quiz"""
    try:
        quiz_id = video_request.get('quiz_id')
        if not quiz_id:
            raise HTTPException(status_code=400, detail="Quiz ID is required")

        # Check if quiz exists and belongs to the user
        from database import Quiz as DBQuiz
        quiz = db.query(DBQuiz).filter(
            DBQuiz.id == quiz_id,
            DBQuiz.created_by == current_user.id
        ).first()
        if not quiz:
            raise HTTPException(status_code=404, detail="Quiz not found or access denied")

        # Create video job record
        from database import VideoJob as DBVideoJob
        job = DBVideoJob(
            quiz_id=quiz_id,
            created_by=current_user.id,
            status="pending",
            config=video_request  # Store the video generation config
        )
        db.add(job)
        db.commit()

        logger.info(f"Video generation job created: {job.id}")

        return {
            "job_id": job.id,
            "status": "pending",
            "message": "Video generation started",
            "quiz_title": quiz.title
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting video generation: {e}")
        raise HTTPException(status_code=500, detail="Failed to start video generation")

# Job status endpoints
@app.get("/api/jobs/{job_id}")
async def get_job_status(
    job_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(require_auth)
):
    """Get job status"""
    from database import VideoJob as DBVideoJob
    job = db.query(DBVideoJob).filter(
        DBVideoJob.id == job_id,
        DBVideoJob.created_by == current_user.id
    ).first()

    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    return {
        "job_id": job.id,
        "status": job.status,
        "progress": job.progress or 0,
        "result": job.result,
        "error": job.error,
        "created_at": job.created_at,
        "updated_at": job.updated_at
    }

@app.get("/api/jobs")
async def get_user_jobs(
    db: Session = Depends(get_db),
    current_user = Depends(require_auth),
    skip: int = 0,
    limit: int = 20
):
    """Get user's jobs"""
    from database import VideoJob as DBVideoJob, Quiz as DBQuiz
    jobs = db.query(DBVideoJob).filter(
        DBVideoJob.created_by == current_user.id
    ).order_by(DBVideoJob.created_at.desc()).offset(skip).limit(limit).all()

    return {
        "jobs": [
            {
                "job_id": job.id,
                "quiz_id": job.quiz_id,
                "status": job.status,
                "progress": job.progress or 0,
                "created_at": job.created_at,
                "updated_at": job.updated_at
            }
            for job in jobs
        ],
        "total": db.query(DBVideoJob).filter(DBVideoJob.created_by == current_user.id).count()
    }

@app.get("/api/videos/jobs")
async def get_video_jobs(
    db: Session = Depends(get_db),
    current_user = Depends(require_auth),
    skip: int = 0,
    limit: int = 20
):
    """Get user's video jobs"""
    from database import VideoJob as DBVideoJob, Quiz as DBQuiz
    jobs = db.query(DBVideoJob).join(DBQuiz, DBVideoJob.quiz_id == DBQuiz.id).filter(
        DBVideoJob.created_by == current_user.id
    ).order_by(DBVideoJob.created_at.desc()).offset(skip).limit(limit).all()

    return {
        "items": [
            {
                "id": job.id,
                "quiz_id": job.quiz_id,
                "quiz_title": job.quiz.title if job.quiz else "Unknown Quiz",
                "status": job.status,
                "progress": job.progress or 0,
                "output_path": job.output_path,
                "error_message": job.error_message,
                "created_at": job.created_at,
                "updated_at": job.updated_at,
                "started_at": job.started_at,
                "completed_at": job.completed_at
            }
            for job in jobs
        ],
        "total": db.query(DBVideoJob).filter(DBVideoJob.created_by == current_user.id).count()
    }

# Settings endpoints
@app.get("/api/settings")
async def get_settings(current_user = Depends(require_auth)):
    """Get application settings"""
    return {
        "video_quality_options": ["sd", "hd", "fhd", "4k"],
        "supported_languages": ["ar", "en", "fr", "es"],
        "max_file_size_mb": getattr(config, 'MAX_FILE_SIZE_MB', 100),
        "supported_file_types": ["csv", "json", "xlsx"],
        "api_status": {
            "deepl": bool(getattr(config, 'DEEPL_AUTH_KEY', None)),
            "elevenlabs": bool(getattr(config, 'ELEVENLABS_API_KEY', None)),
            "unsplash": bool(getattr(config, 'UNSPLASH_API_KEY_1', None)),
            "pixabay": bool(getattr(config, 'PIXABAY_API_KEY', None)),
            "pexels": bool(getattr(config, 'PEXELS_API_KEY', None))
        }
    }

if __name__ == "__main__":
    # Ensure directories exist
    os.makedirs(config.UPLOAD_DIR, exist_ok=True)
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # Run server
    uvicorn.run(
        "api_server_production:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.DEBUG,
        log_level=config.LOG_LEVEL.lower(),
        access_log=True
    )
