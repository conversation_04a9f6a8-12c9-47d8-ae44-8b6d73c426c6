#!/usr/bin/env python3
"""
Database Migration Management Script for Xvion
Provides easy commands for managing database migrations
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config_production import current_config as config
from database import engine, Base
from sqlalchemy import text
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MigrationManager:
    """Manages database migrations using Alembic"""
    
    def __init__(self):
        self.alembic_cfg = "alembic.ini"
        self.ensure_alembic_setup()
    
    def ensure_alembic_setup(self):
        """Ensure Alembic is properly set up"""
        if not os.path.exists(self.alembic_cfg):
            logger.error("alembic.ini not found. Please run 'python migrate.py init' first.")
            sys.exit(1)
        
        if not os.path.exists("alembic"):
            logger.error("alembic directory not found. Please run 'python migrate.py init' first.")
            sys.exit(1)
    
    def run_alembic_command(self, command: list):
        """Run an Alembic command"""
        try:
            # Set environment variable for database URL
            env = os.environ.copy()
            env['DATABASE_URL'] = config.DATABASE_URL
            
            cmd = ["alembic"] + command
            logger.info(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                check=True
            )
            
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print(result.stderr, file=sys.stderr)
                
            return result
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {e}")
            if e.stdout:
                print(e.stdout)
            if e.stderr:
                print(e.stderr, file=sys.stderr)
            sys.exit(1)
        except FileNotFoundError:
            logger.error("Alembic not found. Please install it: pip install alembic")
            sys.exit(1)
    
    def init(self):
        """Initialize Alembic in the project"""
        logger.info("Initializing Alembic...")
        
        if os.path.exists("alembic"):
            logger.warning("Alembic already initialized")
            return
        
        # Initialize Alembic
        subprocess.run(["alembic", "init", "alembic"], check=True)
        logger.info("Alembic initialized successfully")
    
    def create_migration(self, message: str, autogenerate: bool = True):
        """Create a new migration"""
        logger.info(f"Creating migration: {message}")
        
        command = ["revision"]
        if autogenerate:
            command.append("--autogenerate")
        command.extend(["-m", message])
        
        self.run_alembic_command(command)
        logger.info("Migration created successfully")
    
    def upgrade(self, revision: str = "head"):
        """Upgrade database to a revision"""
        logger.info(f"Upgrading database to: {revision}")
        self.run_alembic_command(["upgrade", revision])
        logger.info("Database upgraded successfully")
    
    def downgrade(self, revision: str):
        """Downgrade database to a revision"""
        logger.info(f"Downgrading database to: {revision}")
        self.run_alembic_command(["downgrade", revision])
        logger.info("Database downgraded successfully")
    
    def current(self):
        """Show current revision"""
        logger.info("Current database revision:")
        self.run_alembic_command(["current"])
    
    def history(self):
        """Show migration history"""
        logger.info("Migration history:")
        self.run_alembic_command(["history"])
    
    def show(self, revision: str):
        """Show details of a specific revision"""
        logger.info(f"Showing revision: {revision}")
        self.run_alembic_command(["show", revision])
    
    def stamp(self, revision: str):
        """Stamp database with a revision without running migrations"""
        logger.info(f"Stamping database with revision: {revision}")
        self.run_alembic_command(["stamp", revision])
    
    def check_connection(self):
        """Check database connection"""
        try:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            logger.info("✅ Database connection successful")
            return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
    
    def create_initial_migration(self):
        """Create initial migration with all current models"""
        logger.info("Creating initial migration...")
        
        # Check if any migrations exist
        versions_dir = Path("alembic/versions")
        if versions_dir.exists() and list(versions_dir.glob("*.py")):
            logger.warning("Migrations already exist. Use 'create' command for new migrations.")
            return
        
        self.create_migration("Initial migration - create all tables", autogenerate=True)
    
    def reset_database(self):
        """Reset database (DROP ALL TABLES and recreate)"""
        logger.warning("⚠️  This will DROP ALL TABLES in the database!")
        confirm = input("Are you sure? Type 'yes' to continue: ")
        
        if confirm.lower() != 'yes':
            logger.info("Operation cancelled")
            return
        
        try:
            # Drop all tables
            Base.metadata.drop_all(bind=engine)
            logger.info("All tables dropped")
            
            # Recreate tables
            Base.metadata.create_all(bind=engine)
            logger.info("All tables recreated")
            
            # Stamp with head revision
            self.stamp("head")
            logger.info("Database reset and stamped with head revision")
            
        except Exception as e:
            logger.error(f"Error resetting database: {e}")
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="Database Migration Management")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Init command
    subparsers.add_parser("init", help="Initialize Alembic")
    
    # Create migration command
    create_parser = subparsers.add_parser("create", help="Create a new migration")
    create_parser.add_argument("message", help="Migration message")
    create_parser.add_argument("--no-autogenerate", action="store_true", 
                              help="Don't auto-generate migration content")
    
    # Upgrade command
    upgrade_parser = subparsers.add_parser("upgrade", help="Upgrade database")
    upgrade_parser.add_argument("revision", nargs="?", default="head", 
                               help="Target revision (default: head)")
    
    # Downgrade command
    downgrade_parser = subparsers.add_parser("downgrade", help="Downgrade database")
    downgrade_parser.add_argument("revision", help="Target revision")
    
    # Status commands
    subparsers.add_parser("current", help="Show current revision")
    subparsers.add_parser("history", help="Show migration history")
    
    # Show command
    show_parser = subparsers.add_parser("show", help="Show revision details")
    show_parser.add_argument("revision", help="Revision to show")
    
    # Stamp command
    stamp_parser = subparsers.add_parser("stamp", help="Stamp database with revision")
    stamp_parser.add_argument("revision", help="Revision to stamp")
    
    # Utility commands
    subparsers.add_parser("check", help="Check database connection")
    subparsers.add_parser("initial", help="Create initial migration")
    subparsers.add_parser("reset", help="Reset database (DROP ALL TABLES)")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = MigrationManager()
    
    # Check database connection for most commands
    if args.command not in ["init", "check"]:
        if not manager.check_connection():
            logger.error("Cannot proceed without database connection")
            sys.exit(1)
    
    # Execute commands
    if args.command == "init":
        manager.init()
    elif args.command == "create":
        manager.create_migration(args.message, not args.no_autogenerate)
    elif args.command == "upgrade":
        manager.upgrade(args.revision)
    elif args.command == "downgrade":
        manager.downgrade(args.revision)
    elif args.command == "current":
        manager.current()
    elif args.command == "history":
        manager.history()
    elif args.command == "show":
        manager.show(args.revision)
    elif args.command == "stamp":
        manager.stamp(args.revision)
    elif args.command == "check":
        manager.check_connection()
    elif args.command == "initial":
        manager.create_initial_migration()
    elif args.command == "reset":
        manager.reset_database()

if __name__ == "__main__":
    main()
