#!/bin/bash

# Xvion MVP Stop Script
# This script stops both the backend API server and frontend development server

echo "🛑 Stopping Xvion MVP servers..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to kill process by PID
kill_process() {
    local pid=$1
    local name=$2
    
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        print_status "Stopping $name (PID: $pid)..."
        kill "$pid" 2>/dev/null
        
        # Wait up to 5 seconds for graceful shutdown
        for i in {1..5}; do
            if ! kill -0 "$pid" 2>/dev/null; then
                print_success "$name stopped successfully"
                return 0
            fi
            sleep 1
        done
        
        # Force kill if still running
        print_warning "Force killing $name..."
        kill -9 "$pid" 2>/dev/null
        if ! kill -0 "$pid" 2>/dev/null; then
            print_success "$name force stopped"
        else
            print_error "Failed to stop $name"
            return 1
        fi
    else
        print_warning "$name is not running or PID not found"
    fi
}

# Stop servers using saved PIDs
if [ -f ".backend.pid" ]; then
    BACKEND_PID=$(cat .backend.pid)
    kill_process "$BACKEND_PID" "Backend server"
    rm -f .backend.pid
fi

if [ -f ".frontend.pid" ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    kill_process "$FRONTEND_PID" "Frontend server"
    rm -f .frontend.pid
fi

# Also try to kill any remaining processes by name/port
print_status "Checking for any remaining processes..."

# Kill any Python processes running api_server.py
pkill -f "python.*api_server.py" 2>/dev/null && print_status "Killed remaining backend processes"

# Kill any Node processes running on port 3000
lsof -ti:3000 | xargs kill -9 2>/dev/null && print_status "Killed processes on port 3000"

# Kill any Python processes running on port 8001
lsof -ti:8001 | xargs kill -9 2>/dev/null && print_status "Killed processes on port 8001"

print_success "✅ All Xvion MVP servers have been stopped"
echo ""
echo "To start the servers again, run: ./start-mvp.sh"
