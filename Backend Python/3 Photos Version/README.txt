# A code that creates a 6 questions quiz videos along with zooming background and 3 call to actions,
# Like CTA in the 33rd second of the original video, and a Rader CTA in the 11th second of the original video,
# And a Save CTA in the 55th second of the original video.
# And this code accepts 1 CSV file, Or 4 CSV files, if one CSV file is provided it will randomly choose
# 6 questions from this file, if 4 CSV files is provided it will randomly choose 1 question from the Q1 CSV file,
# 1 random question from Q2 csv file, 3 random questions ( without replications ) from Q3-Q5 Csv file, and 1 random question from Q6 CSV file
# It uses Elevenlabs for voiceover
# Dynamically displays the questions text on the video and Dynamically generate the voiceover
# ( depends on the number of questions you ask it to make )
# Contains the missmatch audio trick, use the voiceover for question 4 for both question 3 and question 4,
# ( only the voice of the questions, the answers are correct )
# No Hook
#
# EMOJI SELECTION LOGIC:
# Uses comprehensive emoji intelligence system with multiple search strategies:
# - STEP 0: Compound word matching (e.g., "blue whale" -> whale emoji)
# - STEP 0.5: Country flag matching (highest priority for country names)
# - STEP 1: Individual word literal matching from extensive emoji mapping
# - STEP 1.5: City fame associations (cities matched to what they're famous for)
# - STEP 2: Country fame associations (countries matched to famous landmarks/items)
# - STEP 3: Human emotional response matching (semantic groups and feelings)
# - Fallback system with variety of thinking/question emojis
# - Tracks used emojis per quiz to avoid repetition
# - Supports both standard and playful emoji styles per question
# - Translates non-English text to English for better emoji matching
#
# PHOTO SELECTION LOGIC:
# Uses direct option text search with translation enhancement:
# - Extracts clean option text (removes A), B), C) prefixes)
# - Automatically translates non-English text to English using DeepL
# - Searches multiple photo APIs (Unsplash, Pexels, Pixabay) using translated text
# - Multiple API key fallback system for Unsplash (10+ backup keys)
# - Filters inappropriate content (removes photos with certain keywords)
# - Downloads and processes images (resize, format conversion, quality optimization)
# - Fallback system: API photos -> verified fallback URLs -> generated placeholder images
# - Each question gets exactly 3 photos corresponding to the 3 answer options
# - Photos are positioned dynamically based on question text length
# - Comprehensive error handling and retry mechanisms for reliable photo acquisition

# It also create a question text box, options text box, options letters circles ,
# along with settings config to play with everything

# EXTERNAL POSITION CONFIQ