# you can change the emojis position by: Changing the x_offset in the EMOJI_CONFIG
# Adjusting the emoji_x position from in the video creation function


import os
import subprocess
import json
import shutil
import random
import random
import string
import cv2
import csv
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import inflect
import re
from langdetect import detect, DetectorFactory
from multiprocessing import Pool, Semaphore, freeze_support
from dotenv import load_dotenv
from elevenlabs import VoiceSettings
from elevenlabs.client import ElevenLabs
import soundfile as sf
import requests
import time
import urllib.parse
import hashlib
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import deepl
import arabic_reshaper
import bidi.algorithm
from bidi.algorithm import get_display
from quiz_positioning_config_arabic import (
    get_question_config,
    get_dynamic_photo_config,
    get_dynamic_text_box_config,
    get_dynamic_question_text_box_config,
    get_dynamic_emoji_config
)


def load_arabic_font(font_size, fallback_size=None):
    """Load Arabic font with proper fallback handling"""
    if fallback_size is None:
        fallback_size = font_size

    # List of Arabic font paths to try
    arabic_fonts = [
        "./Assets/NotoSansArabic-Bold.ttf",
        "./assets/NotoSansArabic-Bold.ttf",
        "NotoSansArabic-Bold.ttf",
        "./Assets/arial-unicode.ttf",
        "arial-unicode.ttf",
        "./Assets/DejaVuSans.ttf",
        "DejaVuSans.ttf"
    ]

    for font_path in arabic_fonts:
        try:
            font = ImageFont.truetype(font_path, font_size)
            # Test if font can handle Arabic characters
            test_img = Image.new('RGB', (100, 100), (255, 255, 255))
            test_draw = ImageDraw.Draw(test_img)
            test_draw.textbbox((0, 0), 'أ', font=font)  # Test with Arabic letter
            print(f"✅ Successfully loaded Arabic font: {font_path}")
            return font
        except Exception as e:
            print(f"❌ Failed to load font {font_path}: {e}")
            continue

    print("⚠️ No Arabic fonts found, using default with smaller size")
    try:
        return ImageFont.load_default()
    except:
        return None


def process_arabic_text(text):
    """Process Arabic text for proper display with reshaping and bidirectional support"""
    if not ARABIC_SUPPORT:
        return text

    try:
        # Check if text contains Arabic characters
        if any('\u0600' <= char <= '\u06FF' or '\u0750' <= char <= '\u077F' for char in text):
            # Reshape Arabic text (connect letters properly)
            reshaped_text = arabic_reshaper.reshape(text)
            # Handle bidirectional text (RTL)
            display_text = get_display(reshaped_text)
            return display_text
        else:
            return text
    except Exception as e:
        print(f"⚠️ Arabic text processing failed: {e}")
        return text




def measure_arabic_text_width(text, font_path, font_size):
    """Accurately measure Arabic text width for positioning"""
    try:
        # Load the font
        font = ImageFont.truetype(font_path, font_size)

        # Process Arabic text
        processed_text = process_arabic_text(text)

        # Create temporary image for measurement
        temp_img = Image.new('RGBA', (2000, 200), (0, 0, 0, 0))
        temp_draw = ImageDraw.Draw(temp_img)

        # Get accurate bounding box
        bbox = temp_draw.textbbox((0, 0), processed_text, font=font)
        width = bbox[2] - bbox[0]

        print(f"📐 Measured Arabic text '{text}' -> width: {width}px")
        return width

    except Exception as e:
        print(f"❌ Error measuring Arabic text width: {e}")
        # Fallback: estimate based on character count
        return len(text) * 25


# DeepL Translation Configuration
DEEPL_AUTH_KEY = "Replace with your DeepL API key"  # Replace with your DeepL API key


# Make language detection deterministic
DetectorFactory.seed = 0

# Arabic text support configuration
ARABIC_SUPPORT = True  # Set to False to disable Arabic text processing


# Load API key
ELEVENLABS_API_KEY = "Replace with your Elevenlabs API key" # Replace with your Elevenlabs API key
if not ELEVENLABS_API_KEY:
    raise ValueError("ELEVENLABS_API_KEY environment variable not set")
# Semaphore to limit ElevenLabs API requests to 2 at a time
elevenlabs_semaphore = Semaphore(2)

client = ElevenLabs(api_key=ELEVENLABS_API_KEY)

# UPDATED: API_KEYS dictionary with multiple Unsplash API keys
API_KEYS = {
    # Multiple Unsplash API keys for fallback
    "unsplash": [
        "Replace with your Unsplash API key",  # Primary key
        "Replace with your Unsplash API key",   # Backup key 1
        "Replace with your Unsplash API key",    # Backup key 2
        "Replace with your Unsplash API key",     # Backup key 3
        "Replace with your Unsplash API key",      # Backup key 4
        "Replace with your Unsplash API key",       # Backup key 5
        "Replace with your Unsplash API key",        # Backup key 6
        "Replace with your Unsplash API key",         # Backup key 7
        "Replace with your Unsplash API key",          # Backup key 8
        "Replace with your Unsplash API key",           # Backup key 9
        "Replace with your Unsplash API key",            # Backup key 10
    ],
    # Get from https://pixabay.com/api/docs/
    "pixabay": "Replace with your pixabay API key", # Replace with your pixabay API key
    "pexels": "Replace with your pexels API key",  # Replace with your pexels API key
    # Add more emoji-specific APIs
    "emoji_api": "Replace with your emoji_api API key", # Replace with your emoji_api API key
    "openai": "Replace with your openai API key", # Replace with your openai API key
}


def translate_to_english(text, auth_key=DEEPL_AUTH_KEY, source_language=None):
    """Translates text to English using DeepL with improved error handling and specified source language."""
    try:
        # Clean the text first
        clean_text = text.strip()
        if not clean_text:
            return text

        # Use provided source language or detect it
        if source_language:
            detected_lang = source_language
            print(f"🔍 Using provided source language '{detected_lang}' for: '{clean_text}'")
        else:
            try:
                detected_lang = detect(clean_text)
                print(f"🔍 Language detection for '{clean_text}': {detected_lang}")
            except Exception as lang_error:
                print(f"⚠️ Language detection failed for '{clean_text}': {lang_error}")
                detected_lang = 'unknown'

        # Skip translation only if confidently English
        if detected_lang == 'en':
            print(f"✅ Text '{clean_text}' detected as English, skipping translation")
            return text

        # Always attempt translation for non-English or uncertain cases
        print(f"🌐 Attempting translation for '{clean_text}' (source: {detected_lang})")

        translator = deepl.Translator(auth_key)
        result = translator.translate_text(clean_text, target_lang="EN-US")

        translated_text = result.text.strip()
        print(f"✅ Translation successful: '{clean_text}' → '{translated_text}'")
        return translated_text

    except Exception as e:
        print(f"❌ Translation Error for '{text}': {e}")
        print(f"   Error type: {type(e).__name__}")

        # Fallback: try simple word mappings for common Spanish words
        spanish_to_english = {
            'pez': 'fish',
            'rana': 'frog',
            'manzana': 'apple',
            'plátano': 'banana',
            'uva': 'grape',
            'perro': 'dog',
            'gato': 'cat',
            'casa': 'house',
            'agua': 'water',
            'fuego': 'fire',
            'tierra': 'earth',
            'aire': 'air',
            'sol': 'sun',
            'luna': 'moon',
            'estrella': 'star',
            'mar': 'sea',
            'montaña': 'mountain',
            'árbol': 'tree',
            'flor': 'flower',
            'pájaro': 'bird',
            'caballo': 'horse',
            'vaca': 'cow',
            'pollo': 'chicken',
            'cerdo': 'pig',
            'pescado': 'fish',
            'carne': 'meat',
            'verdura': 'vegetable',
            'fruta': 'fruit',
            'pan': 'bread',
            'leche': 'milk',
            'queso': 'cheese',
            'huevo': 'egg',
            'azúcar': 'sugar',
            'sal': 'salt',
            'aceite': 'oil',
            'mantequilla': 'butter',
            'arroz': 'rice',
            'pasta': 'pasta',
            'sopa': 'soup',
            'ensalada': 'salad',
            'postre': 'dessert',
            'café': 'coffee',
            'té': 'tea',
            'vino': 'wine',
            'cerveza': 'beer',
            'jugo': 'juice',
            'refresco': 'soda'
        }

        # Try fallback translation
        lower_text = text.lower().strip()
        if lower_text in spanish_to_english:
            fallback_translation = spanish_to_english[lower_text]
            print(f"🔄 Using fallback translation: '{text}' → '{fallback_translation}'")
            return fallback_translation

        print(f"🚫 No fallback available, returning original: '{text}'")
        return text  # Return original text if all translation attempts fail



def detect_language_of_first_question(question):
    """Detect the language of the first question to use for all translations in the quiz."""
    try:
        # Clean the question text first
        clean_question = question.strip().replace('\\n', ' ').replace('\n', ' ')
        if not clean_question:
            return 'en'  # Default to English

        detected_lang = detect(clean_question)
        print(f"🌐 FIRST QUESTION LANGUAGE DETECTION: '{clean_question[:50]}...' → {detected_lang}")
        return detected_lang

    except Exception as lang_error:
        print(f"⚠️ Language detection failed for first question: {lang_error}")
        return 'en'  # Default to English if detection fails


def validate_deepl_connection():
    """Validate DeepL API connection and provide diagnostics."""
    try:
        print("🔧 Testing DeepL API connection...")
        translator = deepl.Translator(DEEPL_AUTH_KEY)

        # Test with a simple Spanish word
        test_result = translator.translate_text("hola", target_lang="EN-US")
        print(f"✅ DeepL API test successful: 'hola' → '{test_result.text}'")

        # Check usage
        usage = translator.get_usage()
        print(f"📊 DeepL API usage: {usage.character.count}/{usage.character.limit} characters used")

        return True
    except Exception as e:
        print(f"❌ DeepL API test failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        print(f"   Check your API key: {DEEPL_AUTH_KEY[:10]}...")
        return False


# Photo storage
PHOTOS_DIR = "photos"

# Add this new configuration at the top to control letter circle strokes
LETTER_CIRCLE_STROKE_CONFIG = {
    "enabled": True,  # Set to False for no stroke, True for stroke
    "color": "white",  # Options: "white", "same_as_border", "random"
    "width": 5  # Stroke width (ignored if enabled=False)
}

# NEW: Account name text box configuration
ACCOUNT_NAME_TEXT_BOX_CONFIG = {
    "width": 1150,  # Width of account name text box
    "height": 120,  # Height of account name text box
    "x": -25,  # X position - centered (1920-800)/2 = 560
    "y": 160,  # Y position - top of video
    "corner_radius": 20,  # Rounded corner radius
    "border_width": 0,  # No border needed for gradient background
    "text_color": (255, 255, 255),  # White text
    "shadow_color": (0, 0, 0, 100),  # Black shadow with transparency
    "shadow_offset": (2, 2),  # Shadow offset (x, y)
    "font_size": 75,  # Font size for account name
    # "gradient_start": (30, 100, 255),  # Blue gradient start
    # "gradient_end": (0, 200, 255),  # Cyan gradient end
    "gradient_start": (0, 0, 0),  # Blue gradient start
    "gradient_end": (0, 0, 0),  # Cyan gradient end
}

# Updated Photo configuration for 3 photos per question
PHOTO_CONFIG = {
    "width": 250,  # Width of each individual photo
    "height": 250,  # Height of each individual photo
    "spacing": 30,  # Space between photos
    "total_width": 840,  # Total width for all 3 photos (400*3 + 20*2)
    "x_start": 140,  # Starting X position (centered: (1920-1240)/2)
    "y": 450,  # Y position (upper part of video)
}

# Emoji configuration with dynamic positioning
EMOJI_CONFIG = {
    "size": 120,  # Size of emoji images
    "x_offset": -20,  # UPDATED: Distance from the start of Arabic text (negative moves left, positive moves right)
    "y_base": 860,  # Base Y position (same as options)
    "spacing": 180,  # Vertical spacing between emojis
}



# Question text box configuration to match photo position
QUESTION_TEXT_BOX_CONFIG = {
    "width": 920,  # Width of question text box
    "height": 530,  # Height of question text box
    "x": 90,  # X position - centered
    "y": 235,  # Y position - upper part to match photo
    "corner_radius": 50,  # Rounded corner radius
    "border_width": 10,  # Border width
    "fill_color": (255, 255, 255),  # White fill
    # "text_color": (255, 255, 255),  # Black text
    "shadow_color": (0, 0, 0, 150),  # Black shadow
    "shadow_offset": (2, 2),  # Shadow offset
}

# Updated TEXT_BOX_CONFIG with letter circle stroke options
TEXT_BOX_CONFIG = {
    "width": 850,  # Width of each text box (made even bigger)
    "height": 170,  # Height of each text box (made even bigger)
    "x": 140,  # X position (moved much further left)
    "y_base": 770,  # Base Y position for first option
    "spacing": 200,  # Vertical spacing between text boxes (more space for bigger boxes)
    "corner_radius": 30,  # Rounded corner radius (bigger radius)
    "border_width": 7,  # Increased border width for more prominent stroke
    "fill_color": (255, 255, 255),  # White fill
    "text_color": (0, 0, 0),  # Black text
    "shadow_color": (0, 0, 0, 180),  # Black shadow with transparency
    "shadow_offset": (3, 3),  # Shadow offset (x, y)
    "letter_circle_radius": 80,  # UPDATED: Increased radius for bigger circles (was 60)
    "letter_circle_color": (138, 43, 226),  # Purple color for letter circles
    "letter_text_color": (255, 255, 255),  # White text for letters
    "letter_offset_x": -370,  # X offset for letter circle from text box center (adjusted for bigger box)
    # Green answer reveal colors
    "correct_fill_color": (34, 197, 94),  # Green fill for correct answer
    "correct_border_color": (22, 163, 74),  # Darker green border for correct answer
    "correct_letter_circle_color": (21, 128, 61),  # Dark green for letter circle
    "option_text_size": 80,  # Larger font size for option text
    "letter_text_size": 85,  # Larger font size for letter text
    # NEW: Control letter position inside circle
    "letter_x_offset": 0,  # Horizontal offset for letter inside circle (0 = centered)
    "letter_y_offset": -12,  # Vertical offset for letter inside circle (negative = up)
    # NEW: Control options text position inside text box
    "text_x_offset": 60,  # Distance from left edge of text box (for left alignment)
    "text_y_offset": -20,  # Vertical offset from center
}

# Color Mode Configurations
COLOR_MODES = {
    "mode1": {  # Original colors (white backgrounds)
        "question_text_box": {
            "fill_color": (255, 255, 255),  # White fill
            "text_color": (0, 0, 0),  # Black text
            "shadow_color": (0, 0, 0, 150),  # Black shadow
        },
        "options_text_box": {
            "fill_color": (255, 255, 255),  # White fill
            "text_color": (0, 0, 0),  # Black text
            "border_color": (0, 0, 0),  # Black stroke
            "shadow_color": (0, 0, 0, 180),  # Black shadow
        }
    },
    "mode2": {  # New colors (black backgrounds)
        "question_text_box": {
            "fill_color": (0, 0, 0),  # Black fill
            "text_color": (255, 255, 255),  # White text
            "shadow_color": (255, 255, 255, 150),  # White shadow
        },
        "options_text_box": {
            "fill_color": (0, 0, 0),  # Black fill
            "text_color": (255, 255, 255),  # White text
            "border_color": (255, 255, 255),  # White stroke
            "shadow_color": (255, 255, 255, 180),  # White shadow
        },
    },
    "mode3": {  # Original colors (white backgrounds)
        "question_text_box": {
            "fill_color": (255, 255, 255),  # White fill
            "text_color": (0, 0, 0),  # Black text
            "shadow_color": (0, 0, 0, 150),  # Black shadow
        },
        "options_text_box": {
            "fill_color": (0, 0, 0),  # Black fill
            "text_color": (255, 255, 255),  # White text
            "border_color": (255, 255, 255),  # White stroke
            "shadow_color": (255, 255, 255, 180),  # White shadow
        },
    },
    "mode4": {  # New colors (black backgrounds)
        "question_text_box": {
            "fill_color": (0, 0, 0),  # Black fill
            "text_color": (255, 255, 255),  # White text
            "shadow_color": (255, 255, 255, 150),  # White shadow
        },
        "options_text_box": {
            "fill_color": (255, 255, 255),  # White fill
            "text_color": (0, 0, 0),  # Black text
            "border_color": (0, 0, 0),  # Black stroke
            "shadow_color": (0, 0, 0, 180),  # Black shadow
        }
    }
}



# Follow me text configuration
FOLLOW_ME_CONFIG = {
    "text": "شارك اجابتك في التعليقات",
    "fontsize": 90,
    "fontcolor": "red",
    "x": 520,  # Center horizontally (1920/2)
    "y": 1570,  # Position from top
    "flicker_speed": 6,  # Times per second to flicker
}

def extract_option_text(option):
    """Extract the text from an option, removing the A), B), C) prefix"""
    if ')' in option:
        return option.split(')', 1)[1].strip()
    return option.strip()



def create_account_name_text_box(account_display_name, account_index):
    """Create account name text box with gradient background like in the image"""
    config = ACCOUNT_NAME_TEXT_BOX_CONFIG

    # Create image with transparency
    img = Image.new('RGBA', (config["width"] + 20, config["height"] + 20), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    # Create gradient background
    gradient_start = config["gradient_start"]
    gradient_end = config["gradient_end"]

    # Draw gradient rectangle
    for y in range(config["height"]):
        # Calculate gradient ratio
        ratio = y / config["height"]
        r = int(gradient_start[0] * (1 - ratio) + gradient_end[0] * ratio)
        g = int(gradient_start[1] * (1 - ratio) + gradient_end[1] * ratio)
        b = int(gradient_start[2] * (1 - ratio) + gradient_end[2] * ratio)

        # Draw line with gradient color
        draw.line([(0, y), (config["width"], y)], fill=(r, g, b))

    # Create a new image with the gradient and apply rounded corners
    gradient_img = Image.new('RGBA', (config["width"], config["height"]), (0, 0, 0, 0))
    gradient_draw = ImageDraw.Draw(gradient_img)

    # Draw the gradient background with rounded corners
    for y in range(config["height"]):
        ratio = y / config["height"]
        r = int(gradient_start[0] * (1 - ratio) + gradient_end[0] * ratio)
        g = int(gradient_start[1] * (1 - ratio) + gradient_end[1] * ratio)
        b = int(gradient_start[2] * (1 - ratio) + gradient_end[2] * ratio)
        gradient_draw.line([(0, y), (config["width"], y)], fill=(r, g, b))

    # Create mask for rounded corners
    mask = Image.new('RGBA', (config["width"], config["height"]), (0, 0, 0, 0))
    mask_draw = ImageDraw.Draw(mask)
    mask_draw.rounded_rectangle(
        [0, 0, config["width"], config["height"]],
        radius=config["corner_radius"],
        fill=(255, 255, 255, 255)
    )

    # Apply mask to gradient
    result_img = Image.new('RGBA', (config["width"] + 20, config["height"] + 20), (0, 0, 0, 0))
    result_draw = ImageDraw.Draw(result_img)

    # Draw shadow first
    shadow_x, shadow_y = config["shadow_offset"]
    shadow_mask = Image.new('RGBA', (config["width"], config["height"]), (0, 0, 0, 0))
    shadow_mask_draw = ImageDraw.Draw(shadow_mask)
    shadow_mask_draw.rounded_rectangle(
        [0, 0, config["width"], config["height"]],
        radius=config["corner_radius"],
        fill=config["shadow_color"]
    )
    result_img.paste(shadow_mask, (shadow_x, shadow_y), shadow_mask)

    # Apply rounded corners to gradient
    gradient_with_corners = Image.new('RGBA', (config["width"], config["height"]), (0, 0, 0, 0))
    for x in range(config["width"]):
        for y in range(config["height"]):
            if mask.getpixel((x, y))[3] > 0:  # If mask is not transparent
                gradient_with_corners.putpixel((x, y), gradient_img.getpixel((x, y)))

    # Paste the gradient with rounded corners
    result_img.paste(gradient_with_corners, (0, 0), gradient_with_corners)

    # Add account name text
    try:
        font = ImageFont.truetype("arialbd.ttf", config["font_size"])
    except:
        try:
            font = ImageFont.truetype("arial.ttf", config["font_size"])
        except:
            font = ImageFont.load_default()

    if font:
        # Get text bounding box for centering
        bbox = result_draw.textbbox((0, 0), account_display_name, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # Center text in the text box
        text_x = (config["width"] - text_width) // 2
        text_y = (config["height"] - text_height) // 2 - 5  # Slight vertical adjustment

        # Draw text shadow
        text_shadow_x = text_x + 1
        text_shadow_y = text_y + 1
        result_draw.text((text_shadow_x, text_shadow_y), account_display_name,
                         fill=(0, 0, 0, 80),  # Semi-transparent black shadow
                         font=font)

        # Draw main text
        result_draw.text((text_x, text_y), account_display_name,
                         fill=config["text_color"],
                         font=font)

    # Save account name text box image
    filename = f"text_boxes/account_name_{account_index}.png"
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    result_img.save(filename, 'PNG')

    print(f"✅ Created account name text box: '{account_display_name}' -> {filename}")
    return filename


def get_letter_circle_stroke_settings(stroke_color, is_correct=False):
    """Get stroke settings for letter circles based on configuration"""
    if not LETTER_CIRCLE_STROKE_CONFIG["enabled"]:
        # No stroke
        return None, 0

    if is_correct:
        # For correct answers (green), always use the correct border color
        return TEXT_BOX_CONFIG["correct_border_color"], LETTER_CIRCLE_STROKE_CONFIG["width"]

    # For regular circles, check the color setting
    color_setting = LETTER_CIRCLE_STROKE_CONFIG["color"]

    if color_setting == "white":
        return (255, 255, 255), LETTER_CIRCLE_STROKE_CONFIG["width"]
    elif color_setting == "same_as_border":
        return stroke_color, LETTER_CIRCLE_STROKE_CONFIG["width"]
    elif color_setting == "random":
        return generate_random_stroke_color(), LETTER_CIRCLE_STROKE_CONFIG["width"]
    else:
        # Default to white
        return (255, 255, 255), LETTER_CIRCLE_STROKE_CONFIG["width"]


def generate_random_stroke_color():
    """Generate a random vibrant stroke color, avoiding white and very light colors"""

    # Define a list of vibrant, distinct colors
    vibrant_colors = [
        (255, 0, 0),  # Red
        (0, 100, 255),  # Blue
        (0, 200, 0),  # Green
        (255, 140, 0),  # Orange
        (128, 0, 128),  # Purple
        (255, 20, 147),  # Deep Pink
        (0, 191, 255),  # Deep Sky Blue
        (220, 20, 60),  # Crimson
        (50, 205, 50),  # Lime Green
        (138, 43, 226),  # Blue Violet
        (255, 69, 0),  # Red Orange
        (30, 144, 255),  # Dodger Blue
        (255, 0, 255),  # Magenta
        (0, 206, 209),  # Dark Turquoise
        (255, 215, 0),  # Gold (darker gold)
        (148, 0, 211),  # Dark Violet
        (220, 20, 60),  # Crimson
        (0, 128, 128),  # Teal
        (184, 134, 11),  # Dark Golden Rod
        (139, 69, 19),  # Saddle Brown
    ]

    # Select a random color from the list
    selected_color = random.choice(vibrant_colors)

    # Add some random variation to make it unique each time
    r, g, b = selected_color

    # Add small random variations (±20) while keeping colors vibrant
    r = max(0, min(255, r + random.randint(-20, 20)))
    g = max(0, min(255, g + random.randint(-20, 20)))
    b = max(0, min(255, b + random.randint(-20, 20)))

    return (r, g, b)


def generate_random_letter_circle_color():
    """Generate a random vibrant color for letter circles, avoiding white and very light colors"""

    # Define a list of vibrant, distinct colors suitable for letter circles
    vibrant_colors = [
        (255, 0, 0),  # Red
        (0, 100, 255),  # Blue
        (0, 150, 0),  # Green
        (255, 140, 0),  # Orange
        (128, 0, 128),  # Purple
        (255, 20, 147),  # Deep Pink
        (0, 191, 255),  # Deep Sky Blue
        (220, 20, 60),  # Crimson
        (50, 205, 50),  # Lime Green
        (138, 43, 226),  # Blue Violet (original purple)
        (255, 69, 0),  # Red Orange
        (30, 144, 255),  # Dodger Blue
        (255, 0, 255),  # Magenta
        (0, 206, 209),  # Dark Turquoise
        (148, 0, 211),  # Dark Violet
        (220, 20, 60),  # Crimson
        (0, 128, 128),  # Teal
        (184, 134, 11),  # Dark Golden Rod
        (139, 69, 19),  # Saddle Brown
        (165, 42, 42),  # Brown
        (128, 0, 0),  # Maroon
        (0, 128, 0),  # Dark Green
        (0, 0, 139),  # Dark Blue
        (75, 0, 130),  # Indigo
        (199, 21, 133),  # Medium Violet Red
    ]

    # Select a random color from the list
    selected_color = random.choice(vibrant_colors)

    # Add some random variation to make it unique each time
    r, g, b = selected_color

    # Add small random variations (±15) while keeping colors vibrant and avoiding white
    r = max(30, min(255, r + random.randint(-15, 15)))  # Ensure minimum of 30 to avoid very light colors
    g = max(30, min(255, g + random.randint(-15, 15)))  # Ensure minimum of 30 to avoid very light colors
    b = max(30, min(255, b + random.randint(-15, 15)))  # Ensure minimum of 30 to avoid very light colors

    # Additional check to avoid colors too close to white
    if r > 220 and g > 220 and b > 220:
        # If too close to white, force it to be darker
        r = min(r, 180)
        g = min(g, 180)
        b = min(b, 180)

    print(f"🎨 Generated random letter circle color: RGB({r}, {g}, {b})")
    return (r, g, b)


def create_white_text_boxes_with_dynamic_positioning(question_index, answer_options, correct_answer=None,
                                                     letter_circle_color=None, question_text="", color_mode="mode1"):
    """Create 3 white text boxes with dynamic positioning based on question length"""
    text_box_paths = []
    green_text_box_paths = []

    # Get dynamic configuration based on question text
    dynamic_text_box_config = get_dynamic_text_box_config(question_text)

    # Get color configuration based on mode
    color_config = COLOR_MODES.get(color_mode, COLOR_MODES["mode1"])["options_text_box"]

    # UPDATED: Always use black stroke color for text boxes
    stroke_colors = [color_config["border_color"], color_config["border_color"], color_config["border_color"]]

    print(f"🎨 Using color mode {color_mode} for options text boxes in question {question_index + 1}")

    # Use provided letter circle color or generate a new one
    if letter_circle_color is None:
        letter_circle_color = generate_random_letter_circle_color()

    print(f"🎨 Using letter circle color for question {question_index + 1}: RGB{letter_circle_color}")
    print(
        f"📏 Using dynamic positioning - Text box size: {dynamic_text_box_config['width']}x{dynamic_text_box_config['height']}")

    # Determine which option is correct
    correct_option_letter = None
    if correct_answer:
        correct_option_letter = extract_correct_option_letter(correct_answer)

    for i, option in enumerate(answer_options):
        option_letter = chr(65 + i)  # A, B, C (for comparison with correct answer)
        arabic_letter = ARABIC_LETTERS[i]  # أ, ب, ج (for display)
        is_correct = (option_letter == correct_option_letter)
        stroke_color = stroke_colors[i]

        # Create regular text box with dynamic sizing
        box_width = dynamic_text_box_config["width"]
        box_height = dynamic_text_box_config["height"]

        # UPDATED: Create image with extra space for circle on the RIGHT
        circle_radius = dynamic_text_box_config["letter_circle_radius"]
        extra_space = circle_radius * 2 + 20  # Extra space for complete circle + padding
        img = Image.new('RGBA', (box_width + extra_space, box_height + 20), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Draw shadow first (behind everything)
        shadow_offset_x, shadow_offset_y = dynamic_text_box_config["shadow_offset"]
        shadow_x = 0 + shadow_offset_x  # No offset needed since circle is on right
        shadow_y = 0 + shadow_offset_y

        draw.rounded_rectangle(
            [shadow_x, shadow_y, shadow_x + box_width, shadow_y + box_height],
            radius=dynamic_text_box_config["corner_radius"],
            fill=dynamic_text_box_config["shadow_color"]
        )

        # UPDATED: Draw main text box without left offset (circle is on right now)
        box_x = 0  # No left offset needed
        box_y = 0

        # Draw rounded rectangle background with random stroke color
        draw.rounded_rectangle(
            [shadow_x, shadow_y, shadow_x + box_width, shadow_y + box_height],
            radius=dynamic_text_box_config["corner_radius"],
            fill=color_config["shadow_color"]  # Use mode-specific shadow color
        )

        # UPDATED: Draw main text box without left offset (circle is on right now)
        box_x = 0  # No left offset needed
        box_y = 0

        # Draw rounded rectangle background with random stroke color
        draw.rounded_rectangle(
            [box_x, box_y, box_x + box_width, box_y + box_height],
            radius=dynamic_text_box_config["corner_radius"],
            fill=color_config["fill_color"],  # Use mode-specific fill color
            outline=stroke_color,  # Use random stroke color
            width=dynamic_text_box_config["border_width"]
        )

        # UPDATED: Draw circle on the RIGHT side of text box
        circle_x = box_width + circle_radius - 90  # Position circle on the right
        circle_y = box_height // 2  # Center vertically with text box

        # Draw circle shadow
        circle_shadow_x = circle_x + shadow_offset_x
        circle_shadow_y = circle_y + shadow_offset_y
        draw.ellipse(
            [circle_shadow_x - circle_radius, circle_shadow_y - circle_radius,
             circle_shadow_x + circle_radius, circle_shadow_y + circle_radius],
            fill=dynamic_text_box_config["shadow_color"]
        )

        # Get stroke settings for letter circle
        circle_stroke_color, circle_stroke_width = get_letter_circle_stroke_settings(stroke_color, is_correct=False)

        # Draw COMPLETE circle with configurable stroke
        if circle_stroke_color is not None and circle_stroke_width > 0:
            # Draw circle with stroke
            draw.ellipse(
                [circle_x - circle_radius, circle_y - circle_radius,
                 circle_x + circle_radius, circle_y + circle_radius],
                fill=letter_circle_color,  # Use random letter circle color
                outline=circle_stroke_color,  # Use configurable stroke color
                width=circle_stroke_width
            )
        else:
            # Draw circle without stroke
            draw.ellipse(
                [circle_x - circle_radius, circle_y - circle_radius,
                 circle_x + circle_radius, circle_y + circle_radius],
                fill=letter_circle_color  # Use random letter circle color, no outline
            )

        # UPDATED: Draw ARABIC letter in circle with proper font loading
        letter_font = load_arabic_font(dynamic_text_box_config["letter_text_size"])

        if letter_font:
            try:
                # Get text bounding box for perfect centering
                bbox = draw.textbbox((0, 0), arabic_letter, font=letter_font)  # Use Arabic letter
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                # After getting dynamic_text_box_config
                letter_offsets = dynamic_text_box_config.get("letter_offsets", {})
                offsets = letter_offsets.get(arabic_letter, {"x": 0, "y": -12})

                # Then use these offsets:
                letter_x = circle_x - text_width // 2 + offsets["x"]
                letter_y = circle_y - text_height // 2 + offsets["y"]

                draw.text((letter_x, letter_y), arabic_letter,  # Use Arabic letter
                          fill=dynamic_text_box_config["letter_text_color"],
                          font=letter_font)
                print(f"✅ Successfully drew Arabic letter '{arabic_letter}' in circle")

            except Exception as e:
                print(f"❌ Error drawing Arabic letter '{arabic_letter}': {e}")
                # Fallback: draw English letter
                try:
                    english_letter = chr(65 + i)  # A, B, C
                    bbox = draw.textbbox((0, 0), english_letter, font=letter_font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]
                    letter_x = circle_x - text_width // 2
                    letter_y = circle_y - text_height // 2 - 12
                    draw.text((letter_x, letter_y), english_letter,
                              fill=dynamic_text_box_config["letter_text_color"],
                              font=letter_font)
                    print(f"🔄 Used English fallback letter '{english_letter}'")
                except Exception as fallback_error:
                    print(f"❌ Fallback letter drawing also failed: {fallback_error}")

        # Draw option text in regular text box
        text_font = load_arabic_font(dynamic_text_box_config["option_text_size"])

        if text_font:
            # Process Arabic text properly
            option_text = option.split(')', 1)[1].strip() if ')' in option else option
            processed_option_text = process_arabic_text(option_text)

            try:
                # Calculate text positioning
                bbox = draw.textbbox((0, 0), processed_option_text, font=text_font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                # Get positioning offsets from config
                text_x_offset = dynamic_text_box_config.get("text_x_offset", 20)
                text_y_offset = dynamic_text_box_config.get("text_y_offset", 0)

                # UPDATED: Position text from RIGHT edge (RTL) instead of left edge
                text_x = box_x + box_width - text_width - text_x_offset
                text_y = box_y + (box_height - text_height) // 2 + text_y_offset

                # Draw text shadow
                text_shadow_x = text_x + 1
                text_shadow_y = text_y + 1
                draw.text((text_shadow_x, text_shadow_y), processed_option_text,
                          fill=(128, 128, 128, 100),  # Light gray shadow
                          font=text_font)

                # Draw main text
                draw.text((text_x, text_y), processed_option_text,
                          fill=color_config["text_color"],  # Use mode-specific text color
                          font=text_font)

                print(f"✅ Successfully drew Arabic option text")

            except Exception as e:
                print(f"❌ Error drawing option text: {e}")
                # Draw simple fallback text
                try:
                    simple_text = f"Option {chr(65 + i)}"
                    bbox = draw.textbbox((0, 0), simple_text, font=text_font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]
                    text_x = box_x + 20
                    text_y = box_y + (box_height - text_height) // 2
                    draw.text((text_x, text_y), simple_text,
                              fill=dynamic_text_box_config["text_color"],
                              font=text_font)
                    print(f"🔄 Used simple fallback text: {simple_text}")
                except:
                    print(f"❌ Even fallback text failed")

        # Save regular text box image
        filename = f"text_boxes/question_{question_index}_option_{i}.png"
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        img.save(filename, 'PNG')
        text_box_paths.append(filename)

        # Create green version if this is the correct answer
        if is_correct:
            # [Green text box creation code remains the same but with similar font loading updates...]
            # Create green text box for correct answer reveal with COMPLETE circle
            green_img = Image.new('RGBA', (box_width + extra_space, box_height + 20), (0, 0, 0, 0))
            green_draw = ImageDraw.Draw(green_img)

            # Draw green shadow
            green_draw.rounded_rectangle(
                [shadow_x, shadow_y, shadow_x + box_width, shadow_y + box_height],
                radius=dynamic_text_box_config["corner_radius"],
                fill=dynamic_text_box_config["shadow_color"]
            )

            # UPDATED: Draw green rounded rectangle background with black stroke
            green_draw.rounded_rectangle(
                [box_x, box_y, box_x + box_width, box_y + box_height],
                radius=dynamic_text_box_config["corner_radius"],
                fill=dynamic_text_box_config["correct_fill_color"],
                outline=(0, 0, 0),  # Always black stroke
                width=dynamic_text_box_config["border_width"]
            )

            # Draw green circle shadow
            green_draw.ellipse(
                [circle_shadow_x - circle_radius, circle_shadow_y - circle_radius,
                 circle_shadow_x + circle_radius, circle_shadow_y + circle_radius],
                fill=dynamic_text_box_config["shadow_color"]
            )

            # Draw COMPLETE dark green letter circle (keep green for correct answer)
            green_draw.ellipse(
                [circle_x - circle_radius, circle_y - circle_radius,
                 circle_x + circle_radius, circle_y + circle_radius],
                fill=dynamic_text_box_config["correct_letter_circle_color"],
                outline=(0, 0, 0),  # Black outline for green circle too
                width=3
            )

            # UPDATED: Draw ARABIC letter in green circle with proper font
            if letter_font:
                try:
                    green_draw.text((letter_x, letter_y), arabic_letter,  # Use Arabic letter
                                    fill=dynamic_text_box_config["letter_text_color"],
                                    font=letter_font)
                except Exception as e:
                    print(f"❌ Error drawing Arabic letter in green circle: {e}")
                    # Use English fallback
                    try:
                        english_letter = chr(65 + i)
                        green_draw.text((letter_x, letter_y), english_letter,
                                        fill=dynamic_text_box_config["letter_text_color"],
                                        font=letter_font)
                    except:
                        pass

            # Draw option text in green text box (white text with right alignment)
            if text_font:
                try:
                    # Process Arabic text properly
                    processed_option_text = process_arabic_text(option_text)

                    # Calculate text positioning (same as regular text box)
                    bbox = green_draw.textbbox((0, 0), processed_option_text, font=text_font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]

                    # Get positioning offsets from config
                    text_x_offset = dynamic_text_box_config.get("text_x_offset", 20)
                    text_y_offset = dynamic_text_box_config.get("text_y_offset", 0)

                    # UPDATED: Position text from RIGHT edge (RTL) instead of left edge
                    text_x = box_x + box_width - text_width - text_x_offset
                    text_y = box_y + (box_height - text_height) // 2 + text_y_offset

                    # Draw text shadow
                    text_shadow_x = text_x + 1
                    text_shadow_y = text_y + 1
                    green_draw.text((text_shadow_x, text_shadow_y), processed_option_text,
                                    fill=(0, 0, 0, 120),  # Dark shadow on green
                                    font=text_font)

                    # Draw white text on green background (right-aligned)
                    green_draw.text((text_x, text_y), processed_option_text,
                                    fill=(255, 255, 255),  # White text on green background
                                    font=text_font)
                except Exception as e:
                    print(f"❌ Error drawing green text: {e}")

            # Save green text box image
            green_filename = f"text_boxes/question_{question_index}_option_{i}_green.png"
            green_img.save(green_filename, 'PNG')
            green_text_box_paths.append(green_filename)
        else:
            green_text_box_paths.append(None)

        print(f"✅ Created dynamic text box for option {arabic_letter} - Size: {box_width}x{box_height}")

    return text_box_paths, green_text_box_paths


def create_question_text_box_dynamic(question_text, question_index, color_mode="mode1"):
    """Create a white text box for the question text with dynamic sizing based on question length"""

    # Get dynamic configuration
    dynamic_config = get_dynamic_question_text_box_config(question_text)

    # Get color configuration based on mode
    color_config = COLOR_MODES.get(color_mode, COLOR_MODES["mode1"])["question_text_box"]

    # Generate random stroke color for question
    stroke_color = generate_random_stroke_color()
    print(f"🎨 Generated question stroke color: RGB{stroke_color}")
    print(f"🎨 Using color mode: {color_mode}")
    print(
        f"📏 Using dynamic question box - Size: {dynamic_config['width']}x{dynamic_config['height']}, Position: ({dynamic_config['x']}, {dynamic_config['y']})")

    box_width = dynamic_config["width"]
    box_height = dynamic_config["height"]

    # Create image with transparency (extra space for shadow)
    img = Image.new('RGBA', (box_width + 20, box_height + 20), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    # Draw shadow first
    shadow_offset_x, shadow_offset_y = dynamic_config["shadow_offset"]
    shadow_x = shadow_offset_x
    shadow_y = shadow_offset_y

    draw.rounded_rectangle(
        [shadow_x, shadow_y, shadow_x + box_width, shadow_y + box_height],
        radius=dynamic_config["corner_radius"],
        fill=color_config["shadow_color"]  # Use mode-specific shadow color
    )

    # Draw main question text box
    box_x = 0
    box_y = 0

    draw.rounded_rectangle(
        [box_x, box_y, box_x + box_width, box_y + box_height],
        radius=dynamic_config["corner_radius"],
        fill=color_config["fill_color"],  # Use mode-specific fill color
        outline=stroke_color,  # Random stroke color
        width=dynamic_config["border_width"]
    )

    # Save question text box image
    filename = f"text_boxes/question_{question_index}_question_box.png"
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    img.save(filename, 'PNG')

    print(
        f"✅ Created dynamic question text box with color mode {color_mode} and stroke color RGB{stroke_color}: {filename}")
    return filename


def create_session_with_retries():
    """Create a requests session with retry strategy"""
    session = requests.Session()

    # Define retry strategy
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"]
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })

    return session


def extract_correct_option_letter(correct_answer):
    """Extract the correct option letter (A, B, or C) from the correct answer string"""
    # Look for patterns like "Answer A", "Answer B", "Answer C"
    match = re.search(r'Answer\s+([ABC])', correct_answer, re.IGNORECASE)
    if match:
        return match.group(1).upper()

    # Look for just the letter
    match = re.search(r'^([ABC])$', correct_answer.strip(), re.IGNORECASE)
    if match:
        return match.group(1).upper()

    # Default to A if can't determine
    return 'A'


def create_white_text_boxes(question_index, answer_options, correct_answer=None):
    """Create 3 white text boxes with purple letter circles for the options"""
    text_box_paths = []
    green_text_box_paths = []  # For correct answer reveal

    # Determine which option is correct
    correct_option_letter = None
    if correct_answer:
        correct_option_letter = extract_correct_option_letter(correct_answer)

    for i, option in enumerate(answer_options):
        option_letter = chr(65 + i)  # A, B, C
        is_correct = (option_letter == correct_option_letter)

        # Create regular text box
        box_width = TEXT_BOX_CONFIG["width"]
        box_height = TEXT_BOX_CONFIG["height"]

        # Create image with transparency
        img = Image.new('RGBA', (box_width + 100, box_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Draw rounded rectangle for text box
        box_x = 50  # Offset to leave space for letter circle
        box_y = 0

        # Draw rounded rectangle background
        draw.rounded_rectangle(
            [box_x, box_y, box_x + box_width, box_y + box_height],
            radius=TEXT_BOX_CONFIG["corner_radius"],
            fill=TEXT_BOX_CONFIG["fill_color"],
            outline=TEXT_BOX_CONFIG["border_color"],
            width=TEXT_BOX_CONFIG["border_width"]
        )

        # Draw purple letter circle
        letter = chr(65 + i)  # A, B, C
        circle_x = 25  # Position for letter circle
        circle_y = box_height // 2
        circle_radius = TEXT_BOX_CONFIG["letter_circle_radius"]

        # Draw circle
        draw.ellipse(
            [circle_x - circle_radius, circle_y - circle_radius,
             circle_x + circle_radius, circle_y + circle_radius],
            fill=TEXT_BOX_CONFIG["letter_circle_color"],
            outline=TEXT_BOX_CONFIG["letter_circle_color"]
        )

        # Draw letter in circle
        try:
            letter_font = ImageFont.truetype("arialbd.ttf", TEXT_BOX_CONFIG["letter_text_size"])  # Bold font
        except:
            try:
                letter_font = ImageFont.truetype("arial.ttf",
                                                 TEXT_BOX_CONFIG["letter_text_size"])  # Fallback to regular arial
            except:
                letter_font = ImageFont.load_default()

        if letter_font:
            # Get text bounding box for centering
            bbox = draw.textbbox((0, 0), letter, font=letter_font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            letter_x = circle_x - text_width // 2
            letter_y = circle_y - text_height // 2 - 2

            draw.text((letter_x, letter_y), letter,
                      fill=TEXT_BOX_CONFIG["letter_text_color"],
                      font=letter_font)

        # UPDATED: Draw option text in text box with left alignment
        option_text = option.split(')', 1)[1].strip() if ')' in option else option

        try:
            text_font = ImageFont.truetype("arialbd.ttf", dynamic_text_box_config["option_text_size"])
        except:
            try:
                text_font = ImageFont.truetype("arial.ttf", dynamic_text_box_config["option_text_size"])
            except:
                text_font = ImageFont.load_default()

        if text_font:
            # UPDATED: Left-align text instead of centering
            bbox = draw.textbbox((0, 0), option_text, font=text_font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # Get positioning offsets from config
            text_x_offset = dynamic_text_box_config.get("text_x_offset", 20)  # Distance from left edge
            text_y_offset = dynamic_text_box_config.get("text_y_offset", 0)  # Vertical offset from center

            # Position text from left edge with configurable offset
            text_x = box_x + text_x_offset  # Left-aligned with offset
            text_y = box_y + (box_height - text_height) // 2 + text_y_offset  # Vertically centered with offset

            # Draw text shadow
            text_shadow_x = text_x + 1
            text_shadow_y = text_y + 1
            draw.text((text_shadow_x, text_shadow_y), option_text,
                      fill=(128, 128, 128, 100),  # Light gray shadow
                      font=text_font)

            # Draw main text
            draw.text((text_x, text_y), option_text,
                      fill=dynamic_text_box_config["text_color"],
                      font=text_font)

        # Save regular text box image
        filename = f"text_boxes/question_{question_index}_option_{i}.png"
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        img.save(filename, 'PNG')
        text_box_paths.append(filename)

        # Create green version if this is the correct answer
        if is_correct:
            # Create green text box for correct answer reveal
            green_img = Image.new('RGBA', (box_width + 100, box_height), (0, 0, 0, 0))
            green_draw = ImageDraw.Draw(green_img)

            # Draw green rounded rectangle background
            green_draw.rounded_rectangle(
                [box_x, box_y, box_x + box_width, box_y + box_height],
                radius=TEXT_BOX_CONFIG["corner_radius"],
                fill=TEXT_BOX_CONFIG["correct_fill_color"],
                outline=TEXT_BOX_CONFIG["correct_border_color"],
                width=TEXT_BOX_CONFIG["border_width"]
            )

            # Draw dark green letter circle
            green_draw.ellipse(
                [circle_x - circle_radius, circle_y - circle_radius,
                 circle_x + circle_radius, circle_y + circle_radius],
                fill=TEXT_BOX_CONFIG["correct_letter_circle_color"],
                outline=TEXT_BOX_CONFIG["correct_letter_circle_color"]
            )

            # Draw letter in green circle
            if letter_font:
                green_draw.text((letter_x, letter_y), letter,
                                fill=TEXT_BOX_CONFIG["letter_text_color"],
                                font=letter_font)

            # Draw option text in green text box (white text)
            if text_font:
                green_draw.text((text_x, text_y), option_text,
                                fill=(255, 255, 255),  # White text on green background
                                font=text_font)

            # Save green text box image
            green_filename = f"text_boxes/question_{question_index}_option_{i}_green.png"
            green_img.save(green_filename, 'PNG')
            green_text_box_paths.append(green_filename)
        else:
            green_text_box_paths.append(None)

        print(f"✅ Created text box for option {letter}: {filename}")
        if is_correct:
            print(f"✅ Created GREEN text box for correct option {letter}: {green_filename}")

    return text_box_paths, green_text_box_paths


class EmojiImageManager:
    def __init__(self):
        self.session = create_session_with_retries()
        self.emoji_cache = {}
        self.emoji_dir = "emoji_images"
        os.makedirs(self.emoji_dir, exist_ok=True)
        self.used_emojis_in_quiz = set()
        self.debug_mode = True

        # Test Arabic font loading on initialization
        print("🔤 Testing Arabic font support...")
        test_font = load_arabic_font(20)
        if test_font:
            print("✅ Arabic font support confirmed")
        else:
            print("⚠️ Arabic font support limited - using fallbacks")

        # Emoji styles - will be randomly selected per question
        self.emoji_styles = ['standard', 'playful']

        # Comprehensive country flag mapping - HIGHEST PRIORITY for countries
        self.country_flags = {
            # Major Countries - Full names and common variations
            'afghanistan': '1f1e6-1f1eb', 'albania': '1f1e6-1f1f1', 'algeria': '1f1e9-1f1ff',
            'andorra': '1f1e6-1f1e9', 'angola': '1f1e6-1f1f4', 'argentina': '1f1e6-1f1f7',
            'armenia': '1f1e6-1f1f2', 'australia': '1f1e6-1f1fa', 'austria': '1f1e6-1f1f9',
            'azerbaijan': '1f1e6-1f1ff', 'bahrain': '1f1e7-1f1ed', 'bangladesh': '1f1e7-1f1e9',
            'belarus': '1f1e7-1f1fe', 'belgium': '1f1e7-1f1ea', 'bolivia': '1f1e7-1f1f4',
            'bosnia': '1f1e7-1f1e6', 'bosnia and herzegovina': '1f1e7-1f1e6', 'botswana': '1f1e7-1f1fc',
            'brazil': '1f1e7-1f1f7', 'brunei': '1f1e7-1f1f3', 'bulgaria': '1f1e7-1f1ec',
            'cambodia': '1f1f0-1f1ed', 'cameroon': '1f1e8-1f1f2', 'canada': '1f1e8-1f1e6',
            'chile': '1f1e8-1f1f1', 'china': '1f1e8-1f1f3', 'colombia': '1f1e8-1f1f4',
            'croatia': '1f1ed-1f1f7', 'cuba': '1f1e8-1f1fa', 'cyprus': '1f1e8-1f1fe',
            'czech republic': '1f1e8-1f1ff', 'czechia': '1f1e8-1f1ff', 'denmark': '1f1e9-1f1f0',
            'ecuador': '1f1ea-1f1e8', 'egypt': '1f1ea-1f1ec', 'estonia': '1f1ea-1f1ea',
            'ethiopia': '1f1ea-1f1f9', 'finland': '1f1eb-1f1ee', 'france': '1f1eb-1f1f7',
            'georgia': '1f1ec-1f1ea', 'germany': '1f1e9-1f1ea', 'ghana': '1f1ec-1f1ed',
            'greece': '1f1ec-1f1f7', 'guatemala': '1f1ec-1f1f9', 'hungary': '1f1ed-1f1fa',
            'iceland': '1f1ee-1f1f8', 'india': '1f1ee-1f1f3', 'indonesia': '1f1ee-1f1e9',
            'iran': '1f1ee-1f1f7', 'iraq': '1f1ee-1f1f6', 'ireland': '1f1ee-1f1ea',
            'israel': '1f1ee-1f1f1', 'italy': '1f1ee-1f1f9', 'jamaica': '1f1ef-1f1f2',
            'japan': '1f1ef-1f1f5', 'jordan': '1f1ef-1f1f4', 'kazakhstan': '1f1f0-1f1ff',
            'kenya': '1f1f0-1f1ea', 'kuwait': '1f1f0-1f1fc', 'latvia': '1f1f1-1f1fb',
            'lebanon': '1f1f1-1f1e7', 'libya': '1f1f1-1f1fe', 'lithuania': '1f1f1-1f1f9',
            'luxembourg': '1f1f1-1f1fa', 'malaysia': '1f1f2-1f1fe', 'malta': '1f1f2-1f1f9',
            'mexico': '1f1f2-1f1fd', 'morocco': '1f1f2-1f1e6', 'nepal': '1f1f3-1f1f5',
            'netherlands': '1f1f3-1f1f1', 'new zealand': '1f1f3-1f1ff', 'nigeria': '1f1f3-1f1ec',
            'north korea': '1f1f0-1f1f5', 'norway': '1f1f3-1f1f4', 'pakistan': '1f1f5-1f1f0',
            'palestine': '1f1f5-1f1f8', 'panama': '1f1f5-1f1e6', 'peru': '1f1f5-1f1ea',
            'philippines': '1f1f5-1f1ed', 'poland': '1f1f5-1f1f1', 'portugal': '1f1f5-1f1f9',
            'qatar': '1f1f6-1f1e6', 'romania': '1f1f7-1f1f4', 'russia': '1f1f7-1f1fa',
            'saudi arabia': '1f1f8-1f1e6', 'serbia': '1f1f7-1f1f8', 'singapore': '1f1f8-1f1ec',
            'slovakia': '1f1f8-1f1f0', 'slovenia': '1f1f8-1f1ee', 'south africa': '1f1ff-1f1e6',
            'south korea': '1f1f0-1f1f7', 'spain': '1f1ea-1f1f8', 'sri lanka': '1f1f1-1f1f0',
            'sweden': '1f1f8-1f1ea', 'switzerland': '1f1e8-1f1ed', 'syria': '1f1f8-1f1fe',
            'taiwan': '1f1f9-1f1fc', 'thailand': '1f1f9-1f1ed', 'tunisia': '1f1f9-1f1f3',
            'turkey': '1f1f9-1f1f7', 'ukraine': '1f1fa-1f1e6', 'uruguay': '1f1fa-1f1fe',
            'venezuela': '1f1fb-1f1ea', 'vietnam': '1f1fb-1f1f3', 'yemen': '1f1fe-1f1ea',
            'zimbabwe': '1f1ff-1f1fc',

            # Common country abbreviations and alternative names
            'usa': '1f1fa-1f1f8', 'us': '1f1fa-1f1f8', 'america': '1f1fa-1f1f8',
            'united states': '1f1fa-1f1f8', 'united states of america': '1f1fa-1f1f8',
            'uk': '1f1ec-1f1e7', 'britain': '1f1ec-1f1e7', 'great britain': '1f1ec-1f1e7',
            'england': '1f1ec-1f1e7', 'united kingdom': '1f1ec-1f1e7',
            'uae': '1f1e6-1f1ea', 'united arab emirates': '1f1e6-1f1ea', 'emirates': '1f1e6-1f1ea',
            'south korea': '1f1f0-1f1f7', 'korea': '1f1f0-1f1f7', 'korean': '1f1f0-1f1f7',
            'north korea': '1f1f0-1f1f5', 'dprk': '1f1f0-1f1f5',
            'czech republic': '1f1e8-1f1ff', 'czechia': '1f1e8-1f1ff', 'czech': '1f1e8-1f1ff',
            'russia': '1f1f7-1f1fa', 'russian federation': '1f1f7-1f1fa', 'ussr': '1f1f7-1f1fa',
            'china': '1f1e8-1f1f3', 'prc': '1f1e8-1f1f3', 'peoples republic of china': '1f1e8-1f1f3',
            'taiwan': '1f1f9-1f1fc', 'republic of china': '1f1f9-1f1fc', 'roc': '1f1f9-1f1fc',
            'palestine': '1f1f5-1f1f8', 'palestinian territories': '1f1f5-1f1f8',
            'bosnia': '1f1e7-1f1e6', 'bosnia and herzegovina': '1f1e7-1f1e6', 'bosnia herzegovina': '1f1e7-1f1e6',
            'macedonia': '1f1f2-1f1f0', 'north macedonia': '1f1f2-1f1f0',
            'congo': '1f1e8-1f1e9', 'democratic republic of congo': '1f1e8-1f1e9', 'drc': '1f1e8-1f1e9',
            'ivory coast': '1f1e8-1f1ee', 'cote divoire': '1f1e8-1f1ee',
            'myanmar': '1f1f2-1f1f2', 'burma': '1f1f2-1f1f2',
            'east timor': '1f1f9-1f1f1', 'timor leste': '1f1f9-1f1f1',
            'vatican': '1f1fb-1f1e6', 'vatican city': '1f1fb-1f1e6', 'holy see': '1f1fb-1f1e6',
            'san marino': '1f1f8-1f1f2', 'monaco': '1f1f2-1f1e8', 'liechtenstein': '1f1f1-1f1ee',

            # Regional and historical names
            'persia': '1f1ee-1f1f7', 'persian': '1f1ee-1f1f7',
            'holland': '1f1f3-1f1f1', 'dutch': '1f1f3-1f1f1',
            'scottish': '1f1ec-1f1e7', 'scotland': '1f1ec-1f1e7',
            'welsh': '1f1ec-1f1e7', 'wales': '1f1ec-1f1e7',
            'irish': '1f1ee-1f1ea', 'northern ireland': '1f1ec-1f1e7',
            'catalonia': '1f1ea-1f1f8', 'catalan': '1f1ea-1f1f8',
            'basque': '1f1ea-1f1f8', 'basque country': '1f1ea-1f1f8',
            'tibet': '1f1e8-1f1f3', 'tibetan': '1f1e8-1f1f3',
            'kurdistan': '1f1ee-1f1f6', 'kurdish': '1f1ee-1f1f6',
            'punjab': '1f1ee-1f1f3', 'punjabi': '1f1ee-1f1f3',
            'bengal': '1f1e7-1f1e9', 'bengali': '1f1e7-1f1e9',
            'tamil': '1f1ee-1f1f3', 'tamil nadu': '1f1ee-1f1f3',
            'sikh': '1f1ee-1f1f3', 'sikhism': '1f1ee-1f1f3',
            'hindu': '1f1ee-1f1f3', 'hinduism': '1f1ee-1f1f3',
            'buddhist': '1f1f9-1f1ed', 'buddhism': '1f1f9-1f1ed',
            'islamic': '1f1f8-1f1e6', 'muslim': '1f1f8-1f1e6',
            'christian': '1f1fb-1f1e6', 'christianity': '1f1fb-1f1e6',
            'jewish': '1f1ee-1f1f1', 'judaism': '1f1ee-1f1f1',
        }

        # MASSIVELY EXPANDED emoji mapping with comprehensive coverage of all common English words
        self.emoji_mapping = {
            # COMPOUND WORDS - HIGHEST PRIORITY (Add these first)
            'blue whale': '1f40b', 'blue whales': '1f40b',
            'gray whale': '1f40b', 'grey whale': '1f40b',
            'humpback whale': '1f40b', 'sperm whale': '1f40b',
            'killer whale': '1f40b', 'orca whale': '1f40b',
            'white shark': '1f988', 'great white shark': '1f988',
            'tiger shark': '1f988', 'bull shark': '1f988',
            'polar bear': '1f43b-200d-2744-fe0f', 'grizzly bear': '1f43b',
            'black bear': '1f43b', 'brown bear': '1f43b',
            'snow leopard': '1f406', 'black panther': '1f406',
            'mountain lion': '1f981', 'sea lion': '1f9ad',
            'red fox': '1f98a', 'arctic fox': '1f98a',
            'grey wolf': '1f43a', 'gray wolf': '1f43a',
            'timber wolf': '1f43a', 'pack wolf': '1f43a',
            'barn owl': '1f989', 'snowy owl': '1f989',
            'bald eagle': '1f985', 'golden eagle': '1f985',
            'sea turtle': '1f422', 'giant tortoise': '1f422',
            'green turtle': '1f422', 'leatherback turtle': '1f422',
            'poison frog': '1f438', 'tree frog': '1f438',
            'bull frog': '1f438', 'poison dart frog': '1f438',
            'king cobra': '1f40d', 'python snake': '1f40d',
            'rattlesnake': '1f40d', 'black mamba': '1f40d',
            'praying mantis': '1f997', 'stick insect': '1f997',
            'honey bee': '1f41d', 'bumble bee': '1f41d',
            'monarch butterfly': '1f98b', 'swallowtail butterfly': '1f98b',
            'fruit fly': '1f4a8', 'house fly': '1f4a8',
            'fire ant': '1f41c', 'carpenter ant': '1f41c',
            'black widow': '1f577', 'wolf spider': '1f577',
            'hermit crab': '1f980', 'blue crab': '1f980',
            'snow crab': '1f980', 'dungeness crab': '1f980',
            'maine lobster': '1f99e', 'spiny lobster': '1f99e',
            'giant squid': '1f991', 'colossal squid': '1f991',
            'vampire squid': '1f991', 'flying squid': '1f991',
            'electric eel': '1f41f', 'moray eel': '1f41f',
            'rainbow trout': '1f41f', 'salmon fish': '1f41f',
            'gold fish': '1f41f', 'angel fish': '1f41f',
            'clown fish': '1f420', 'sword fish': '1f41f',
            'tuna fish': '1f41f', 'cod fish': '1f41f',
            'blue jay': '1f426', 'cardinal bird': '1f426',
            'robin bird': '1f426', 'sparrow bird': '1f426',
            'humming bird': '1f426', 'woodpecker': '1f426',
            'peacock mantis': '1f99a', 'flamingo bird': '1f9a9',
            'guinea pig': '1f439', 'prairie dog': '1f43f',
            'flying squirrel': '1f43f', 'ground squirrel': '1f43f',
            'tree squirrel': '1f43f', 'red squirrel': '1f43f',
            'field mouse': '1f401', 'house mouse': '1f401',
            'deer mouse': '1f401', 'pocket mouse': '1f401',
            'cottontail rabbit': '1f407', 'jack rabbit': '1f407',
            'snow rabbit': '1f407', 'marsh rabbit': '1f407',
            'race horse': '1f40e', 'wild horse': '1f40e',
            'mustang horse': '1f40e', 'draft horse': '1f40e',
            'dairy cow': '1f404', 'beef cow': '1f404',
            'highland cow': '1f404', 'jersey cow': '1f404',
            'pot belly pig': '1f437', 'guinea pig': '1f439',
            'wild boar': '1f417', 'wart hog': '1f417',
            'bighorn sheep': '1f411', 'dall sheep': '1f411',
            'merino sheep': '1f411', 'jacob sheep': '1f411',
            'mountain goat': '1f410', 'billy goat': '1f410',
            'nanny goat': '1f410', 'angora goat': '1f410',
            'rhode island red': '1f414', 'leghorn chicken': '1f414',
            'bantam chicken': '1f414', 'silkie chicken': '1f414',
            'mallard duck': '1f986', 'wood duck': '1f986',
            'canvasback duck': '1f986', 'teal duck': '1f986',
            'wild turkey': '1f983', 'domestic turkey': '1f983',
            'emperor penguin': '1f427', 'king penguin': '1f427',
            'adelie penguin': '1f427', 'chinstrap penguin': '1f427',
            'great horned owl': '1f989', 'screech owl': '1f989',

            # COMPREHENSIVE ANIMALS - COMPLETE ANIMAL KINGDOM COVERAGE
            # Mammals - Land Animals
            'cat': '1f408', 'cats': '1f408', 'kitten': '1f408', 'kittens': '1f408', 'feline': '1f408', 'kitty': '1f408',
            'dog': '1f415', 'dogs': '1f415', 'puppy': '1f415', 'puppies': '1f415', 'canine': '1f415', 'pup': '1f415',
            'horse': '1f40e', 'horses': '1f40e', 'stallion': '1f40e', 'mare': '1f40e', 'pony': '1f40e', 'foal': '1f40e',
            'cow': '1f404', 'cows': '1f404', 'cattle': '1f404', 'bull': '1f402', 'bulls': '1f402', 'ox': '1f402',
            'pig': '1f437', 'pigs': '1f437', 'swine': '1f437', 'hog': '1f437', 'boar': '1f417', 'piglet': '1f437',
            'sheep': '1f411', 'lamb': '1f411', 'lambs': '1f411', 'wool': '1f411', 'ewe': '1f411', 'ram': '1f411',
            'goat': '1f410', 'goats': '1f410', 'kid': '1f410', 'nanny': '1f410', 'billy': '1f410',
            'rabbit': '1f407', 'rabbits': '1f407', 'bunny': '1f407', 'bunnies': '1f407', 'hare': '1f407',
            'cottontail': '1f407',
            'mouse': '1f401', 'mice': '1f401', 'rat': '1f401', 'rats': '1f401', 'rodent': '1f401', 'hamster': '1f439',
            'elephant': '1f418', 'elephants': '1f418', 'trunk': '1f418', 'ivory': '1f418', 'mammoth': '1f418',
            'lion': '1f981', 'lions': '1f981', 'lioness': '1f981', 'pride': '1f981', 'mane': '1f981', 'roar': '1f981',
            'tiger': '1f405', 'tigers': '1f405', 'stripes': '1f405', 'siberian': '1f405', 'bengal': '1f405',
            'leopard': '1f406', 'leopards': '1f406', 'spots': '1f406', 'cheetah': '1f406', 'jaguar': '1f406',
            'bear': '1f43b', 'bears': '1f43b', 'cub': '1f43b', 'cubs': '1f43b', 'ursine': '1f43b',
            'wolf': '1f43a', 'wolves': '1f43a', 'pack': '1f43a', 'howl': '1f43a', 'alpha': '1f43a',
            'fox': '1f98a', 'foxes': '1f98a', 'vixen': '1f98a', 'sly': '1f98a', 'cunning': '1f98a',
            'deer': '1f98c', 'doe': '1f98c', 'buck': '1f98c', 'fawn': '1f98c', 'antlers': '1f98c', 'stag': '1f98c',
            'zebra': '1f993', 'zebras': '1f993', 'stripes': '1f993', 'savanna': '1f993',
            'giraffe': '1f992', 'giraffes': '1f992', 'neck': '1f992', 'tall': '1f992', 'spots': '1f992',
            'hippo': '1f99b', 'hippopotamus': '1f99b', 'water': '1f99b', 'river': '1f99b',
            'rhino': '1f98f', 'rhinoceros': '1f98f', 'horn': '1f98f', 'thick skin': '1f98f',
            'monkey': '1f412', 'monkeys': '1f412', 'ape': '1f98d', 'apes': '1f98d', 'primate': '1f412',
            'gorilla': '1f98d', 'gorillas': '1f98d', 'silverback': '1f98d', 'strong': '1f98d',
            'chimpanzee': '1f98d', 'chimp': '1f98d', 'chimps': '1f98d', 'banana': '1f34c',
            'orangutan': '1f98d', 'orangutans': '1f98d', 'swing': '1f98d', 'tree': '1f333',
            'kangaroo': '1f998', 'kangaroos': '1f998', 'joey': '1f998', 'pouch': '1f998', 'hop': '1f998',
            'koala': '1f428', 'koalas': '1f428', 'eucalyptus': '1f428', 'marsupial': '1f428',
            'panda': '1f43c', 'pandas': '1f43c', 'bamboo': '1f43c', 'china': '1f43c', 'giant panda': '1f43c',
            'sloth': '1f9a5', 'sloths': '1f9a5', 'slow': '1f9a5', 'tree': '1f333', 'hang': '1f9a5',
            'raccoon': '1f99d', 'raccoons': '1f99d', 'mask': '1f99d', 'trash': '1f99d', 'nocturnal': '1f99d',
            'skunk': '1f9a8', 'skunks': '1f9a8', 'smell': '1f9a8', 'spray': '1f9a8', 'stripe': '1f9a8',
            'badger': '1f9a1', 'badgers': '1f9a1', 'burrow': '1f9a1', 'honey badger': '1f9a1',
            'otter': '1f9a6', 'otters': '1f9a6', 'water': '1f4a7', 'swim': '1f3ca', 'playful': '1f9a6',
            'beaver': '1f9ab', 'beavers': '1f9ab', 'dam': '1f9ab', 'wood': '1f333', 'teeth': '1f9ab',
            'hedgehog': '1f994', 'hedgehogs': '1f994', 'spiky': '1f994', 'quill': '1f994', 'roll': '1f994',
            'porcupine': '1f994', 'porcupines': '1f994', 'quills': '1f994', 'spines': '1f994',
            'armadillo': '1f43e', 'armadillos': '1f43e', 'armor': '1f43e', 'shell': '1f43e', 'roll up': '1f43e',
            'anteater': '1f43e', 'anteaters': '1f43e', 'ants': '1f41c', 'long nose': '1f43e',
            'llama': '1f999', 'llamas': '1f999', 'alpaca': '1f999', 'wool': '1f999', 'spit': '1f999',
            'camel': '1f42a', 'camels': '1f42a', 'hump': '1f42a', 'desert': '1f3dc', 'dromedary': '1f42a',
            'yak': '1f402', 'yaks': '1f402', 'tibet': '1f402', 'fur': '1f402', 'mountain': '26f0',
            'bison': '1f403', 'buffalo': '1f403', 'american': '1f403', 'herd': '1f403', 'plains': '1f403',
            'moose': '1f98c', 'elk': '1f98c', 'antlers': '1f98c', 'forest': '1f333', 'large': '1f98c',
            'reindeer': '1f98c', 'caribou': '1f98c', 'santa': '1f98c', 'sleigh': '1f98c', 'christmas': '1f384',

            # Marine Animals
            'whale': '1f40b', 'whales': '1f40b', 'ocean': '1f30a', 'marine': '1f40b', 'blowhole': '1f40b',
            'dolphin': '1f42c', 'dolphins': '1f42c', 'smart': '1f42c', 'playful': '1f42c', 'echolocation': '1f42c',
            'shark': '1f988', 'sharks': '1f988', 'teeth': '1f988', 'predator': '1f988', 'fin': '1f988',
            'fish': '1f41f', 'fishes': '1f41f', 'swim': '1f41f', 'scales': '1f41f', 'gills': '1f41f',
            'goldfish': '1f420', 'tropical fish': '1f420', 'aquarium': '1f420', 'bowl': '1f420',
            'octopus': '1f419', 'tentacles': '1f419', 'eight': '1f419', 'ink': '1f419', 'sucker': '1f419',
            'squid': '1f991', 'tentacles': '1f991', 'ink': '1f991', 'beak': '1f991', 'mantle': '1f991',
            'jellyfish': '1f95f', 'sting': '1f95f', 'transparent': '1f95f', 'float': '1f95f', 'bell': '1f95f',
            'seahorse': '1f433', 'coral': '1f433', 'reef': '1f433', 'tiny': '1f433', 'curved': '1f433',
            'starfish': '2b50', 'five arms': '2b50', 'regenerate': '2b50', 'sea star': '2b50',
            'crab': '1f980', 'crabs': '1f980', 'claws': '1f980', 'shell': '1f980', 'sideways': '1f980',
            'lobster': '1f99e', 'lobsters': '1f99e', 'claws': '1f99e', 'red': '1f99e', 'marine': '1f99e',
            'shrimp': '1f990', 'prawns': '1f990', 'small': '1f990', 'cocktail': '1f990', 'pink': '1f990',
            'oyster': '1f9aa', 'pearl': '1f9aa', 'shell': '1f9aa', 'filter': '1f9aa', 'bivalve': '1f9aa',
            'clam': '1f9aa', 'scallop': '1f9aa', 'mussel': '1f9aa', 'seafood': '1f9aa',
            'seal': '1f9ad', 'seals': '1f9ad', 'flipper': '1f9ad', 'bark': '1f9ad', 'whiskers': '1f9ad',
            'walrus': '1f9ad', 'tusks': '1f9ad', 'arctic': '1f9ad', 'ice': '1f9ad', 'blubber': '1f9ad',
            'manatee': '1f9ad', 'sea cow': '1f9ad', 'gentle': '1f9ad', 'slow': '1f9ad', 'herbivore': '1f9ad',

            # Birds - Comprehensive Coverage
            'bird': '1f426', 'birds': '1f426', 'fly': '1f426', 'wing': '1f426', 'feather': '1f426',
            'eagle': '1f985', 'eagles': '1f985', 'soar': '1f985', 'talons': '1f985', 'majestic': '1f985',
            'hawk': '1f985', 'hawks': '1f985', 'predator': '1f985', 'hunt': '1f985', 'sharp': '1f985',
            'falcon': '1f985', 'falcons': '1f985', 'fast': '1f985', 'dive': '1f985', 'speed': '1f985',
            'owl': '1f989', 'owls': '1f989', 'hoot': '1f989', 'wise': '1f989', 'nocturnal': '1f989',
            'robin': '1f426', 'robins': '1f426', 'red breast': '1f426', 'spring': '1f426', 'worm': '1f426',
            'sparrow': '1f426', 'sparrows': '1f426', 'small': '1f426', 'brown': '1f426', 'chirp': '1f426',
            'crow': '1f426', 'crows': '1f426', 'black': '1f426', 'caw': '1f426', 'smart': '1f426',
            'raven': '1f426', 'ravens': '1f426', 'black': '1f426', 'large': '1f426', 'croak': '1f426',
            'blue jay': '1f426', 'jay': '1f426', 'blue': '1f426', 'crest': '1f426', 'loud': '1f426',
            'cardinal': '1f426', 'cardinals': '1f426', 'red': '1f426', 'bright': '1f426', 'seed': '1f426',
            'woodpecker': '1f426', 'woodpeckers': '1f426', 'peck': '1f426', 'tree': '1f426', 'drum': '1f426',
            'hummingbird': '1f426', 'hummingbirds': '1f426', 'tiny': '1f426', 'fast': '1f426', 'nectar': '1f426',
            'swan': '1f9a2', 'swans': '1f9a2', 'white': '1f9a2', 'graceful': '1f9a2', 'lake': '1f9a2',
            'goose': '1f986', 'geese': '1f986', 'honk': '1f986', 'migrate': '1f986', 'v formation': '1f986',
            'duck': '1f986', 'ducks': '1f986', 'quack': '1f986', 'pond': '1f986', 'water': '1f986',
            'chicken': '1f414', 'chickens': '1f414', 'hen': '1f414', 'rooster': '1f413', 'coop': '1f414',
            'turkey': '1f983', 'turkeys': '1f983', 'gobble': '1f983', 'thanksgiving': '1f983', 'feathers': '1f983',
            'peacock': '1f99a', 'peacocks': '1f99a', 'colorful': '1f99a', 'tail': '1f99a', 'display': '1f99a',
            'flamingo': '1f9a9', 'flamingos': '1f9a9', 'pink': '1f9a9', 'long legs': '1f9a9', 'balance': '1f9a9',
            'penguin': '1f427', 'penguins': '1f427', 'waddle': '1f427', 'antarctica': '1f427', 'ice': '1f427',
            'parrot': '1f99c', 'parrots': '1f99c', 'colorful': '1f99c', 'talk': '1f99c', 'tropical': '1f99c',
            'canary': '1f426', 'canaries': '1f426', 'yellow': '1f426', 'sing': '1f426', 'cage': '1f426',
            'pigeon': '1f54a', 'pigeons': '1f54a', 'dove': '1f54a', 'peace': '1f54a', 'city': '1f54a',
            'seagull': '1f426', 'seagulls': '1f426', 'beach': '1f426', 'coast': '1f426', 'scavenge': '1f426',
            'pelican': '1f426', 'pelicans': '1f426', 'pouch': '1f426', 'fish': '1f426', 'large bill': '1f426',
            'stork': '1f426', 'storks': '1f426', 'baby': '1f426', 'delivery': '1f426', 'long legs': '1f426',
            'crane': '1f426', 'cranes': '1f426', 'tall': '1f426', 'elegant': '1f426', 'dance': '1f426',
            'ostrich': '1f426', 'ostriches': '1f426', 'large': '1f426', 'run': '1f426', 'flightless': '1f426',
            'emu': '1f426', 'emus': '1f426', 'australia': '1f426', 'large': '1f426', 'run': '1f426',
            'kiwi': '1f426', 'kiwis': '1f426', 'new zealand': '1f426', 'flightless': '1f426', 'nocturnal': '1f426',

            # Reptiles and Amphibians
            'snake': '1f40d', 'snakes': '1f40d', 'slither': '1f40d', 'scales': '1f40d', 'venom': '1f40d',
            'lizard': '1f98e', 'lizards': '1f98e', 'gecko': '1f98e', 'iguana': '1f98e', 'chameleon': '1f98e',
            'turtle': '1f422', 'turtles': '1f422', 'shell': '1f422', 'slow': '1f422', 'tortoise': '1f422',
            'crocodile': '1f40a', 'crocodiles': '1f40a', 'alligator': '1f40a', 'teeth': '1f40a', 'swamp': '1f40a',
            'frog': '1f438', 'frogs': '1f438', 'hop': '1f438', 'pond': '1f438', 'ribbit': '1f438',
            'toad': '1f438', 'toads': '1f438', 'bumpy': '1f438', 'warts': '1f438', 'hop': '1f438',
            'salamander': '1f438', 'salamanders': '1f438', 'newt': '1f438', 'moist': '1f438', 'regenerate': '1f438',
            'dragon': '1f409', 'dragons': '1f409', 'mythical': '1f409', 'fire': '1f409', 'wings': '1f409',

            # Insects and Arachnids - MASSIVE EXPANSION
            'bee': '1f41d', 'bees': '1f41d', 'honey': '1f41d', 'buzz': '1f41d', 'hive': '1f41d',
            'ant': '1f41c', 'ants': '1f41c', 'colony': '1f41c', 'worker': '1f41c', 'march': '1f41c',
            'butterfly': '1f98b', 'butterflies': '1f98b', 'metamorphosis': '1f98b', 'colorful': '1f98b',
            'flutter': '1f98b',
            'moth': '1f98b', 'moths': '1f98b', 'nocturnal': '1f98b', 'light': '1f98b', 'dust': '1f98b',
            'spider': '1f577', 'spiders': '1f577', 'web': '1f577', 'eight legs': '1f577', 'silk': '1f577',
            'scorpion': '1f982', 'scorpions': '1f982', 'sting': '1f982', 'tail': '1f982', 'desert': '1f982',
            'fly': '1f4a8', 'flies': '1f4a8', 'buzz': '1f4a8', 'annoying': '1f4a8', 'swat': '1f4a8',
            'mosquito': '1f99f', 'mosquitoes': '1f99f', 'bite': '1f99f', 'blood': '1f99f', 'itch': '1f99f',
            'wasp': '1f41d', 'wasps': '1f41d', 'sting': '1f41d', 'yellow': '1f41d', 'aggressive': '1f41d',
            'hornet': '1f41d', 'hornets': '1f41d', 'large': '1f41d', 'dangerous': '1f41d', 'nest': '1f41d',
            'cricket': '1f997', 'crickets': '1f997', 'chirp': '1f997', 'night': '1f997', 'jump': '1f997',
            'grasshopper': '1f997', 'grasshoppers': '1f997', 'green': '1f997', 'hop': '1f997', 'legs': '1f997',
            'mantis': '1f997', 'praying mantis': '1f997', 'predator': '1f997', 'prey': '1f997', 'arms': '1f997',
            'cockroach': '1f4a8', 'cockroaches': '1f4a8', 'pest': '1f4a8', 'survive': '1f4a8', 'dirty': '1f4a8',
            'beetle': '1f41b', 'beetles': '1f41b', 'hard shell': '1f41b', 'ladybug': '1f41b', 'spots': '1f41b',
            'caterpillar': '1f41b', 'caterpillars': '1f41b', 'larva': '1f41b', 'leaf': '1f41b', 'crawl': '1f41b',
            'worm': '1f41b', 'worms': '1f41b', 'earthworm': '1f41b', 'soil': '1f41b', 'underground': '1f41b',
            'snail': '1f40c', 'snails': '1f40c', 'shell': '1f40c', 'slow': '1f40c', 'slime': '1f40c',
            'slug': '1f40c', 'slugs': '1f40c', 'slime': '1f40c', 'garden': '1f40c', 'no shell': '1f40c',
            'dragonfly': '1f98b', 'dragonflies': '1f98b', 'water': '1f98b', 'fast': '1f98b', 'iridescent': '1f98b',
            'firefly': '1f4a1', 'fireflies': '1f4a1', 'glow': '1f4a1', 'light': '1f4a1', 'summer': '1f4a1',
            'termite': '1f41c', 'termites': '1f41c', 'wood': '1f41c', 'damage': '1f41c', 'colony': '1f41c',
            'flea': '1f4a8', 'fleas': '1f4a8', 'jump': '1f4a8', 'pet': '1f4a8', 'bite': '1f4a8',
            'tick': '1f577', 'ticks': '1f577', 'blood': '1f577', 'disease': '1f577', 'parasite': '1f577',
            'louse': '1f4a8', 'lice': '1f4a8', 'hair': '1f4a8', 'itch': '1f4a8', 'nit': '1f4a8',
            'mite': '1f577', 'mites': '1f577', 'tiny': '1f577', 'dust': '1f577', 'allergy': '1f577',

            # COMPREHENSIVE FOOD COVERAGE
            # Fruits
            'apple': '1f34e', 'apples': '1f34e', 'red apple': '1f34e', 'green apple': '1f34f',
            'banana': '1f34c', 'bananas': '1f34c', 'yellow': '1f34c', 'peel': '1f34c', 'potassium': '1f34c',
            'orange': '1f34a', 'oranges': '1f34a', 'citrus': '1f34a', 'vitamin c': '1f34a', 'juice': '1f34a',
            'lemon': '1f34b', 'lemons': '1f34b', 'sour': '1f34b', 'yellow': '1f34b', 'citrus': '1f34b',
            'lime': '1f34b', 'limes': '1f34b', 'green': '1f34b', 'sour': '1f34b', 'cocktail': '1f34b',
            'grape': '1f347', 'grapes': '1f347', 'vine': '1f347', 'wine': '1f347', 'cluster': '1f347',
            'strawberry': '1f353', 'strawberries': '1f353', 'red': '1f353', 'seeds': '1f353', 'sweet': '1f353',
            'cherry': '1f352', 'cherries': '1f352', 'red': '1f352', 'pit': '1f352', 'tree': '1f352',
            'peach': '1f351', 'peaches': '1f351', 'fuzzy': '1f351', 'pit': '1f351', 'sweet': '1f351',
            'pear': '1f350', 'pears': '1f350', 'green': '1f350', 'sweet': '1f350', 'juice': '1f350',
            'pineapple': '1f34d', 'pineapples': '1f34d', 'tropical': '1f34d', 'spiky': '1f34d', 'crown': '1f34d',
            'mango': '1f96d', 'mangos': '1f96d', 'tropical': '1f96d', 'sweet': '1f96d', 'orange': '1f96d',
            'watermelon': '1f349', 'watermelons': '1f349', 'red': '1f349', 'seeds': '1f349', 'summer': '1f349',
            'melon': '1f348', 'melons': '1f348', 'cantaloupe': '1f348', 'sweet': '1f348', 'orange': '1f348',
            'kiwi': '1f95d', 'kiwis': '1f95d', 'green': '1f95d', 'fuzzy': '1f95d', 'tart': '1f95d',
            'coconut': '1f965', 'coconuts': '1f965', 'tropical': '1f965', 'milk': '1f965', 'oil': '1f965',
            'avocado': '1f951', 'avocados': '1f951', 'green': '1f951', 'healthy': '1f951', 'guacamole': '1f951',
            'blueberry': '1fad0', 'blueberries': '1fad0', 'blue': '1fad0', 'antioxidants': '1fad0', 'small': '1fad0',
            'blackberry': '1fad0', 'blackberries': '1fad0', 'dark': '1fad0', 'tart': '1fad0', 'thorns': '1fad0',
            'raspberry': '1fad0', 'raspberries': '1fad0', 'red': '1fad0', 'tart': '1fad0', 'seeds': '1fad0',
            'cranberry': '1fad0', 'cranberries': '1fad0', 'red': '1fad0', 'tart': '1fad0', 'thanksgiving': '1fad0',
            'papaya': '1f96d', 'papayas': '1f96d', 'tropical': '1f96d', 'orange': '1f96d', 'seeds': '1f96d',
            'fig': '1fad0', 'figs': '1fad0', 'purple': '1fad0', 'sweet': '1fad0', 'ancient': '1fad0',
            'date': '1fad0', 'dates': '1fad0', 'brown': '1fad0', 'sweet': '1fad0', 'desert': '1fad0',
            'pomegranate': '1fad0', 'pomegranates': '1fad0', 'red': '1fad0', 'seeds': '1fad0', 'antioxidants': '1fad0',
            'plum': '1f351', 'plums': '1f351', 'purple': '1f351', 'sweet': '1f351', 'pit': '1f351',
            'apricot': '1f351', 'apricots': '1f351', 'orange': '1f351', 'small': '1f351', 'sweet': '1f351',

            # Vegetables
            'carrot': '1f955', 'carrots': '1f955', 'orange': '1f955', 'vitamin a': '1f955', 'root': '1f955',
            'potato': '1f954', 'potatoes': '1f954', 'starch': '1f954', 'fries': '1f35f', 'chips': '1f954',
            'tomato': '1f345', 'tomatoes': '1f345', 'red': '1f345', 'vine': '1f345', 'sauce': '1f345',
            'onion': '1f9c5', 'onions': '1f9c5', 'layers': '1f9c5', 'cry': '1f9c5', 'flavor': '1f9c5',
            'garlic': '1f9c4', 'clove': '1f9c4', 'smell': '1f9c4', 'flavor': '1f9c4', 'bulb': '1f9c4',
            'pepper': '1f336', 'peppers': '1f336', 'spicy': '1f336', 'hot': '1f336', 'chili': '1f336',
            'bell pepper': '1fad1', 'bell peppers': '1fad1', 'sweet': '1fad1', 'colorful': '1fad1', 'crisp': '1fad1',
            'cucumber': '1f952', 'cucumbers': '1f952', 'green': '1f952', 'cool': '1f952', 'water': '1f952',
            'lettuce': '1f96c', 'leafy': '1f96c', 'green': '1f96c', 'salad': '1f96c', 'crisp': '1f96c',
            'spinach': '1f96c', 'iron': '1f96c', 'green': '1f96c', 'leafy': '1f96c', 'popeye': '1f96c',
            'cabbage': '1f96c', 'green': '1f96c', 'round': '1f96c', 'layers': '1f96c', 'cole': '1f96c',
            'broccoli': '1f966', 'green': '1f966', 'tree': '1f966', 'healthy': '1f966', 'vitamin': '1f966',
            'cauliflower': '1f966', 'white': '1f966', 'florets': '1f966', 'healthy': '1f966', 'cruciferous': '1f966',
            'corn': '1f33d', 'kernel': '1f33d', 'yellow': '1f33d', 'cob': '1f33d', 'maize': '1f33d',
            'peas': '1fad0', 'pod': '1fad0', 'green': '1fad0', 'sweet': '1fad0', 'round': '1fad0',
            'bean': '1fad0', 'beans': '1fad0', 'protein': '1fad0', 'fiber': '1fad0', 'legume': '1fad0',
            'mushroom': '1f344', 'mushrooms': '1f344', 'fungi': '1f344', 'cap': '1f344', 'stem': '1f344',
            'eggplant': '1f346', 'eggplants': '1f346', 'purple': '1f346', 'shiny': '1f346', 'oval': '1f346',
            'zucchini': '1f952', 'squash': '1f952', 'green': '1f952', 'summer': '1f952', 'long': '1f952',
            'pumpkin': '1f383', 'pumpkins': '1f383', 'orange': '1f383', 'halloween': '1f383', 'jack': '1f383',
            'celery': '1f96c', 'stalk': '1f96c', 'green': '1f96c', 'crisp': '1f96c', 'stringy': '1f96c',
            'radish': '1f955', 'radishes': '1f955', 'red': '1f955', 'spicy': '1f955', 'root': '1f955',
            'beet': '1f955', 'beets': '1f955', 'red': '1f955', 'sweet': '1f955', 'earthy': '1f955',
            'turnip': '1f955', 'turnips': '1f955', 'white': '1f955', 'root': '1f955', 'bitter': '1f955',
            'parsnip': '1f955', 'parsnips': '1f955', 'white': '1f955', 'sweet': '1f955', 'root': '1f955',
            'sweet potato': '1f360', 'yam': '1f360', 'orange': '1f360', 'sweet': '1f360', 'vitamin': '1f360',
            'artichoke': '1f96c', 'artichokes': '1f96c', 'heart': '1f96c', 'leaves': '1f96c', 'thorny': '1f96c',
            'asparagus': '1f96c', 'spears': '1f96c', 'green': '1f96c', 'tender': '1f96c', 'spring': '1f96c',
            'leek': '1f9c5', 'leeks': '1f9c5', 'green': '1f9c5', 'white': '1f9c5', 'mild': '1f9c5',
            'scallion': '1f9c5', 'green onion': '1f9c5', 'chives': '1f9c5', 'spring onion': '1f9c5',
            'ginger': '1fad0', 'root': '1fad0', 'spicy': '1fad0', 'medicinal': '1fad0', 'knobby': '1fad0',

            # Grains and Starches
            'bread': '1f35e', 'loaf': '1f35e', 'wheat': '1f35e', 'slice': '1f35e', 'crust': '1f35e',
            'rice': '1f35a', 'grain': '1f35a', 'white': '1f35a', 'steamed': '1f35a', 'bowl': '1f35a',
            'pasta': '1f35d', 'noodles': '1f35d', 'spaghetti': '1f35d', 'sauce': '1f35d', 'italian': '1f35d',
            'cereal': '1f963', 'breakfast': '1f963', 'milk': '1f963', 'bowl': '1f963', 'grain': '1f963',
            'oats': '1f963', 'oatmeal': '1f963', 'fiber': '1f963', 'healthy': '1f963', 'breakfast': '1f963',
            'quinoa': '1f35a', 'superfood': '1f35a', 'protein': '1f35a', 'grain': '1f35a', 'ancient': '1f35a',
            'barley': '1f35a', 'grain': '1f35a', 'soup': '1f35a', 'beer': '1f35a', 'ancient': '1f35a',
            'flour': '1f35e', 'powder': '1f35e', 'baking': '1f35e', 'white': '1f35e', 'grain': '1f35e',

            # Proteins
            'meat': '1f969', 'protein': '1f969', 'beef': '1f969', 'steak': '1f969', 'red': '1f969',
            'chicken': '1f357', 'poultry': '1f357', 'breast': '1f357', 'wing': '1f357', 'drumstick': '1f357',
            'pork': '1f953', 'pig': '1f953', 'bacon': '1f953', 'ham': '1f953', 'chop': '1f953',
            'fish': '1f41f', 'seafood': '1f41f', 'omega': '1f41f', 'fillet': '1f41f', 'fresh': '1f41f',
            'salmon': '1f41f', 'pink': '1f41f', 'omega': '1f41f', 'healthy': '1f41f', 'grill': '1f41f',
            'tuna': '1f41f', 'can': '1f41f', 'sashimi': '1f41f', 'red': '1f41f', 'mercury': '1f41f',
            'shrimp': '1f990', 'prawns': '1f990', 'cocktail': '1f990', 'pink': '1f990', 'shellfish': '1f990',
            'crab': '1f980', 'claws': '1f980', 'shell': '1f980', 'red': '1f980', 'seafood': '1f980',
            'lobster': '1f99e', 'claws': '1f99e', 'red': '1f99e', 'expensive': '1f99e', 'maine': '1f99e',
            'egg': '1f95a', 'eggs': '1f95a', 'protein': '1f95a', 'yolk': '1f95a', 'white': '1f95a',
            'milk': '1f95b', 'dairy': '1f95b', 'calcium': '1f95b', 'white': '1f95b', 'glass': '1f95b',
            'cheese': '1f9c0', 'dairy': '1f9c0', 'aged': '1f9c0', 'yellow': '1f9c0', 'slice': '1f9c0',
            'yogurt': '1f95b', 'probiotic': '1f95b', 'culture': '1f95b', 'healthy': '1f95b', 'thick': '1f95b',
            'tofu': '1f35a', 'soy': '1f35a', 'protein': '1f35a', 'vegetarian': '1f35a', 'cube': '1f35a',
            'nuts': '1f95c', 'almonds': '1f95c', 'protein': '1f95c', 'healthy': '1f95c', 'crunchy': '1f95c',
            'peanut': '1f95c', 'peanuts': '1f95c', 'butter': '1f95c', 'allergy': '1f95c', 'shell': '1f95c',

            # Desserts and Sweets
            'cake': '1f370', 'birthday': '1f370', 'frosting': '1f370', 'sweet': '1f370', 'layers': '1f370',
            'cookie': '1f36a', 'cookies': '1f36a', 'chocolate chip': '1f36a', 'baked': '1f36a', 'sweet': '1f36a',
            'candy': '1f36c', 'sweet': '1f36c', 'sugar': '1f36c', 'colorful': '1f36c', 'treat': '1f36c',
            'chocolate': '1f36b', 'cocoa': '1f36b', 'dark': '1f36b', 'sweet': '1f36b', 'bar': '1f36b',
            'ice cream': '1f368', 'frozen': '1f368', 'cold': '1f368', 'scoop': '1f368', 'cone': '1f368',
            'pie': '1f967', 'crust': '1f967', 'filling': '1f967', 'slice': '1f967', 'dessert': '1f967',
            'donut': '1f369', 'doughnut': '1f369', 'fried': '1f369', 'glaze': '1f369', 'hole': '1f369',
            'cupcake': '1f9c1', 'small cake': '1f9c1', 'frosting': '1f9c1', 'sweet': '1f9c1', 'individual': '1f9c1',
            'muffin': '1f9c1', 'breakfast': '1f9c1', 'blueberry': '1f9c1', 'baked': '1f9c1', 'sweet': '1f9c1',
            'pancake': '1f95e', 'breakfast': '1f95e', 'syrup': '1f95e', 'stack': '1f95e', 'fluffy': '1f95e',
            'waffle': '1f9c7', 'breakfast': '1f9c7', 'squares': '1f9c7', 'syrup': '1f9c7', 'crispy': '1f9c7',
            'honey': '1f36f', 'bee': '1f36f', 'sweet': '1f36f', 'golden': '1f36f', 'natural': '1f36f',
            'syrup': '1f36f', 'maple': '1f36f', 'pancakes': '1f36f', 'sweet': '1f36f', 'sticky': '1f36f',
            'jam': '1fad0', 'jelly': '1fad0', 'preserve': '1fad0', 'fruit': '1fad0', 'spread': '1fad0',

            # Drinks
            'water': '1f4a7', 'clear': '1f4a7', 'hydration': '1f4a7', 'pure': '1f4a7', 'essential': '1f4a7',
            'coffee': '2615', 'caffeine': '2615', 'hot': '2615', 'morning': '2615', 'bean': '2615',
            'tea': '1fad6', 'hot': '1fad6', 'leaf': '1fad6', 'green': '1fad6', 'black': '1fad6',
            'juice': '1f9c3', 'fruit': '1f9c3', 'orange': '1f9c3', 'vitamin': '1f9c3', 'fresh': '1f9c3',
            'soda': '1f95a', 'carbonated': '1f95a', 'fizzy': '1f95a', 'sweet': '1f95a', 'cola': '1f95a',
            'beer': '1f37a', 'alcohol': '1f37a', 'hops': '1f37a', 'foam': '1f37a', 'cold': '1f37a',
            'wine': '1f377', 'grape': '1f377', 'red': '1f377', 'white': '1f377', 'glass': '1f377',
            'cocktail': '1f378', 'mixed drink': '1f378', 'alcohol': '1f378', 'bar': '1f378', 'fancy': '1f378',
            'smoothie': '1f9c3', 'blended': '1f9c3', 'fruit': '1f9c3', 'healthy': '1f9c3', 'thick': '1f9c3',
            'shake': '1f95b', 'milkshake': '1f95b', 'thick': '1f95b', 'sweet': '1f95b', 'cold': '1f95b',

            # Basic emotions and expressions
            'happy': '1f60a', 'smile': '1f642', 'joy': '1f602', 'laugh': '1f602', 'grin': '1f601',
            'sad': '1f622', 'cry': '1f622', 'tears': '1f622', 'weep': '1f622', 'sorrow': '1f622',
            'angry': '1f620', 'mad': '1f620', 'rage': '1f621', 'furious': '1f621', 'upset': '1f620',
            'excited': '1f604', 'thrilled': '1f604', 'enthusiastic': '1f604', 'pumped': '1f604',
            'calm': '1f60c', 'peaceful': '1f60c', 'relaxed': '1f60c', 'serene': '1f60c', 'tranquil': '1f60c',
            'worried': '1f61f', 'anxious': '1f630', 'nervous': '1f630', 'concerned': '1f61f', 'stress': '1f630',
            'surprised': '1f632', 'shocked': '1f631', 'amazed': '1f632', 'astonished': '1f632', 'stunned': '1f631',
            'confused': '1f615', 'puzzled': '1f914', 'thinking': '1f914', 'perplexed': '1f615', 'bewildered': '1f615',
            'tired': '1f634', 'sleepy': '1f634', 'exhausted': '1f634', 'weary': '1f634', 'drowsy': '1f634',
            'love': '2764', 'heart': '2764', 'romance': '1f496', 'affection': '2764', 'adore': '2764',
            'scared': '1f628', 'afraid': '1f628', 'fearful': '1f628', 'terrified': '1f631', 'frightened': '1f628',
            'embarrassed': '1f633', 'shy': '1f633', 'bashful': '1f633', 'modest': '1f633', 'timid': '1f633',
            'proud': '1f929', 'confident': '1f929', 'smug': '1f929', 'arrogant': '1f929', 'boastful': '1f929',
            'jealous': '1f47f', 'envious': '1f47f', 'resentful': '1f47f', 'covetous': '1f47f',
            'guilty': '1f614', 'ashamed': '1f614', 'remorseful': '1f614', 'regret': '1f614', 'sorry': '1f614',
            'hopeful': '1f929', 'optimistic': '1f929', 'positive': '1f929', 'upbeat': '1f929', 'cheerful': '1f60a',
            'disappointed': '1f61e', 'let down': '1f61e', 'discouraged': '1f61e', 'dejected': '1f61e',
            'frustrated': '1f624', 'annoyed': '1f624', 'irritated': '1f624', 'bothered': '1f624', 'vexed': '1f624',
            'grateful': '1f64f', 'thankful': '1f64f', 'appreciative': '1f64f', 'blessed': '1f64f',
            'lonely': '1f614', 'isolated': '1f614', 'alone': '1f614', 'solitary': '1f614', 'abandoned': '1f614',
            'curious': '1f914', 'interested': '1f914', 'wondering': '1f914', 'inquisitive': '1f914', 'nosy': '1f914',
            'bored': '1f634', 'uninterested': '1f634', 'dull': '1f634', 'tedious': '1f634', 'monotonous': '1f634',
            'disgusted': '1f922', 'revolted': '1f922', 'repulsed': '1f922', 'sickened': '1f922', 'nauseated': '1f922',

            # Actions and activities - MASSIVE EXPANSION
            'sit': '1fa91', 'sitting': '1fa91', 'seated': '1fa91', 'chair': '1fa91', 'rest': '1fa91',
            'stand': '1f9cd', 'standing': '1f9cd', 'upright': '1f9cd', 'erect': '1f9cd', 'vertical': '1f9cd',
            'walk': '1f6b6', 'walking': '1f6b6', 'stroll': '1f6b6', 'amble': '1f6b6', 'pace': '1f6b6',
            'run': '1f3c3', 'running': '1f3c3', 'jog': '1f3c3', 'sprint': '1f3c3', 'dash': '1f3c3',
            'jump': '1f938', 'jumping': '1f938', 'leap': '1f938', 'bound': '1f938', 'hop': '1f938',
            'climb': '1f9d7', 'climbing': '1f9d7', 'ascend': '1f9d7', 'scale': '1f9d7', 'mount': '1f9d7',
            'swim': '1f3ca', 'swimming': '1f3ca', 'float': '1f3ca', 'stroke': '1f3ca', 'dive': '1f3ca',
            'fly': '2708', 'flying': '2708', 'soar': '2708', 'glide': '2708', 'hover': '2708',
            'drive': '1f697', 'driving': '1f697', 'steer': '1f697', 'operate': '1f697', 'pilot': '1f697',
            'ride': '1f6b4', 'riding': '1f6b4', 'mount': '1f6b4', 'cycle': '1f6b4', 'pedal': '1f6b4',
            'dance': '1f483', 'dancing': '1f483', 'ballet': '1f483', 'waltz': '1f483', 'tango': '1f483',
            'sing': '1f3a4', 'singing': '1f3a4', 'vocalize': '1f3a4', 'croon': '1f3a4', 'chant': '1f3a4',
            'play': '1f3ae', 'playing': '1f3ae', 'game': '1f3ae', 'fun': '1f3ae', 'recreation': '1f3ae',
            'work': '1f4bc', 'working': '1f4bc', 'labor': '1f4bc', 'job': '1f4bc', 'employment': '1f4bc',
            'study': '1f4d6', 'studying': '1f4d6', 'learn': '1f4d6', 'research': '1f4d6', 'examine': '1f4d6',
            'read': '1f4d6', 'reading': '1f4d6', 'peruse': '1f4d6', 'scan': '1f4d6', 'browse': '1f4d6',
            'write': '270d', 'writing': '270d', 'compose': '270d', 'draft': '270d', 'scribe': '270d',
            'draw': '1f58d', 'drawing': '1f58d', 'sketch': '1f58d', 'illustrate': '1f58d', 'doodle': '1f58d',
            'paint': '1f3a8', 'painting': '1f3a8', 'brush': '1f3a8', 'canvas': '1f3a8', 'art': '1f3a8',
            'cook': '1f373', 'cooking': '1f373', 'bake': '1f373', 'prepare': '1f373', 'chef': '1f373',
            'eat': '1f37d', 'eating': '1f37d', 'consume': '1f37d', 'devour': '1f37d', 'feast': '1f37d',
            'drink': '1f943', 'drinking': '1f943', 'sip': '1f943', 'gulp': '1f943', 'beverage': '1f943',
            'sleep': '1f634', 'sleeping': '1f634', 'rest': '1f634', 'nap': '1f634', 'slumber': '1f634',
            'wake': '23f0', 'awaken': '23f0', 'arise': '23f0', 'rouse': '23f0', 'stir': '23f0',
            'think': '1f914', 'thinking': '1f914', 'ponder': '1f914', 'contemplate': '1f914', 'reflect': '1f914',
            'listen': '1f442', 'listening': '1f442', 'hear': '1f442', 'attend': '1f442', 'heed': '1f442',
            'speak': '1f5e3', 'speaking': '1f5e3', 'talk': '1f5e3', 'converse': '1f5e3', 'communicate': '1f5e3',
            'laugh': '1f602', 'laughing': '1f602', 'chuckle': '1f602', 'giggle': '1f602', 'guffaw': '1f602',
            'cry': '1f622', 'crying': '1f622', 'weep': '1f622', 'sob': '1f622', 'bawl': '1f622',
            'hug': '1f917', 'hugging': '1f917', 'embrace': '1f917', 'cuddle': '1f917', 'snuggle': '1f917',
            'kiss': '1f48b', 'kissing': '1f48b', 'smooch': '1f48b', 'peck': '1f48b', 'osculate': '1f48b',
            'wave': '1f44b', 'waving': '1f44b', 'greet': '1f44b', 'hello': '1f44b', 'goodbye': '1f44b',
            'point': '1f449', 'pointing': '1f449', 'indicate': '1f449', 'direct': '1f449', 'show': '1f449',
            'clap': '1f44f', 'clapping': '1f44f', 'applaud': '1f44f', 'praise': '1f44f', 'appreciate': '1f44f',
            'shake': '1f91d', 'shaking': '1f91d', 'handshake': '1f91d', 'greet': '1f91d', 'agreement': '1f91d',
            'push': '1f4aa', 'pushing': '1f4aa', 'shove': '1f4aa', 'force': '1f4aa', 'propel': '1f4aa',
            'pull': '1f4aa', 'pulling': '1f4aa', 'drag': '1f4aa', 'tug': '1f4aa', 'yank': '1f4aa',
            'lift': '1f4aa', 'lifting': '1f4aa', 'raise': '1f4aa', 'hoist': '1f4aa', 'elevate': '1f4aa',
            'carry': '1f4aa', 'carrying': '1f4aa', 'transport': '1f4aa', 'bear': '1f4aa', 'convey': '1f4aa',
            'throw': '1f93e', 'throwing': '1f93e', 'toss': '1f93e', 'hurl': '1f93e', 'pitch': '1f93e',
            'catch': '1f945', 'catching': '1f945', 'grab': '1f945', 'snatch': '1f945', 'intercept': '1f945',
            'kick': '26bd', 'kicking': '26bd', 'boot': '26bd', 'punt': '26bd', 'strike': '26bd',
            'hit': '1f44a', 'hitting': '1f44a', 'strike': '1f44a', 'punch': '1f44a', 'smack': '1f44a',
            'build': '1f3d7', 'building': '1f3d7', 'construct': '1f3d7', 'assemble': '1f3d7', 'erect': '1f3d7',
            'fix': '1f527', 'fixing': '1f527', 'repair': '1f527', 'mend': '1f527', 'restore': '1f527',
            'clean': '1f9fd', 'cleaning': '1f9fd', 'wash': '1f9fd', 'scrub': '1f9fd', 'sanitize': '1f9fd',
            'organize': '1f4ca', 'organizing': '1f4ca', 'arrange': '1f4ca', 'sort': '1f4ca', 'tidy': '1f4ca',
            'search': '1f50d', 'searching': '1f50d', 'look': '1f50d', 'seek': '1f50d', 'hunt': '1f50d',
            'find': '1f50d', 'finding': '1f50d', 'discover': '1f50d', 'locate': '1f50d', 'uncover': '1f50d',
            'choose': '1f449', 'choosing': '1f449', 'select': '1f449', 'pick': '1f449', 'decide': '1f449',
            'buy': '1f4b3', 'buying': '1f4b3', 'purchase': '1f4b3', 'shop': '1f4b3', 'acquire': '1f4b3',
            'sell': '1f4b0', 'selling': '1f4b0', 'vend': '1f4b0', 'market': '1f4b0', 'trade': '1f4b0',
            'give': '1f381', 'giving': '1f381', 'donate': '1f381', 'offer': '1f381', 'present': '1f381',
            'take': '1f91a', 'taking': '1f91a', 'grab': '1f91a', 'seize': '1f91a', 'obtain': '1f91a',
            'share': '1f91d', 'sharing': '1f91d', 'distribute': '1f91d', 'divide': '1f91d', 'split': '1f91d',
            'help': '1f91d', 'helping': '1f91d', 'assist': '1f91d', 'aid': '1f91d', 'support': '1f91d',
            'teach': '1f9d1-200d-1f3eb', 'teaching': '1f9d1-200d-1f3eb', 'instruct': '1f9d1-200d-1f3eb',
            'educate': '1f9d1-200d-1f3eb',
            'learn': '1f393', 'learning': '1f393', 'study': '1f393', 'master': '1f393', 'absorb': '1f393',
            'remember': '1f9e0', 'remembering': '1f9e0', 'recall': '1f9e0', 'recollect': '1f9e0', 'retain': '1f9e0',
            'forget': '1f914', 'forgetting': '1f914', 'overlook': '1f914', 'omit': '1f914', 'neglect': '1f914',
            'open': '1f511', 'opening': '1f511', 'unlock': '1f511', 'unfold': '1f511', 'reveal': '1f511',
            'close': '1f512', 'closing': '1f512', 'shut': '1f512', 'lock': '1f512', 'seal': '1f512',
            'start': '25b6', 'starting': '25b6', 'begin': '25b6', 'commence': '25b6', 'initiate': '25b6',
            'stop': '23f9', 'stopping': '23f9', 'halt': '23f9', 'cease': '23f9', 'end': '23f9',
            'continue': '25b6', 'continuing': '25b6', 'proceed': '25b6', 'persist': '25b6', 'resume': '25b6',
            'wait': '23f3', 'waiting': '23f3', 'pause': '23f3', 'delay': '23f3', 'hold': '23f3',
            'hurry': '1f3c3', 'hurrying': '1f3c3', 'rush': '1f3c3', 'speed': '1f3c3', 'hasten': '1f3c3',
            'relax': '1f60c', 'relaxing': '1f60c', 'unwind': '1f60c', 'rest': '1f60c', 'calm': '1f60c',

            # Objects and Tools - COMPREHENSIVE COVERAGE
            # Technology
            'computer': '1f4bb', 'laptop': '1f4bb', 'desktop': '1f4bb', 'pc': '1f4bb', 'machine': '1f4bb',
            'phone': '1f4f1', 'mobile': '1f4f1', 'smartphone': '1f4f1', 'cell': '1f4f1', 'device': '1f4f1',
            'tablet': '1f4f1', 'ipad': '1f4f1', 'screen': '1f4f1', 'touch': '1f4f1', 'portable': '1f4f1',
            'television': '1f4fa', 'tv': '1f4fa', 'monitor': '1f4fa', 'screen': '1f4fa', 'broadcast': '1f4fa',
            'radio': '1f4fb', 'wireless': '1f4fb', 'frequency': '1f4fb', 'antenna': '1f4fb', 'station': '1f4fb',
            'camera': '1f4f7', 'photo': '1f4f7', 'picture': '1f4f7', 'lens': '1f4f7', 'snapshot': '1f4f7',
            'video': '1f4f9', 'camcorder': '1f4f9', 'recording': '1f4f9', 'film': '1f4f9', 'movie': '1f4f9',
            'microphone': '1f3a4', 'mic': '1f3a4', 'audio': '1f3a4', 'sound': '1f3a4', 'record': '1f3a4',
            'speaker': '1f50a', 'audio': '1f50a', 'sound': '1f50a', 'volume': '1f50a', 'loud': '1f50a',
            'headphones': '1f3a7', 'earphones': '1f3a7', 'audio': '1f3a7', 'listen': '1f3a7', 'music': '1f3a7',
            'keyboard': '2328', 'typing': '2328', 'keys': '2328', 'input': '2328', 'qwerty': '2328',
            'mouse': '1f5b1', 'click': '1f5b1', 'pointer': '1f5b1', 'cursor': '1f5b1', 'scroll': '1f5b1',
            'printer': '1f5a8', 'print': '1f5a8', 'paper': '1f5a8', 'ink': '1f5a8', 'copy': '1f5a8',
            'scanner': '1f5a8', 'scan': '1f5a8', 'digital': '1f5a8', 'copy': '1f5a8', 'document': '1f5a8',
            'usb': '1f4be', 'flash drive': '1f4be', 'memory': '1f4be', 'storage': '1f4be', 'data': '1f4be',
            'disk': '1f4bf', 'cd': '1f4bf', 'dvd': '1f4bf', 'storage': '1f4bf', 'optical': '1f4bf',
            'battery': '1f50b', 'power': '1f50b', 'charge': '1f50b', 'energy': '1f50b', 'electric': '1f50b',
            'charger': '1f50c', 'cable': '1f50c', 'plug': '1f50c', 'power': '1f50c', 'electric': '1f50c',
            'wire': '1f50c', 'cable': '1f50c', 'cord': '1f50c', 'connection': '1f50c', 'electric': '1f50c',
            'remote': '1f4fa', 'control': '1f4fa', 'channel': '1f4fa', 'button': '1f4fa', 'device': '1f4fa',
            'gamepad': '1f3ae', 'controller': '1f3ae', 'joystick': '1f3ae', 'gaming': '1f3ae', 'console': '1f3ae',
            'router': '1f4e1', 'wifi': '1f4e1', 'internet': '1f4e1', 'network': '1f4e1', 'wireless': '1f4e1',
            'server': '1f4be', 'database': '1f4be', 'cloud': '2601', 'storage': '1f4be', 'data': '1f4be',

            # Tools and Equipment
            'hammer': '1f528', 'nail': '1f528', 'build': '1f528', 'construction': '1f528', 'tool': '1f528',
            'screwdriver': '1f527', 'screw': '1f527', 'turn': '1f527', 'fix': '1f527', 'repair': '1f527',
            'wrench': '1f527', 'spanner': '1f527', 'bolt': '1f527', 'tighten': '1f527', 'loosen': '1f527',
            'saw': '1f6e0', 'cut': '1f6e0', 'wood': '1f6e0', 'blade': '1f6e0', 'sharp': '1f6e0',
            'drill': '1f6e0', 'hole': '1f6e0', 'bore': '1f6e0', 'power': '1f6e0', 'electric': '1f6e0',
            'pliers': '1f6e0', 'grip': '1f6e0', 'squeeze': '1f6e0', 'hold': '1f6e0', 'wire': '1f6e0',
            'scissors': '2702', 'cut': '2702', 'snip': '2702', 'trim': '2702', 'sharp': '2702',
            'knife': '1f52a', 'blade': '1f52a', 'cut': '1f52a', 'sharp': '1f52a', 'kitchen': '1f52a',
            'fork': '1f374', 'eating': '1f374', 'utensil': '1f374', 'prong': '1f374', 'food': '1f374',
            'spoon': '1f944', 'eating': '1f944', 'utensil': '1f944', 'scoop': '1f944', 'soup': '1f944',
            'chopsticks': '1f962', 'asian': '1f962', 'eating': '1f962', 'utensil': '1f962', 'wood': '1f962',
            'plate': '1f37d', 'dish': '1f37d', 'food': '1f37d', 'serve': '1f37d', 'round': '1f37d',
            'bowl': '1f963', 'dish': '1f963', 'food': '1f963', 'cereal': '1f963', 'soup': '1f963',
            'cup': '2615', 'mug': '2615', 'drink': '2615', 'beverage': '2615', 'handle': '2615',
            'glass': '1f943', 'drink': '1f943', 'water': '1f943', 'transparent': '1f943', 'fragile': '1f943',
            'bottle': '1f37c', 'container': '1f37c', 'liquid': '1f37c', 'water': '1f37c', 'baby': '1f37c',
            'can': '1f96b', 'container': '1f96b', 'metal': '1f96b', 'food': '1f96b', 'preserve': '1f96b',
            'jar': '1fad9', 'container': '1fad9', 'glass': '1fad9', 'preserve': '1fad9', 'lid': '1fad9',
            'box': '1f4e6', 'container': '1f4e6', 'package': '1f4e6', 'cardboard': '1f4e6', 'shipping': '1f4e6',
            'bag': '1f45c', 'purse': '1f45c', 'carry': '1f45c', 'handbag': '1f45c', 'container': '1f45c',
            'backpack': '1f392', 'rucksack': '1f392', 'school': '1f392', 'hiking': '1f392', 'carry': '1f392',
            'suitcase': '1f9f3', 'luggage': '1f9f3', 'travel': '1f9f3', 'vacation': '1f9f3', 'pack': '1f9f3',
            'basket': '1f9fa', 'container': '1f9fa', 'wicker': '1f9fa', 'carry': '1f9fa', 'shopping': '1f9fa',
            'bucket': '1faa3', 'pail': '1faa3', 'container': '1faa3', 'water': '1faa3', 'carry': '1faa3',

            # Household Items
            'lamp': '1f4a1', 'light': '1f4a1', 'bulb': '1f4a1', 'illuminate': '1f4a1', 'bright': '1f4a1',
            'candle': '1f56f', 'flame': '1f56f', 'wax': '1f56f', 'light': '1f56f', 'romantic': '1f56f',
            'clock': '1f550', 'time': '1f550', 'hour': '1f550', 'minute': '1f550', 'tick': '1f550',
            'watch': '231a', 'timepiece': '231a', 'wrist': '231a', 'time': '231a', 'tick': '231a',
            'mirror': '1fa9e', 'reflection': '1fa9e', 'glass': '1fa9e', 'look': '1fa9e', 'vanity': '1fa9e',
            'picture': '1f5bc', 'frame': '1f5bc', 'art': '1f5bc', 'photo': '1f5bc', 'wall': '1f5bc',
            'window': '1fa9f', 'glass': '1fa9f', 'view': '1fa9f', 'light': '1fa9f', 'open': '1fa9f',
            'door': '1f6aa', 'entrance': '1f6aa', 'exit': '1f6aa', 'portal': '1f6aa', 'wood': '1f6aa',
            'key': '1f511', 'lock': '1f511', 'unlock': '1f511', 'metal': '1f511', 'access': '1f511',
            'lock': '1f512', 'secure': '1f512', 'key': '1f512', 'safety': '1f512', 'closed': '1f512',
            'bed': '1f6cf', 'sleep': '1f6cf', 'mattress': '1f6cf', 'pillow': '1f6cf', 'rest': '1f6cf',
            'pillow': '1f6cf', 'cushion': '1f6cf', 'soft': '1f6cf', 'head': '1f6cf', 'comfort': '1f6cf',
            'blanket': '1f6cf', 'cover': '1f6cf', 'warm': '1f6cf', 'bed': '1f6cf', 'cozy': '1f6cf',
            'chair': '1fa91', 'seat': '1fa91', 'furniture': '1fa91', 'sit': '1fa91', 'rest': '1fa91',
            'table': '1f37d', 'furniture': '1f37d', 'surface': '1f37d', 'desk': '1f37d', 'eat': '1f37d',
            'sofa': '1f6cb', 'couch': '1f6cb', 'furniture': '1f6cb', 'sit': '1f6cb', 'living room': '1f6cb',
            'shelf': '1f4da', 'bookshelf': '1f4da', 'storage': '1f4da', 'books': '1f4da', 'display': '1f4da',
            'closet': '1f45a', 'wardrobe': '1f45a', 'clothes': '1f45a', 'storage': '1f45a', 'hanging': '1f45a',
            'refrigerator': '1f9ca', 'fridge': '1f9ca', 'cold': '1f9ca', 'food': '1f9ca', 'appliance': '1f9ca',
            'stove': '1f373', 'oven': '1f373', 'cook': '1f373', 'hot': '1f373', 'kitchen': '1f373',
            'microwave': '1f373', 'heat': '1f373', 'quick': '1f373', 'radiation': '1f373', 'kitchen': '1f373',
            'dishwasher': '1f9fd', 'clean': '1f9fd', 'dishes': '1f9fd', 'water': '1f9fd', 'kitchen': '1f9fd',
            'washing machine': '1f9fd', 'laundry': '1f9fd', 'clothes': '1f9fd', 'clean': '1f9fd', 'water': '1f9fd',
            'dryer': '1f9fd', 'dry': '1f9fd', 'clothes': '1f9fd', 'hot': '1f9fd', 'laundry': '1f9fd',
            'vacuum': '1f9fd', 'clean': '1f9fd', 'carpet': '1f9fd', 'suction': '1f9fd', 'dust': '1f9fd',
            'broom': '1f9f9', 'sweep': '1f9f9', 'clean': '1f9f9', 'floor': '1f9f9', 'dust': '1f9f9',
            'mop': '1f9fd', 'clean': '1f9fd', 'floor': '1f9fd', 'water': '1f9fd', 'wet': '1f9fd',
            'bucket': '1faa3', 'pail': '1faa3', 'water': '1faa3', 'clean': '1faa3', 'carry': '1faa3',
            'trash': '1f5d1', 'garbage': '1f5d1', 'waste': '1f5d1', 'rubbish': '1f5d1', 'bin': '1f5d1',
            'toilet': '1f6bd', 'bathroom': '1f6bd', 'restroom': '1f6bd', 'lavatory': '1f6bd', 'flush': '1f6bd',
            'sink': '1f6b0', 'wash': '1f6b0', 'water': '1f6b0', 'basin': '1f6b0', 'faucet': '1f6b0',
            'bathtub': '1f6c1', 'bath': '1f6c1', 'soak': '1f6c1', 'water': '1f6c1', 'relax': '1f6c1',
            'shower': '1f6bf', 'wash': '1f6bf', 'water': '1f6bf', 'clean': '1f6bf', 'spray': '1f6bf',
            'towel': '1f9fd', 'dry': '1f9fd', 'clean': '1f9fd', 'bath': '1f9fd', 'absorb': '1f9fd',
            'soap': '1f9fc', 'clean': '1f9fc', 'wash': '1f9fc', 'bubble': '1f9fc', 'hygiene': '1f9fc',
            'shampoo': '1f9fc', 'hair': '1f9fc', 'wash': '1f9fc', 'clean': '1f9fc', 'bottle': '1f9fc',
            'toothbrush': '1faa5', 'teeth': '1faa5', 'clean': '1faa5', 'brush': '1faa5', 'hygiene': '1faa5',
            'toothpaste': '1faa5', 'teeth': '1faa5', 'clean': '1faa5', 'mint': '1faa5', 'tube': '1faa5',

            # Clothing and Accessories
            'shirt': '1f455', 'top': '1f455', 'clothing': '1f455', 'wear': '1f455', 'fabric': '1f455',
            'pants': '1f456', 'trousers': '1f456', 'clothing': '1f456', 'legs': '1f456', 'wear': '1f456',
            'dress': '1f457', 'gown': '1f457', 'clothing': '1f457', 'formal': '1f457', 'elegant': '1f457',
            'skirt': '1f457', 'clothing': '1f457', 'feminine': '1f457', 'wear': '1f457', 'short': '1f457',
            'jacket': '1f9e5', 'coat': '1f9e5', 'outer': '1f9e5', 'warm': '1f9e5', 'clothing': '1f9e5',
            'sweater': '1f9e6', 'jumper': '1f9e6', 'warm': '1f9e6', 'wool': '1f9e6', 'clothing': '1f9e6',
            'hat': '1f452', 'cap': '1f452', 'head': '1f452', 'wear': '1f452', 'protection': '1f452',
            'shoes': '1f45e', 'footwear': '1f45e', 'feet': '1f45e', 'walk': '1f45e', 'leather': '1f45e',
            'boots': '1f97e', 'footwear': '1f97e', 'tall': '1f97e', 'protection': '1f97e', 'sturdy': '1f97e',
            'sandals': '1f461', 'footwear': '1f461', 'open': '1f461', 'summer': '1f461', 'casual': '1f461',
            'socks': '1f9e6', 'feet': '1f9e6', 'warm': '1f9e6', 'cotton': '1f9e6', 'pair': '1f9e6',
            'gloves': '1f9e4', 'hands': '1f9e4', 'warm': '1f9e4', 'protection': '1f9e4', 'pair': '1f9e4',
            'scarf': '1f9e3', 'neck': '1f9e3', 'warm': '1f9e3', 'wrap': '1f9e3', 'winter': '1f9e3',
            'tie': '1f454', 'necktie': '1f454', 'formal': '1f454', 'business': '1f454', 'knot': '1f454',
            'belt': '1f94e', 'waist': '1f94e', 'leather': '1f94e', 'buckle': '1f94e', 'accessory': '1f94e',
            'watch': '231a', 'timepiece': '231a', 'wrist': '231a', 'accessory': '231a', 'jewelry': '231a',
            'ring': '1f48d', 'jewelry': '1f48d', 'finger': '1f48d', 'wedding': '1f48d', 'precious': '1f48d',
            'necklace': '1f4ff', 'jewelry': '1f4ff', 'neck': '1f4ff', 'chain': '1f4ff', 'pendant': '1f4ff',
            'earrings': '1f48e', 'jewelry': '1f48e', 'ears': '1f48e', 'piercing': '1f48e', 'pair': '1f48e',
            'bracelet': '1f4ff', 'jewelry': '1f4ff', 'wrist': '1f4ff', 'chain': '1f4ff', 'accessory': '1f4ff',
            'glasses': '1f453', 'eyeglasses': '1f453', 'vision': '1f453', 'lens': '1f453', 'see': '1f453',
            'sunglasses': '1f576', 'shades': '1f576', 'eyes': '1f576', 'protection': '1f576', 'cool': '1f576',
            'umbrella': '2602', 'rain': '2602', 'protection': '2602', 'weather': '2602', 'shade': '2602',

            # Vehicles and Transportation
            'car': '1f697', 'automobile': '1f697', 'vehicle': '1f697', 'drive': '1f697', 'road': '1f697',
            'truck': '1f69a', 'lorry': '1f69a', 'vehicle': '1f69a', 'cargo': '1f69a', 'transport': '1f69a',
            'bus': '1f68c', 'vehicle': '1f68c', 'public': '1f68c', 'transport': '1f68c', 'passengers': '1f68c',
            'train': '1f686', 'locomotive': '1f686', 'railway': '1f686', 'transport': '1f686', 'tracks': '1f686',
            'subway': '1f687', 'metro': '1f687', 'underground': '1f687', 'train': '1f687', 'urban': '1f687',
            'plane': '2708', 'airplane': '2708', 'aircraft': '2708', 'fly': '2708', 'aviation': '2708',
            'helicopter': '1f681', 'chopper': '1f681', 'rotor': '1f681', 'fly': '1f681', 'rescue': '1f681',
            'boat': '1f6a2', 'ship': '1f6a2', 'vessel': '1f6a2', 'water': '1f6a2', 'sail': '1f6a2',
            'bicycle': '1f6b2', 'bike': '1f6b2', 'cycle': '1f6b2', 'pedal': '1f6b2', 'two wheels': '1f6b2',
            'motorcycle': '1f3cd', 'motorbike': '1f3cd', 'bike': '1f3cd', 'engine': '1f3cd', 'fast': '1f3cd',
            'scooter': '1f6f4', 'kick scooter': '1f6f4', 'ride': '1f6f4', 'push': '1f6f4', 'wheels': '1f6f4',
            'skateboard': '1f6f9', 'board': '1f6f9', 'wheels': '1f6f9', 'trick': '1f6f9', 'youth': '1f6f9',
            'rocket': '1f680', 'spaceship': '1f680', 'space': '1f680', 'launch': '1f680', 'fast': '1f680',
            'ambulance': '1f691', 'emergency': '1f691', 'medical': '1f691', 'hospital': '1f691', 'siren': '1f691',
            'fire truck': '1f692', 'fire engine': '1f692', 'emergency': '1f692', 'red': '1f692', 'rescue': '1f692',
            'police car': '1f693', 'patrol': '1f693', 'law': '1f693', 'enforcement': '1f693', 'siren': '1f693',
            'taxi': '1f695', 'cab': '1f695', 'yellow': '1f695', 'transport': '1f695', 'hire': '1f695',
            'tractor': '1f69c', 'farm': '1f69c', 'agriculture': '1f69c', 'field': '1f69c', 'rural': '1f69c',

            # Sports and Games
            'ball': '26bd', 'sphere': '26bd', 'round': '26bd', 'play': '26bd', 'sport': '26bd',
            'football': '1f3c8', 'american': '1f3c8', 'sport': '1f3c8', 'tackle': '1f3c8', 'oval': '1f3c8',
            'soccer': '26bd', 'football': '26bd', 'sport': '26bd', 'kick': '26bd', 'goal': '26bd',
            'basketball': '1f3c0', 'sport': '1f3c0', 'hoop': '1f3c0', 'dribble': '1f3c0', 'orange': '1f3c0',
            'baseball': '26be', 'sport': '26be', 'bat': '26be', 'diamond': '26be', 'pitcher': '26be',
            'tennis': '1f3be', 'sport': '1f3be', 'racket': '1f3be', 'court': '1f3be', 'serve': '1f3be',
            'golf': '26f3', 'sport': '26f3', 'club': '26f3', 'course': '26f3', 'hole': '26f3',
            'hockey': '1f3d2', 'ice': '1f3d2', 'sport': '1f3d2', 'stick': '1f3d2', 'puck': '1f3d2',
            'volleyball': '1f3d0', 'sport': '1f3d0', 'net': '1f3d0', 'spike': '1f3d0', 'team': '1f3d0',
            'ping pong': '1f3d3', 'table tennis': '1f3d3', 'paddle': '1f3d3', 'sport': '1f3d3', 'ball': '1f3d3',
            'badminton': '1f3f8', 'sport': '1f3f8', 'racket': '1f3f8', 'shuttlecock': '1f3f8', 'net': '1f3f8',
            'bowling': '1f3b3', 'sport': '1f3b3', 'pins': '1f3b3', 'strike': '1f3b3', 'ball': '1f3b3',
            'chess': '1f3c1', 'board game': '1f3c1', 'strategy': '1f3c1', 'pieces': '1f3c1', 'checkmate': '1f3c1',
            'cards': '1f0cf', 'playing cards': '1f0cf', 'deck': '1f0cf', 'game': '1f0cf', 'suit': '1f0cf',
            'dice': '1f3b2', 'cube': '1f3b2', 'random': '1f3b2', 'game': '1f3b2', 'chance': '1f3b2',
            'puzzle': '1f9e9', 'jigsaw': '1f9e9', 'pieces': '1f9e9', 'solve': '1f9e9', 'challenge': '1f9e9',
            'video game': '1f3ae', 'console': '1f3ae', 'controller': '1f3ae', 'digital': '1f3ae', 'play': '1f3ae',

            # IMPROVED COLORS - Basic colors mapped to colored circle emojis
            'red': '1f534',  # 🔴 Red circle
            'blue': '1f535',  # 🔵 Blue circle
            'green': '1f7e2',  # 🟢 Green circle
            'yellow': '1f7e1',  # 🟡 Yellow circle
            'orange': '1f7e0',  # 🟠 Orange circle
            'purple': '1f7e3',  # 🟣 Purple circle
            'pink': '1f338',  # 🌸 Cherry blossom (best pink alternative)
            'black': '26ab',  # ⚫ Black circle
            'white': '26aa',  # ⚪ White circle
            'brown': '1f7e4',  # 🟤 Brown circle
            'gray': '26aa',  # ⚪ Using white circle for gray
            'grey': '26aa',  # ⚪ Using white circle for grey
            'crimson': '1f534',  # 🔴 Red circle for crimson
            'scarlet': '1f534',  # 🔴 Red circle for scarlet
            'maroon': '1f534',  # 🔴 Red circle for maroon
            'navy': '1f535',  # 🔵 Blue circle for navy
            'royal blue': '1f535',  # 🔵 Blue circle for royal blue
            'sky blue': '1f535',  # 🔵 Blue circle for sky blue
            'lime': '1f7e2',  # 🟢 Green circle for lime
            'forest green': '1f7e2',  # 🟢 Green circle for forest green
            'olive': '1f7e2',  # 🟢 Green circle for olive
            'gold': '1f7e1',  # 🟡 Yellow circle for gold
            'cream': '1f7e1',  # 🟡 Yellow circle for cream
            'beige': '1f7e1',  # 🟡 Yellow circle for beige
            'violet': '1f7e3',  # 🟣 Purple circle for violet
            'magenta': '1f7e3',  # 🟣 Purple circle for magenta
            'indigo': '1f7e3',  # 🟣 Purple circle for indigo
            'coral': '1f7e0',  # 🟠 Orange circle for coral
            'peach': '1f7e0',  # 🟠 Orange circle for peach
            'salmon': '1f7e0',  # 🟠 Orange circle for salmon
            'rose': '1f338',  # 🌸 Cherry blossom for rose
            'fuchsia': '1f338',  # 🌸 Cherry blossom for fuchsia
            'hot pink': '1f338',  # 🌸 Cherry blossom for hot pink
            'silver': '26aa',  # ⚪ White circle for silver
            'platinum': '26aa',  # ⚪ White circle for platinum
            'charcoal': '26ab',  # ⚫ Black circle for charcoal
            'ebony': '26ab',  # ⚫ Black circle for ebony
            'tan': '1f7e4',  # 🟤 Brown circle for tan
            'bronze': '1f7e4',  # 🟤 Brown circle for bronze
            'copper': '1f7e4',  # 🟤 Brown circle for copper

            # Color variations and adjectives
            'red color': '1f534', 'blue color': '1f535', 'green color': '1f7e2',
            'yellow color': '1f7e1', 'orange color': '1f7e0', 'purple color': '1f7e3',
            'pink color': '1f338', 'black color': '26ab', 'white color': '26aa',
            'brown color': '1f7e4', 'gray color': '26aa', 'grey color': '26aa',
            'colorful': '1f308',  # 🌈 Rainbow for colorful
            'rainbow': '1f308',  # 🌈 Rainbow
            'multicolor': '1f308',  # 🌈 Rainbow for multicolor
            'bright': '1f31f',  # ⭐ Star for bright
            'dark': '26ab',  # ⚫ Black circle for dark
            'light': '26aa',  # ⚪ White circle for light
            'pale': '26aa',  # ⚪ White circle for pale
            'vivid': '1f31f',  # ⭐ Star for vivid
            'brilliant': '1f31f',  # ⭐ Star for brilliant

            # Nature and environment - MASSIVE EXPANSION
            'sun': '2600', 'sunny': '2600', 'sunshine': '2600', 'solar': '2600', 'bright': '2600',
            'moon': '1f319', 'lunar': '1f319', 'night': '1f319', 'crescent': '1f319', 'satellite': '1f319',
            'star': '2b50', 'stellar': '2b50', 'bright': '2b50', 'twinkle': '2b50', 'shine': '2b50',
            'cloud': '2601', 'cloudy': '2601', 'sky': '2601', 'weather': '2601', 'overcast': '2601',
            'rain': '1f327', 'rainy': '1f327', 'wet': '1f327', 'precipitation': '1f327', 'storm': '1f327',
            'snow': '2744', 'snowy': '2744', 'cold': '2744', 'winter': '2744', 'flake': '2744',
            'wind': '1f32c', 'windy': '1f32c', 'breeze': '1f32c', 'gust': '1f32c', 'air': '1f32c',
            'lightning': '26a1', 'thunder': '26a1', 'electric': '26a1', 'bolt': '26a1', 'storm': '26a1',
            'fire': '1f525', 'flame': '1f525', 'burn': '1f525', 'hot': '1f525', 'heat': '1f525',
            'water': '1f4a7', 'liquid': '1f4a7', 'drop': '1f4a7', 'wet': '1f4a7', 'ocean': '1f30a',
            'earth': '1f30d', 'world': '1f30d', 'planet': '1f30d', 'globe': '1f30d', 'soil': '1f30d',
            'mountain': '26f0', 'hill': '26f0', 'peak': '26f0', 'summit': '26f0', 'slope': '26f0',
            'valley': '1f3dc', 'canyon': '1f3dc', 'gorge': '1f3dc', 'ravine': '1f3dc', 'depression': '1f3dc',
            'river': '1f30a', 'stream': '1f30a', 'creek': '1f30a', 'brook': '1f30a', 'flow': '1f30a',
            'lake': '1f30a', 'pond': '1f30a', 'reservoir': '1f30a', 'lagoon': '1f30a', 'basin': '1f30a',
            'ocean': '1f30a', 'sea': '1f30a', 'marine': '1f30a', 'saltwater': '1f30a', 'waves': '1f30a',
            'beach': '1f3d6', 'shore': '1f3d6', 'coast': '1f3d6', 'sand': '1f3d6', 'seaside': '1f3d6',
            'desert': '1f3dc', 'arid': '1f3dc', 'sand': '1f3dc', 'dune': '1f3dc', 'dry': '1f3dc',
            'forest': '1f333', 'woods': '1f333', 'trees': '1f333', 'woodland': '1f333', 'timber': '1f333',
            'jungle': '1f333', 'rainforest': '1f333', 'tropical': '1f333', 'dense': '1f333', 'wild': '1f333',
            'tree': '1f333', 'plant': '1f331', 'oak': '1f333', 'pine': '1f333', 'maple': '1f333',
            'flower': '1f33c', 'bloom': '1f33c', 'blossom': '1f33c', 'petal': '1f33c', 'rose': '1f339',
            'grass': '1f33f', 'lawn': '1f33f', 'meadow': '1f33f', 'field': '1f33f', 'green': '1f33f',
            'leaf': '1f343', 'leaves': '1f343', 'foliage': '1f343', 'autumn': '1f343', 'fall': '1f343',
            'seed': '1f331', 'sprout': '1f331', 'seedling': '1f331', 'growth': '1f331', 'plant': '1f331',
            'branch': '1f333', 'twig': '1f333', 'limb': '1f333', 'bough': '1f333', 'wood': '1f333',
            'root': '1f331', 'underground': '1f331', 'foundation': '1f331', 'anchor': '1f331', 'base': '1f331',
            'fruit': '1f34e', 'berry': '1f353', 'nut': '1f95c', 'vegetable': '1f955', 'produce': '1f34e',
            'rock': '1faa8', 'stone': '1faa8', 'boulder': '1faa8', 'pebble': '1faa8', 'mineral': '1faa8',
            'sand': '1f3d6', 'grain': '1f3d6', 'beach': '1f3d6', 'desert': '1f3d6', 'fine': '1f3d6',
            'mud': '1f3d6', 'dirt': '1f3d6', 'soil': '1f3d6', 'clay': '1f3d6', 'earth': '1f3d6',
            'ice': '1f9ca', 'frozen': '1f9ca', 'cold': '1f9ca', 'crystal': '1f9ca', 'glacier': '1f9ca',
            'volcano': '1f30b', 'eruption': '1f30b', 'lava': '1f30b', 'magma': '1f30b', 'hot': '1f30b',
            'earthquake': '1f4c8', 'tremor': '1f4c8', 'shake': '1f4c8', 'seismic': '1f4c8', 'fault': '1f4c8',
            'cave': '1f573', 'cavern': '1f573', 'underground': '1f573', 'hollow': '1f573', 'dark': '1f573',
            'island': '1f3dd', 'isle': '1f3dd', 'atoll': '1f3dd', 'landmass': '1f3dd', 'isolated': '1f3dd',
            'continent': '1f30d', 'landmass': '1f30d', 'large': '1f30d', 'geography': '1f30d', 'massive': '1f30d',

            # Weather phenomena
            'fog': '1f32b', 'mist': '1f32b', 'haze': '1f32b', 'cloudy': '1f32b', 'visibility': '1f32b',
            'frost': '2744', 'freeze': '2744', 'ice crystals': '2744', 'morning': '2744', 'cold': '2744',
            'dew': '1f4a7', 'moisture': '1f4a7', 'droplets': '1f4a7', 'condensation': '1f4a7', 'wet': '1f4a7',
            'hail': '2744', 'ice balls': '2744', 'storm': '2744', 'pellets': '2744', 'damage': '2744',
            'tornado': '1f32a', 'twister': '1f32a', 'cyclone': '1f32a', 'whirlwind': '1f32a', 'spiral': '1f32a',
            'hurricane': '1f32a', 'typhoon': '1f32a', 'storm': '1f32a', 'tropical': '1f32a', 'powerful': '1f32a',
            'rainbow': '1f308', 'arc': '1f308', 'colors': '1f308', 'spectrum': '1f308', 'beautiful': '1f308',
            'aurora': '1f308', 'northern lights': '1f308', 'phenomenon': '1f308', 'polar': '1f308', 'magnetic': '1f308',

            # Time concepts - COMPREHENSIVE COVERAGE
            'time': '1f550', 'clock': '1f550', 'hour': '1f550', 'minute': '1f550', 'second': '1f550',
            'morning': '1f305', 'dawn': '1f305', 'sunrise': '1f305', 'early': '1f305', 'am': '1f305',
            'afternoon': '1f307', 'midday': '1f307', 'noon': '1f307', 'pm': '1f307', 'daytime': '1f307',
            'evening': '1f306', 'dusk': '1f306', 'sunset': '1f306', 'twilight': '1f306', 'late': '1f306',
            'night': '1f319', 'midnight': '1f319', 'dark': '1f319', 'sleep': '1f319', 'moon': '1f319',
            'today': '1f4c5', 'now': '1f4c5', 'present': '1f4c5', 'current': '1f4c5', 'this day': '1f4c5',
            'yesterday': '1f4c5', 'past': '1f4c5', 'before': '1f4c5', 'previous': '1f4c5', 'ago': '1f4c5',
            'tomorrow': '1f4c5', 'future': '1f4c5', 'next': '1f4c5', 'coming': '1f4c5', 'will': '1f4c5',
            'week': '1f4c6', 'weekly': '1f4c6', 'seven days': '1f4c6', 'period': '1f4c6', 'calendar': '1f4c6',
            'month': '1f4c6', 'monthly': '1f4c6', 'thirty days': '1f4c6', 'lunar': '1f4c6', 'calendar': '1f4c6',
            'year': '1f4c5', 'annual': '1f4c5', 'yearly': '1f4c5', 'twelve months': '1f4c5', 'calendar': '1f4c5',
            'decade': '1f4c5', 'ten years': '1f4c5', 'period': '1f4c5', 'era': '1f4c5', 'generation': '1f4c5',
            'century': '1f4c5', 'hundred years': '1f4c5', 'era': '1f4c5', 'historical': '1f4c5', 'long': '1f4c5',
            'season': '1f333', 'spring': '1f33c', 'summer': '2600', 'autumn': '1f343', 'winter': '2744',
            'holiday': '1f389', 'vacation': '1f389', 'celebration': '1f389', 'festival': '1f389', 'special': '1f389',
            'birthday': '1f382', 'anniversary': '1f382', 'celebration': '1f382', 'party': '1f382', 'cake': '1f382',
            'schedule': '1f4c5', 'agenda': '1f4c5', 'plan': '1f4c5', 'timetable': '1f4c5', 'calendar': '1f4c5',
            'deadline': '23f0', 'due': '23f0', 'limit': '23f0', 'urgent': '23f0', 'time limit': '23f0',
            'appointment': '1f4c5', 'meeting': '1f4c5', 'scheduled': '1f4c5', 'planned': '1f4c5', 'arranged': '1f4c5',
            'delay': '23f3', 'postpone': '23f3', 'wait': '23f3', 'later': '23f3', 'defer': '23f3',
            'early': '1f305', 'ahead': '1f305', 'prompt': '1f305', 'beforehand': '1f305', 'advance': '1f305',
            'late': '1f319', 'behind': '1f319', 'delayed': '1f319', 'overdue': '1f319', 'tardy': '1f319',
            'punctual': '1f550', 'on time': '1f550', 'precise': '1f550', 'accurate': '1f550', 'timely': '1f550',
            'fast': '26a1', 'quick': '26a1', 'rapid': '26a1', 'swift': '26a1', 'speedy': '26a1',
            'slow': '1f40c', 'gradual': '1f40c', 'leisurely': '1f40c', 'unhurried': '1f40c', 'deliberate': '1f40c',
            'instant': '26a1', 'immediate': '26a1', 'moment': '26a1', 'second': '26a1', 'flash': '26a1',
            'eternal': '267e', 'forever': '267e', 'endless': '267e', 'infinite': '267e', 'timeless': '267e',
            'temporary': '23f3', 'brief': '23f3', 'short': '23f3', 'momentary': '23f3', 'fleeting': '23f3',
            'permanent': '267e', 'lasting': '267e', 'enduring': '267e', 'stable': '267e', 'fixed': '267e',

            # Size and quantity - COMPREHENSIVE
            'big': '1f418', 'large': '1f418', 'huge': '1f418', 'enormous': '1f418', 'giant': '1f418',
            'small': '1f400', 'tiny': '1f400', 'little': '1f400', 'minute': '1f400', 'petite': '1f400',
            'medium': '1f4cf', 'average': '1f4cf', 'moderate': '1f4cf', 'middle': '1f4cf', 'standard': '1f4cf',
            'tall': '1f5fc', 'high': '1f5fc', 'lofty': '1f5fc', 'elevated': '1f5fc', 'towering': '1f5fc',
            'short': '1f464', 'low': '1f464', 'brief': '1f464', 'compact': '1f464', 'diminutive': '1f464',
            'wide': '1f4cf', 'broad': '1f4cf', 'expansive': '1f4cf', 'extensive': '1f4cf', 'spacious': '1f4cf',
            'narrow': '1f4cf', 'thin': '1f4cf', 'slim': '1f4cf', 'slender': '1f4cf', 'tight': '1f4cf',
            'thick': '1f4cf', 'dense': '1f4cf', 'heavy': '1f4cf', 'substantial': '1f4cf', 'solid': '1f4cf',
            'deep': '1f573', 'profound': '1f573', 'bottomless': '1f573', 'extensive': '1f573', 'far': '1f573',
            'shallow': '1f4a7', 'surface': '1f4a7', 'superficial': '1f4a7', 'light': '1f4a7', 'minimal': '1f4a7',
            'long': '1f4cf', 'lengthy': '1f4cf', 'extended': '1f4cf', 'stretched': '1f4cf', 'prolonged': '1f4cf',
            'many': '1f522', 'multiple': '1f522', 'several': '1f522', 'numerous': '1f522', 'countless': '1f522',
            'few': '1f522', 'some': '1f522', 'handful': '1f522', 'limited': '1f522', 'scarce': '1f522',
            'all': '1f310', 'everything': '1f310', 'complete': '1f310', 'total': '1f310', 'entire': '1f310',
            'none': '1f6ab', 'nothing': '1f6ab', 'zero': '1f6ab', 'empty': '1f6ab', 'void': '1f6ab',
            'full': '1f4c8', 'complete': '1f4c8', 'maximum': '1f4c8', 'total': '1f4c8', 'packed': '1f4c8',
            'empty': '1f4c9', 'vacant': '1f4c9', 'hollow': '1f4c9', 'bare': '1f4c9', 'void': '1f4c9',
            'half': '1f4ca', 'partial': '1f4ca', 'incomplete': '1f4ca', 'middle': '1f4ca', 'divided': '1f4ca',
            'double': '2195', 'twice': '2195', 'dual': '2195', 'pair': '2195', 'twin': '2195',
            'triple': '2195', 'three times': '2195', 'threefold': '2195', 'trio': '2195', 'triad': '2195',
            'single': '1f195', 'one': '1f195', 'individual': '1f195', 'sole': '1f195', 'unique': '1f195',
            'plural': '1f522', 'multiple': '1f522', 'more than one': '1f522', 'various': '1f522', 'several': '1f522',

            # Mathematical and numerical concepts
            'number': '1f522', 'digit': '1f522', 'numeral': '1f522', 'figure': '1f522', 'count': '1f522',
            'zero': '0️⃣', 'nothing': '0️⃣', 'null': '0️⃣', 'empty': '0️⃣', 'void': '0️⃣',
            'one': '1️⃣', 'single': '1️⃣', 'first': '1️⃣', 'unity': '1️⃣', 'individual': '1️⃣',
            'two': '2️⃣', 'pair': '2️⃣', 'couple': '2️⃣', 'double': '2️⃣', 'second': '2️⃣',
            'three': '3️⃣', 'trio': '3️⃣', 'triple': '3️⃣', 'third': '3️⃣', 'triad': '3️⃣',
            'four': '4️⃣', 'quartet': '4️⃣', 'quadruple': '4️⃣', 'fourth': '4️⃣', 'square': '4️⃣',
            'five': '5️⃣', 'quintet': '5️⃣', 'fifth': '5️⃣', 'pentagon': '5️⃣', 'hand': '5️⃣',
            'six': '6️⃣', 'sextet': '6️⃣', 'sixth': '6️⃣', 'hexagon': '6️⃣', 'half dozen': '6️⃣',
            'seven': '7️⃣', 'septet': '7️⃣', 'seventh': '7️⃣', 'lucky': '7️⃣', 'week': '7️⃣',
            'eight': '8️⃣', 'octet': '8️⃣', 'eighth': '8️⃣', 'octagon': '8️⃣', 'infinity': '8️⃣',
            'nine': '9️⃣', 'ninth': '9️⃣', 'nonagon': '9️⃣', 'almost ten': '9️⃣', 'enneagon': '9️⃣',
            'ten': '🔟', 'decade': '🔟', 'tenth': '🔟', 'decimal': '🔟', 'fingers': '🔟',
            'hundred': '1f4af', 'century': '1f4af', 'hundredth': '1f4af', 'percent': '1f4af', 'complete': '1f4af',
            'thousand': '1f4af', 'millennium': '1f4af', 'thousandth': '1f4af', 'kilo': '1f4af', 'grand': '1f4af',
            'million': '1f4b0', 'millionth': '1f4b0', 'mega': '1f4b0', 'huge number': '1f4b0', 'wealthy': '1f4b0',
            'billion': '1f4b0', 'billionth': '1f4b0', 'giga': '1f4b0', 'enormous': '1f4b0', 'rich': '1f4b0',
            'add': '2795', 'plus': '2795', 'addition': '2795', 'sum': '2795', 'total': '2795',
            'subtract': '2796', 'minus': '2796', 'subtraction': '2796', 'difference': '2796', 'less': '2796',
            'multiply': '2716', 'times': '2716', 'multiplication': '2716', 'product': '2716', 'by': '2716',
            'divide': '2797', 'division': '2797', 'quotient': '2797', 'split': '2797', 'share': '2797',
            'equals': '1f4ca', 'equal': '1f4ca', 'same': '1f4ca', 'equivalent': '1f4ca', 'result': '1f4ca',
            'percent': '1f4ca', 'percentage': '1f4ca', 'proportion': '1f4ca', 'ratio': '1f4ca', 'fraction': '1f4ca',
            'decimal': '1f522', 'point': '1f522', 'fractional': '1f522', 'precise': '1f522', 'exact': '1f522',
            'whole': '1f4ca', 'integer': '1f4ca', 'complete': '1f4ca', 'round': '1f4ca', 'full': '1f4ca',
            'negative': '2796', 'minus': '2796', 'below zero': '2796', 'debt': '2796', 'opposite': '2796',
            'positive': '2795', 'plus': '2795', 'above zero': '2795', 'good': '2795', 'optimistic': '2795',
            'square': '1f532', 'rectangle': '1f532', 'quadrilateral': '1f532', 'four sides': '1f532',
            'geometric': '1f532',
            'circle': '1f534', 'round': '1f534', 'sphere': '1f534', 'loop': '1f534', 'ring': '1f534',
            'triangle': '1f53a', 'three sides': '1f53a', 'pyramid': '1f53a', 'delta': '1f53a', 'angular': '1f53a',
            'angle': '1f4d0', 'corner': '1f4d0', 'vertex': '1f4d0', 'degree': '1f4d0', 'turn': '1f4d0',
            'line': '1f4cf', 'straight': '1f4cf', 'linear': '1f4cf', 'ruler': '1f4cf', 'measurement': '1f4cf',
            'curve': '1f4c8', 'arc': '1f4c8', 'bend': '1f4c8', 'curved': '1f4c8', 'smooth': '1f4c8',
            'pattern': '1f4c9', 'design': '1f4c9', 'sequence': '1f4c9', 'repetition': '1f4c9', 'order': '1f4c9',
            'random': '1f3b2', 'chance': '1f3b2', 'luck': '1f3b2', 'arbitrary': '1f3b2', 'unpredictable': '1f3b2',
            'probability': '1f3b2', 'odds': '1f3b2', 'likelihood': '1f3b2', 'chance': '1f3b2', 'possibility': '1f3b2',
            'average': '1f4ca', 'mean': '1f4ca', 'typical': '1f4ca', 'normal': '1f4ca', 'standard': '1f4ca',
            'maximum': '1f4c8', 'highest': '1f4c8', 'peak': '1f4c8', 'top': '1f4c8', 'most': '1f4c8',
            'minimum': '1f4c9', 'lowest': '1f4c9', 'bottom': '1f4c9', 'least': '1f4c9', 'smallest': '1f4c9',

            # Academic and educational terms - MASSIVE EXPANSION
            'education': '1f393', 'learning': '1f393', 'school': '1f393', 'knowledge': '1f393', 'study': '1f393',
            'student': '1f9d1-200d-1f393', 'pupil': '1f9d1-200d-1f393', 'learner': '1f9d1-200d-1f393',
            'scholar': '1f9d1-200d-1f393',
            'teacher': '1f9d1-200d-1f3eb', 'instructor': '1f9d1-200d-1f3eb', 'educator': '1f9d1-200d-1f3eb',
            'professor': '1f9d1-200d-1f3eb',
            'school': '1f3eb', 'academy': '1f3eb', 'institution': '1f3eb', 'educational': '1f3eb', 'campus': '1f3eb',
            'university': '1f3eb', 'college': '1f3eb', 'higher education': '1f3eb', 'campus': '1f3eb',
            'degree': '1f3eb',
            'classroom': '1f3eb', 'room': '1f3eb', 'learning space': '1f3eb', 'educational': '1f3eb',
            'academic': '1f3eb',
            'library': '1f4da', 'books': '1f4da', 'reading': '1f4da', 'research': '1f4da', 'knowledge': '1f4da',
            'book': '1f4d6', 'textbook': '1f4d6', 'novel': '1f4d6', 'manual': '1f4d6', 'publication': '1f4d6',
            'page': '1f4c4', 'sheet': '1f4c4', 'paper': '1f4c4', 'document': '1f4c4', 'leaf': '1f4c4',
            'chapter': '1f4d6', 'section': '1f4d6', 'part': '1f4d6', 'division': '1f4d6', 'segment': '1f4d6',
            'lesson': '1f4d6', 'class': '1f4d6', 'tutorial': '1f4d6', 'instruction': '1f4d6', 'session': '1f4d6',
            'homework': '1f4dd', 'assignment': '1f4dd', 'task': '1f4dd', 'exercise': '1f4dd', 'practice': '1f4dd',
            'test': '1f4dd', 'exam': '1f4dd', 'quiz': '1f4dd', 'assessment': '1f4dd', 'evaluation': '1f4dd',
            'grade': '1f4ca', 'mark': '1f4ca', 'score': '1f4ca', 'rating': '1f4ca', 'evaluation': '1f4ca',
            'diploma': '1f4dc', 'certificate': '1f4dc', 'degree': '1f4dc', 'qualification': '1f4dc',
            'credential': '1f4dc',
            'graduation': '1f393', 'commencement': '1f393', 'ceremony': '1f393', 'completion': '1f393',
            'achievement': '1f393',
            'research': '1f52d', 'investigation': '1f52d', 'study': '1f52d', 'analysis': '1f52d',
            'exploration': '1f52d',
            'experiment': '1f9ea', 'test': '1f9ea', 'trial': '1f9ea', 'laboratory': '1f9ea', 'scientific': '1f9ea',
            'science': '1f52c', 'biology': '1f52c', 'chemistry': '1f52c', 'physics': '1f52c', 'scientific': '1f52c',
            'mathematics': '1f522', 'math': '1f522', 'arithmetic': '1f522', 'algebra': '1f522', 'calculus': '1f522',
            'history': '1f4dc', 'past': '1f4dc', 'historical': '1f4dc', 'ancient': '1f4dc', 'timeline': '1f4dc',
            'geography': '1f30d', 'world': '1f30d', 'map': '1f30d', 'location': '1f30d', 'places': '1f30d',
            'language': '1f5e3', 'linguistics': '1f5e3', 'grammar': '1f5e3', 'vocabulary': '1f5e3',
            'communication': '1f5e3',
            'literature': '1f4da', 'poetry': '1f4da', 'prose': '1f4da', 'writing': '1f4da', 'authors': '1f4da',
            'art': '1f3a8', 'painting': '1f3a8', 'drawing': '1f3a8', 'sculpture': '1f3a8', 'creative': '1f3a8',
            'music': '1f3b5', 'melody': '1f3b5', 'rhythm': '1f3b5', 'harmony': '1f3b5', 'song': '1f3b5',
            'philosophy': '1f914', 'wisdom': '1f914', 'thought': '1f914', 'reasoning': '1f914', 'ethics': '1f914',
            'psychology': '1f9e0', 'mind': '1f9e0', 'behavior': '1f9e0', 'mental': '1f9e0', 'cognitive': '1f9e0',
            'sociology': '1f465', 'society': '1f465', 'social': '1f465', 'culture': '1f465', 'community': '1f465',
            'economics': '1f4b0', 'money': '1f4b0', 'finance': '1f4b0', 'business': '1f4b0', 'trade': '1f4b0',
            'computer science': '1f4bb', 'programming': '1f4bb', 'coding': '1f4bb', 'software': '1f4bb',
            'technology': '1f4bb',
            'engineering': '1f527', 'construction': '1f527', 'building': '1f527', 'design': '1f527',
            'technical': '1f527',
            'medicine': '1f489', 'health': '1f489', 'doctor': '1f489', 'medical': '1f489', 'healing': '1f489',
            'law': '2696', 'legal': '2696', 'justice': '2696', 'court': '2696', 'lawyer': '2696',
            'question': '2753', 'ask': '2753', 'inquiry': '2753', 'query': '2753', 'interrogation': '2753',
            'answer': '2705', 'solution': '2705', 'response': '2705', 'reply': '2705', 'resolution': '2705',
            'correct': '2714', 'right': '2714', 'accurate': '2714', 'true': '2714', 'proper': '2714',
            'wrong': '274c', 'incorrect': '274c', 'false': '274c', 'mistake': '274c', 'error': '274c',
            'true': '2705', 'fact': '2705', 'reality': '2705', 'actual': '2705', 'genuine': '2705',
            'false': '274c', 'lie': '274c', 'untrue': '274c', 'fake': '274c', 'deceptive': '274c',
            'fact': '1f4ca', 'truth': '1f4ca', 'information': '1f4ca', 'data': '1f4ca', 'evidence': '1f4ca',
            'opinion': '1f4ad', 'view': '1f4ad', 'belief': '1f4ad', 'perspective': '1f4ad', 'thought': '1f4ad',
            'theory': '1f52c', 'hypothesis': '1f52c', 'concept': '1f52c', 'idea': '1f52c', 'principle': '1f52c',
            'practice': '1f3af', 'exercise': '1f3af', 'training': '1f3af', 'rehearsal': '1f3af', 'drill': '1f3af',
            'skill': '1f4aa', 'ability': '1f4aa', 'talent': '1f4aa', 'expertise': '1f4aa', 'competence': '1f4aa',
            'knowledge': '1f9e0', 'wisdom': '1f9e0', 'understanding': '1f9e0', 'awareness': '1f9e0', 'insight': '1f9e0',
            'memory': '1f9e0', 'recall': '1f9e0', 'remember': '1f9e0', 'retention': '1f9e0', 'recollection': '1f9e0',
            'concentration': '1f9e0', 'focus': '1f9e0', 'attention': '1f9e0', 'mindfulness': '1f9e0',
            'awareness': '1f9e0',
            'creativity': '1f3a8', 'imagination': '1f3a8', 'innovation': '1f3a8', 'originality': '1f3a8',
            'artistic': '1f3a8',
            'logic': '1f9e0', 'reasoning': '1f9e0', 'rational': '1f9e0', 'logical': '1f9e0', 'systematic': '1f9e0',
            'analysis': '1f52d', 'examination': '1f52d', 'investigation': '1f52d', 'breakdown': '1f52d',
            'study': '1f52d',
            'synthesis': '1f4ca', 'combination': '1f4ca', 'integration': '1f4ca', 'merge': '1f4ca', 'unite': '1f4ca',
            'comparison': '2696', 'contrast': '2696', 'difference': '2696', 'similarity': '2696', 'analogy': '2696',
            'evaluation': '1f4ca', 'assessment': '1f4ca', 'judgment': '1f4ca', 'appraisal': '1f4ca', 'review': '1f4ca',
            'conclusion': '1f3c1', 'ending': '1f3c1', 'result': '1f3c1', 'outcome': '1f3c1', 'finish': '1f3c1',
            'summary': '1f4dd', 'overview': '1f4dd', 'synopsis': '1f4dd', 'abstract': '1f4dd', 'outline': '1f4dd',
            'definition': '1f4dd', 'meaning': '1f4dd', 'explanation': '1f4dd', 'description': '1f4dd',
            'interpretation': '1f4dd',
            'example': '1f4dd', 'instance': '1f4dd', 'sample': '1f4dd', 'illustration': '1f4dd',
            'demonstration': '1f4dd',
            'evidence': '1f4ca', 'proof': '1f4ca', 'support': '1f4ca', 'confirmation': '1f4ca', 'verification': '1f4ca',
            'argument': '1f5e3', 'debate': '1f5e3', 'discussion': '1f5e3', 'reasoning': '1f5e3', 'case': '1f5e3',
            'category': '1f4ca', 'classification': '1f4ca', 'group': '1f4ca', 'type': '1f4ca', 'kind': '1f4ca',
            'concept': '1f4a1', 'idea': '1f4a1', 'notion': '1f4a1', 'thought': '1f4a1', 'principle': '1f4a1',
            'process': '1f504', 'procedure': '1f504', 'method': '1f504', 'system': '1f504', 'approach': '1f504',
            'sequence': '1f522', 'order': '1f522', 'series': '1f522', 'progression': '1f522', 'chain': '1f522',
            'structure': '1f3d7', 'organization': '1f3d7', 'framework': '1f3d7', 'arrangement': '1f3d7',
            'pattern': '1f3d7',
            'function': '2699', 'purpose': '2699', 'role': '2699', 'operation': '2699', 'use': '2699',
            'relationship': '1f517', 'connection': '1f517', 'link': '1f517', 'association': '1f517', 'bond': '1f517',
            'cause': '27a1', 'reason': '27a1', 'source': '27a1', 'origin': '27a1', 'factor': '27a1',
            'effect': '1f4a5', 'result': '1f4a5', 'consequence': '1f4a5', 'outcome': '1f4a5', 'impact': '1f4a5',
            'solution': '1f511', 'answer': '1f511', 'resolution': '1f511', 'fix': '1f511', 'remedy': '1f511',
            'problem': '26a0', 'issue': '26a0', 'difficulty': '26a0', 'challenge': '26a0', 'obstacle': '26a0',

            # Weather and climate terms
            'weather': '1f324', 'climate': '1f324', 'atmosphere': '1f324', 'conditions': '1f324',
            'meteorology': '1f324',
            'temperature': '1f321', 'hot': '1f321', 'warm': '1f321', 'cool': '1f321', 'cold': '1f321',
            'humidity': '1f4a7', 'moisture': '1f4a7', 'dampness': '1f4a7', 'wet': '1f4a7', 'dry': '1f4a7',
            'pressure': '1f4c8', 'barometric': '1f4c8', 'atmospheric': '1f4c8', 'high': '1f4c8', 'low': '1f4c8',
            'forecast': '1f4fa', 'prediction': '1f4fa', 'outlook': '1f4fa', 'projection': '1f4fa', 'expect': '1f4fa',
            'season': '1f333', 'seasonal': '1f333', 'spring': '1f33c', 'summer': '2600', 'autumn': '1f343',
            'fall': '1f343', 'winter': '2744', 'monsoon': '1f327', 'dry season': '2600', 'wet season': '1f327',

            # Body parts - COMPREHENSIVE COVERAGE
            'head': '1f9d1', 'skull': '1f9d1', 'brain': '1f9e0', 'mind': '1f9e0', 'cranium': '1f9d1',
            'face': '1f9d1', 'facial': '1f9d1', 'countenance': '1f9d1', 'visage': '1f9d1', 'expression': '1f9d1',
            'eye': '1f441', 'eyes': '1f441', 'vision': '1f441', 'sight': '1f441', 'see': '1f441',
            'ear': '1f442', 'ears': '1f442', 'hearing': '1f442', 'listen': '1f442', 'sound': '1f442',
            'nose': '1f443', 'smell': '1f443', 'scent': '1f443', 'odor': '1f443', 'fragrance': '1f443',
            'mouth': '1f444', 'lips': '1f444', 'speak': '1f444', 'talk': '1f444', 'oral': '1f444',
            'tongue': '1f445', 'taste': '1f445', 'lick': '1f445', 'flavor': '1f445', 'speech': '1f445',
            'teeth': '1f9b7', 'tooth': '1f9b7', 'dental': '1f9b7', 'bite': '1f9b7', 'chew': '1f9b7',
            'neck': '1f9d1', 'throat': '1f9d1', 'cervical': '1f9d1', 'nape': '1f9d1', 'adam apple': '1f9d1',
            'shoulder': '1f9b5', 'shoulders': '1f9b5', 'blade': '1f9b5', 'joint': '1f9b5', 'carry': '1f9b5',
            'arm': '1f4aa', 'arms': '1f4aa', 'limb': '1f4aa', 'upper': '1f4aa', 'strength': '1f4aa',
            'elbow': '1f4aa', 'bend': '1f4aa', 'joint': '1f4aa', 'hinge': '1f4aa', 'funny bone': '1f4aa',
            'wrist': '1f91a', 'joint': '1f91a', 'hand': '1f91a', 'flexible': '1f91a', 'rotate': '1f91a',
            'hand': '1f91a', 'hands': '1f91a', 'palm': '1f91a', 'grip': '1f91a', 'grasp': '1f91a',
            'finger': '1f448', 'fingers': '1f448', 'digit': '1f448', 'point': '1f448', 'touch': '1f448',
            'thumb': '1f44d', 'opposable': '1f44d', 'digit': '1f44d', 'good': '1f44d', 'approval': '1f44d',
            'nail': '1f485', 'fingernail': '1f485', 'claw': '1f485', 'scratch': '1f485', 'manicure': '1f485',
            'chest': '1f9b5', 'torso': '1f9b5', 'breast': '1f9b5', 'ribcage': '1f9b5', 'pectoral': '1f9b5',
            'back': '1f9b5', 'spine': '1f9b5', 'backbone': '1f9b5', 'vertebrae': '1f9b5', 'rear': '1f9b5',
            'stomach': '1f9b5', 'belly': '1f9b5', 'abdomen': '1f9b5', 'tummy': '1f9b5', 'gut': '1f9b5',
            'waist': '1f9b5', 'middle': '1f9b5', 'center': '1f9b5', 'belt': '1f9b5', 'narrow': '1f9b5',
            'hip': '1f9b5', 'hips': '1f9b5', 'pelvis': '1f9b5', 'joint': '1f9b5', 'bone': '1f9b5',
            'leg': '1f9b5', 'legs': '1f9b5', 'limb': '1f9b5', 'lower': '1f9b5', 'walk': '1f9b5',
            'thigh': '1f9b5', 'upper leg': '1f9b5', 'femur': '1f9b5', 'muscle': '1f9b5', 'quadriceps': '1f9b5',
            'knee': '1f9b5', 'joint': '1f9b5', 'bend': '1f9b5', 'kneecap': '1f9b5', 'patella': '1f9b5',
            'shin': '1f9b5', 'calf': '1f9b5', 'lower leg': '1f9b5', 'tibia': '1f9b5', 'fibula': '1f9b5',
            'ankle': '1f9b6', 'joint': '1f9b6', 'foot': '1f9b6', 'twist': '1f9b6', 'sprain': '1f9b6',
            'foot': '1f9b6', 'feet': '1f9b6', 'sole': '1f9b6', 'heel': '1f9b6', 'arch': '1f9b6',
            'toe': '1f9b6', 'toes': '1f9b6', 'digit': '1f9b6', 'big toe': '1f9b6', 'pinky': '1f9b6',
            'skin': '1f9b4', 'epidermis': '1f9b4', 'dermis': '1f9b4', 'surface': '1f9b4', 'pore': '1f9b4',
            'hair': '1f9b1', 'follicle': '1f9b1', 'strand': '1f9b1', 'locks': '1f9b1', 'mane': '1f9b1',
            'muscle': '1f4aa', 'muscles': '1f4aa', 'tissue': '1f4aa', 'fiber': '1f4aa', 'strength': '1f4aa',
            'bone': '1f9b4', 'bones': '1f9b4', 'skeleton': '1f9b4', 'calcium': '1f9b4', 'marrow': '1f9b4',
            'blood': '1fa78', 'circulation': '1fa78', 'red cells': '1fa78', 'plasma': '1fa78', 'flow': '1fa78',
            'heart': '2764', 'cardiac': '2764', 'pump': '2764', 'beat': '2764', 'circulation': '2764',
            'lung': '1fac1', 'lungs': '1fac1', 'breathing': '1fac1', 'oxygen': '1fac1', 'respiratory': '1fac1',
            'liver': '1f9b4', 'organ': '1f9b4', 'detox': '1f9b4', 'metabolism': '1f9b4', 'bile': '1f9b4',
            'kidney': '1f9b4', 'kidneys': '1f9b4', 'filter': '1f9b4', 'urine': '1f9b4', 'waste': '1f9b4',
            'brain': '1f9e0', 'cerebrum': '1f9e0', 'mind': '1f9e0', 'neurons': '1f9e0', 'intelligence': '1f9e0',

            # Health and medical terms
            'health': '1f9e1', 'wellness': '1f9e1', 'fitness': '1f9e1', 'well-being': '1f9e1', 'vitality': '1f9e1',
            'sick': '1f637', 'illness': '1f637', 'disease': '1f637', 'unwell': '1f637', 'infection': '1f637',
            'pain': '1f915', 'hurt': '1f915', 'ache': '1f915', 'sore': '1f915', 'discomfort': '1f915',
            'medicine': '1f48a', 'medication': '1f48a', 'drug': '1f48a', 'pill': '1f48a', 'treatment': '1f48a',
            'doctor': '1f468-200d-2695-fe0f', 'physician': '1f468-200d-2695-fe0f', 'medical': '1f468-200d-2695-fe0f',
            'nurse': '1f468-200d-2695-fe0f', 'caregiver': '1f468-200d-2695-fe0f', 'healthcare': '1f468-200d-2695-fe0f',
            'hospital': '1f3e5', 'clinic': '1f3e5', 'medical center': '1f3e5', 'emergency': '1f3e5', 'ward': '1f3e5',
            'surgery': '1f489', 'operation': '1f489', 'procedure': '1f489', 'surgical': '1f489', 'incision': '1f489',
            'injection': '1f489', 'shot': '1f489', 'vaccine': '1f489', 'needle': '1f489', 'syringe': '1f489',
            'bandage': '1fa79', 'dressing': '1fa79', 'wrap': '1fa79', 'gauze': '1fa79', 'adhesive': '1fa79',
            'thermometer': '1f321', 'temperature': '1f321', 'fever': '1f321', 'heat': '1f321', 'measure': '1f321',
            'stethoscope': '1fa7a', 'listen': '1fa7a', 'heartbeat': '1fa7a', 'breathing': '1fa7a',
            'examination': '1fa7a',
            'x-ray': '1f9b4', 'scan': '1f9b4', 'image': '1f9b4', 'bone': '1f9b4', 'medical': '1f9b4',
            'wheelchair': '267f', 'mobility': '267f', 'assistance': '267f', 'disabled': '267f', 'access': '267f',
            'crutches': '1f9af', 'walking aid': '1f9af', 'support': '1f9af', 'injury': '1f9af', 'mobility': '1f9af',

            # Professions and occupations - COMPREHENSIVE COVERAGE
            'job': '1f4bc', 'work': '1f4bc', 'career': '1f4bc', 'occupation': '1f4bc', 'profession': '1f4bc',
            'doctor': '1f468-200d-2695-fe0f', 'physician': '1f468-200d-2695-fe0f', 'surgeon': '1f468-200d-2695-fe0f',
            'nurse': '1f469-200d-2695-fe0f', 'caregiver': '1f469-200d-2695-fe0f',
            'medical assistant': '1f469-200d-2695-fe0f',
            'teacher': '1f469-200d-1f3eb', 'educator': '1f469-200d-1f3eb', 'instructor': '1f469-200d-1f3eb',
            'professor': '1f469-200d-1f3eb',
            'lawyer': '1f468-200d-2696-fe0f', 'attorney': '1f468-200d-2696-fe0f', 'legal': '1f468-200d-2696-fe0f',
            'judge': '1f468-200d-2696-fe0f',
            'police': '1f46e', 'officer': '1f46e', 'cop': '1f46e', 'law enforcement': '1f46e', 'sheriff': '1f46e',
            'firefighter': '1f468-200d-1f692', 'fireman': '1f468-200d-1f692', 'rescue': '1f468-200d-1f692',
            'emergency': '1f468-200d-1f692',
            'chef': '1f468-200d-1f373', 'cook': '1f468-200d-1f373', 'culinary': '1f468-200d-1f373',
            'kitchen': '1f468-200d-1f373',
            'waiter': '1f468-200d-1f37d', 'waitress': '1f469-200d-1f37d', 'server': '1f468-200d-1f37d',
            'restaurant': '1f468-200d-1f37d',
            'farmer': '1f468-200d-1f33e', 'agriculture': '1f468-200d-1f33e', 'crops': '1f468-200d-1f33e',
            'rural': '1f468-200d-1f33e',
            'mechanic': '1f468-200d-1f527', 'repair': '1f468-200d-1f527', 'fix': '1f468-200d-1f527',
            'garage': '1f468-200d-1f527',
            'engineer': '1f468-200d-1f4bb', 'technical': '1f468-200d-1f4bb', 'design': '1f468-200d-1f4bb',
            'build': '1f468-200d-1f4bb',
            'scientist': '1f468-200d-1f52c', 'researcher': '1f468-200d-1f52c', 'laboratory': '1f468-200d-1f52c',
            'experiment': '1f468-200d-1f52c',
            'programmer': '1f468-200d-1f4bb', 'developer': '1f468-200d-1f4bb', 'coding': '1f468-200d-1f4bb',
            'software': '1f468-200d-1f4bb',
            'artist': '1f468-200d-1f3a8', 'painter': '1f468-200d-1f3a8', 'creative': '1f468-200d-1f3a8',
            'art': '1f468-200d-1f3a8',
            'musician': '1f468-200d-1f3a4', 'singer': '1f468-200d-1f3a4', 'performer': '1f468-200d-1f3a4',
            'music': '1f468-200d-1f3a4',
            'actor': '1f468-200d-1f3ad', 'actress': '1f469-200d-1f3ad', 'performer': '1f468-200d-1f3ad',
            'theater': '1f468-200d-1f3ad',
            'pilot': '1f468-200d-2708-fe0f', 'aviator': '1f468-200d-2708-fe0f', 'captain': '1f468-200d-2708-fe0f',
            'flight': '1f468-200d-2708-fe0f',
            'driver': '1f468-200d-1f692', 'chauffeur': '1f468-200d-1f692', 'transport': '1f468-200d-1f692',
            'vehicle': '1f468-200d-1f692',
            'dentist': '1f9b7', 'dental': '1f9b7', 'teeth': '1f9b7', 'oral': '1f9b7', 'hygienist': '1f9b7',
            'veterinarian': '1f468-200d-2695-fe0f', 'vet': '1f468-200d-2695-fe0f',
            'animal doctor': '1f468-200d-2695-fe0f',
            'photographer': '1f4f7', 'camera': '1f4f7', 'picture': '1f4f7', 'visual': '1f4f7', 'image': '1f4f7',
            'journalist': '1f4f0', 'reporter': '1f4f0', 'news': '1f4f0', 'media': '1f4f0', 'press': '1f4f0',
            'banker': '1f4b0', 'finance': '1f4b0', 'money': '1f4b0', 'investment': '1f4b0', 'financial': '1f4b0',
            'accountant': '1f4ca', 'numbers': '1f4ca', 'tax': '1f4ca', 'bookkeeper': '1f4ca', 'audit': '1f4ca',
            'salesperson': '1f4b0', 'sales': '1f4b0', 'retail': '1f4b0', 'customer': '1f4b0', 'commerce': '1f4b0',
            'manager': '1f4bc', 'boss': '1f4bc', 'supervisor': '1f4bc', 'executive': '1f4bc', 'leadership': '1f4bc',
            'secretary': '1f4c4', 'assistant': '1f4c4', 'office': '1f4c4', 'administrative': '1f4c4', 'clerk': '1f4c4',
            'librarian': '1f4da', 'books': '1f4da', 'information': '1f4da', 'research': '1f4da', 'knowledge': '1f4da',
            'barber': '2702', 'hairdresser': '2702', 'stylist': '2702', 'hair': '2702', 'cut': '2702',
            'electrician': '26a1', 'electrical': '26a1', 'wiring': '26a1', 'power': '26a1', 'voltage': '26a1',
            'plumber': '1f6b0', 'pipes': '1f6b0', 'water': '1f6b0', 'repair': '1f6b0', 'fix': '1f6b0',
            'carpenter': '1f528', 'wood': '1f528', 'build': '1f528', 'construction': '1f528', 'hammer': '1f528',
            'architect': '1f3d7', 'design': '1f3d7', 'building': '1f3d7', 'blueprint': '1f3d7', 'structure': '1f3d7',
            'janitor': '1f9fd', 'custodian': '1f9fd', 'cleaning': '1f9fd', 'maintenance': '1f9fd', 'clean': '1f9fd',
            'security': '1f6e1', 'guard': '1f6e1', 'protection': '1f6e1', 'safety': '1f6e1', 'watch': '1f6e1',
            'gardener': '1f33f', 'landscaper': '1f33f', 'plants': '1f33f', 'garden': '1f33f', 'green': '1f33f',
            'athlete': '1f3c3', 'sports': '1f3c3', 'fitness': '1f3c3', 'competition': '1f3c3', 'training': '1f3c3',
            'coach': '1f3c5', 'trainer': '1f3c5', 'sports': '1f3c5', 'team': '1f3c5', 'mentor': '1f3c5',
            'referee': '1f93e', 'umpire': '1f93e', 'official': '1f93e', 'sports': '1f93e', 'judge': '1f93e',

            # Buildings and places - MASSIVE EXPANSION
            'building': '1f3e2', 'structure': '1f3e2', 'construction': '1f3e2', 'architecture': '1f3e2',
            'edifice': '1f3e2',
            'house': '1f3e0', 'home': '1f3e0', 'residence': '1f3e0', 'dwelling': '1f3e0', 'abode': '1f3e0',
            'apartment': '1f3e2', 'flat': '1f3e2', 'unit': '1f3e2', 'condo': '1f3e2', 'suite': '1f3e2',
            'office': '1f3e2', 'workplace': '1f3e2', 'business': '1f3e2', 'corporate': '1f3e2', 'commercial': '1f3e2',
            'store': '1f3ea', 'shop': '1f3ea', 'retail': '1f3ea', 'market': '1f3ea', 'boutique': '1f3ea',
            'mall': '1f3ea', 'shopping center': '1f3ea', 'plaza': '1f3ea', 'complex': '1f3ea', 'commercial': '1f3ea',
            'restaurant': '1f37d', 'cafe': '1f37d', 'diner': '1f37d', 'eatery': '1f37d', 'bistro': '1f37d',
            'hotel': '1f3e8', 'motel': '1f3e8', 'inn': '1f3e8', 'lodge': '1f3e8', 'resort': '1f3e8',
            'hospital': '1f3e5', 'clinic': '1f3e5', 'medical center': '1f3e5', 'infirmary': '1f3e5', 'ward': '1f3e5',
            'school': '1f3eb', 'academy': '1f3eb', 'institute': '1f3eb', 'college': '1f3eb', 'university': '1f3eb',
            'library': '1f4da', 'archive': '1f4da', 'repository': '1f4da', 'collection': '1f4da',
            'reading room': '1f4da',
            'museum': '1f3db', 'gallery': '1f3db', 'exhibition': '1f3db', 'art': '1f3db', 'culture': '1f3db',
            'theater': '1f3ad', 'cinema': '1f3ad', 'playhouse': '1f3ad', 'auditorium': '1f3ad', 'stage': '1f3ad',
            'church': '26ea', 'cathedral': '26ea', 'chapel': '26ea', 'sanctuary': '26ea', 'religious': '26ea',
            'mosque': '1f54c', 'islamic': '1f54c', 'minaret': '1f54c', 'prayer': '1f54c', 'muslim': '1f54c',
            'temple': '1f6d5', 'shrine': '1f6d5', 'sacred': '1f6d5', 'worship': '1f6d5', 'spiritual': '1f6d5',
            'synagogue': '1f54d', 'jewish': '1f54d', 'hebrew': '1f54d', 'torah': '1f54d', 'worship': '1f54d',
            'bank': '1f3e6', 'financial': '1f3e6', 'money': '1f3e6', 'deposit': '1f3e6', 'credit': '1f3e6',
            'post office': '1f3e4', 'mail': '1f3e4', 'postal': '1f3e4', 'delivery': '1f3e4', 'packages': '1f3e4',
            'police station': '1f3e2', 'law enforcement': '1f3e2', 'precinct': '1f3e2', 'safety': '1f3e2',
            'security': '1f3e2',
            'fire station': '1f692', 'firehouse': '1f692', 'emergency': '1f692', 'rescue': '1f692',
            'firefighter': '1f692',
            'gas station': '26fd', 'fuel': '26fd', 'gasoline': '26fd', 'petrol': '26fd', 'service': '26fd',
            'train station': '1f686', 'railway': '1f686', 'depot': '1f686', 'terminal': '1f686', 'platform': '1f686',
            'airport': '2708', 'terminal': '2708', 'aviation': '2708', 'flight': '2708', 'runway': '2708',
            'port': '2693', 'harbor': '2693', 'dock': '2693', 'pier': '2693', 'marina': '2693',
            'bridge': '1f309', 'span': '1f309', 'crossing': '1f309', 'overpass': '1f309', 'viaduct': '1f309',
            'tunnel': '1f573', 'underground': '1f573', 'passage': '1f573', 'subway': '1f573', 'underpass': '1f573',
            'road': '1f6e3', 'street': '1f6e3', 'avenue': '1f6e3', 'highway': '1f6e3', 'boulevard': '1f6e3',
            'parking': '1f17f', 'garage': '1f17f', 'lot': '1f17f', 'space': '1f17f', 'vehicle': '1f17f',
            'park': '1f3de', 'garden': '1f3de', 'playground': '1f3de', 'recreation': '1f3de', 'green space': '1f3de',
            'zoo': '1f98e', 'animals': '1f98e', 'wildlife': '1f98e', 'safari': '1f98e', 'conservation': '1f98e',
            'aquarium': '1f420', 'marine': '1f420', 'fish': '1f420', 'underwater': '1f420', 'ocean': '1f420',
            'stadium': '1f3df', 'arena': '1f3df', 'sports': '1f3df', 'field': '1f3df', 'competition': '1f3df',
            'gym': '1f3cb', 'fitness': '1f3cb', 'exercise': '1f3cb', 'workout': '1f3cb', 'health': '1f3cb',
            'pool': '1f3ca', 'swimming': '1f3ca', 'water': '1f3ca', 'recreation': '1f3ca', 'aquatic': '1f3ca',
            'beach': '1f3d6', 'shore': '1f3d6', 'sand': '1f3d6', 'ocean': '1f3d6', 'vacation': '1f3d6',
            'mountain': '26f0', 'peak': '26f0', 'summit': '26f0', 'hill': '26f0', 'elevation': '26f0',
            'valley': '1f3dc', 'canyon': '1f3dc', 'gorge': '1f3dc', 'depression': '1f3dc', 'low land': '1f3dc',
            'forest': '1f333', 'woods': '1f333', 'trees': '1f333', 'wilderness': '1f333', 'nature': '1f333',
            'desert': '1f3dc', 'arid': '1f3dc', 'sand': '1f3dc', 'dunes': '1f3dc', 'dry': '1f3dc',
            'island': '1f3dd', 'isle': '1f3dd', 'atoll': '1f3dd', 'landmass': '1f3dd', 'tropical': '1f3dd',
            'cave': '1f573', 'cavern': '1f573', 'grotto': '1f573', 'underground': '1f573', 'hollow': '1f573',
            'volcano': '1f30b', 'crater': '1f30b', 'lava': '1f30b', 'eruption': '1f30b', 'magma': '1f30b',

            # Famous Brands and Companies
            'apple': '1f34e', 'iphone': '1f4f1', 'mac': '1f4bb', 'macbook': '1f4bb', 'ipad': '1f4f1',
            'google': '1f50e', 'android': '1f916', 'chrome': '1f310', 'gmail': '1f4e7', 'youtube': '1f3a5',
            'amazon': '1f4e6', 'prime': '1f4e6', 'alexa': '1f399', 'kindle': '1f4da', 'aws': '2601',
            'microsoft': '1f3b2', 'windows': '1f5a5', 'xbox': '1f3ae', 'office': '1f4c4', 'teams': '1f465',
            'facebook': '1f310', 'instagram': '1f4f7', 'whatsapp': '1f4ac', 'messenger': '1f4ac', 'meta': '1f310',
            'netflix': '1f4fa', 'streaming': '1f4fa', 'shows': '1f4fa', 'movies': '1f3ac', 'series': '1f4fa',
            'tesla': '1f697', 'spacex': '1f680', 'starlink': '1f6f0', 'boring': '1f687', 'neuralink': '1f9e0',
            'nike': '1f45f', 'adidas': '1f45f', 'puma': '1f406', 'reebok': '1f45f', 'under armour': '1f455',
            'coca cola': '1f964', 'pepsi': '1f964', 'sprite': '1f964', 'fanta': '1f964', 'dr pepper': '1f964',
            'mcdonalds': '1f354', 'burger king': '1f354', 'wendys': '1f354', 'kfc': '1f357', 'subway': '1f96a',
            'starbucks': '2615', 'dunkin': '1f369', 'costa': '2615', 'tim hortons': '2615', 'peets': '2615',
            'disney': '1f3aa', 'pixar': '1f3a5', 'marvel': '1f4a8', 'star wars': '1f680',
            'national geographic': '1f30d',
            'sony': '1f3a4', 'playstation': '1f3ae', 'nintendo': '1f3ae', 'switch': '1f3ae', 'sega': '1f3ae',
            'samsung': '1f4f1', 'lg': '1f4fa', 'huawei': '1f4f1', 'xiaomi': '1f4f1', 'oneplus': '1f4f1',
            'toyota': '1f697', 'honda': '1f697', 'bmw': '1f697', 'mercedes': '1f697', 'audi': '1f697',
            'visa': '1f4b3', 'mastercard': '1f4b3', 'paypal': '1f4b3', 'amex': '1f4b3', 'discover': '1f4b3',
            'spotify': '1f3a7', 'apple music': '1f3a7', 'pandora': '1f3a7', 'tidal': '1f3a7', 'deezer': '1f3a7',
            'tiktok': '1f4f9', 'snapchat': '1f47b', 'twitter': '1f426', 'linkedin': '1f4bc', 'pinterest': '1f4cc',
            'uber': '1f695', 'lyft': '1f695', 'doordash': '1f374', 'grubhub': '1f374', 'instacart': '1f6d2',
            'airbnb': '1f3e1', 'booking': '1f3e8', 'expedia': '2708', 'tripadvisor': '1f30d', 'vrbo': '1f3e1',

            # More Specific Locations and Buildings
            'apartment': '1f3e2', 'flat': '1f3e2', 'unit': '1f3e2', 'condo': '1f3e2', 'suite': '1f3e2',
            'office': '1f3e2', 'workplace': '1f3e2', 'business': '1f3e2', 'corporate': '1f3e2', 'commercial': '1f3e2',
            'store': '1f3ea', 'shop': '1f3ea', 'retail': '1f3ea', 'market': '1f3ea', 'boutique': '1f3ea',
            'mall': '1f3ea', 'shopping center': '1f3ea', 'plaza': '1f3ea', 'complex': '1f3ea', 'commercial': '1f3ea',
            'restaurant': '1f37d', 'cafe': '1f37d', 'diner': '1f37d', 'eatery': '1f37d', 'bistro': '1f37d',
            'bakery': '1f950', 'patisserie': '1f950', 'confectionery': '1f950', 'pastry shop': '1f950',
            'bread shop': '1f950',
            'bar': '1f378', 'pub': '1f378', 'tavern': '1f378', 'nightclub': '1f378', 'lounge': '1f378',
            'gym': '1f3cb', 'fitness center': '1f3cb', 'health club': '1f3cb', 'workout': '1f3cb', 'training': '1f3cb',
            'spa': '1f486', 'wellness': '1f486', 'massage': '1f486', 'relaxation': '1f486', 'therapy': '1f486',
            'salon': '2702', 'barbershop': '2702', 'hairdresser': '2702', 'beauty': '2702', 'stylist': '2702',
            'laundromat': '1f9fa', 'dry cleaner': '1f9fa', 'washer': '1f9fa', 'dryer': '1f9fa', 'cleaning': '1f9fa',
            'pharmacy': '1f48a', 'drugstore': '1f48a', 'chemist': '1f48a', 'apothecary': '1f48a', 'dispensary': '1f48a',
            'daycare': '1f476', 'nursery': '1f476', 'preschool': '1f476', 'childcare': '1f476', 'kindergarten': '1f476',
            'warehouse': '1f3ec', 'storage': '1f3ec', 'depot': '1f3ec', 'distribution': '1f3ec', 'facility': '1f3ec',
            'factory': '1f3ed', 'plant': '1f3ed', 'manufacturing': '1f3ed', 'industrial': '1f3ed',
            'production': '1f3ed',
            'studio': '1f3a5', 'workshop': '1f3a5', 'atelier': '1f3a5', 'creative space': '1f3a5', 'recording': '1f3a5',
            'garage': '1f698', 'carport': '1f698', 'parking': '1f698', 'auto shop': '1f698', 'mechanic': '1f698',
            'basement': '1f573', 'cellar': '1f573', 'underground': '1f573', 'lower level': '1f573',
            'subterranean': '1f573',
            'attic': '1f3e0', 'loft': '1f3e0', 'upper room': '1f3e0', 'garret': '1f3e0', 'top floor': '1f3e0',
            'balcony': '1f3e1', 'terrace': '1f3e1', 'porch': '1f3e1', 'veranda': '1f3e1', 'deck': '1f3e1',

            # More Specific Food Items and Cuisines
            'burger': '1f354', 'hamburger': '1f354', 'cheeseburger': '1f354', 'patty': '1f354', 'slider': '1f354',
            'pizza': '1f355', 'slice': '1f355', 'pepperoni': '1f355', 'margherita': '1f355', 'deep dish': '1f355',
            'taco': '1f32e', 'burrito': '1f32f', 'enchilada': '1f32e', 'quesadilla': '1f32e', 'nachos': '1f32e',
            'sushi': '1f363', 'sashimi': '1f363', 'maki': '1f363', 'nigiri': '1f363', 'temaki': '1f363',
            'ramen': '1f35c', 'udon': '1f35c', 'pho': '1f35c', 'noodle soup': '1f35c', 'broth': '1f35c',
            'curry': '1f35b', 'tikka masala': '1f35b', 'vindaloo': '1f35b', 'korma': '1f35b', 'biryani': '1f35b',
            'kebab': '1f969', 'shawarma': '1f969', 'gyro': '1f969', 'skewer': '1f969', 'souvlaki': '1f969',
            'croissant': '1f950', 'baguette': '1f956', 'brioche': '1f950', 'danish': '1f950', 'pastry': '1f950',
            'waffle': '1f9c7', 'pancake': '1f95e', 'crepe': '1f95e', 'french toast': '1f95e', 'flapjack': '1f95e',
            'salad': '1f957', 'greens': '1f957', 'lettuce': '1f957', 'kale': '1f957', 'spinach': '1f957',
            'smoothie': '1f379', 'juice': '1f379', 'shake': '1f379', 'blend': '1f379', 'acai': '1f379',
            'bbq': '1f356', 'barbecue': '1f356', 'grill': '1f356', 'smoked': '1f356', 'brisket': '1f356',
            'dim sum': '1f95f', 'dumpling': '1f95f', 'bao': '1f95f', 'wonton': '1f95f', 'potsticker': '1f95f',
            'falafel': '1f9c6', 'hummus': '1f9c6', 'tahini': '1f9c6', 'pita': '1f9c6', 'mediterranean': '1f9c6',
            'paella': '1f958', 'risotto': '1f958', 'jambalaya': '1f958', 'pilaf': '1f958', 'fried rice': '1f958',
            'poutine': '1f35f', 'fries': '1f35f', 'chips': '1f35f', 'wedges': '1f35f', 'potato': '1f35f',
            'gelato': '1f368', 'sorbet': '1f368', 'frozen yogurt': '1f368', 'soft serve': '1f368', 'sherbet': '1f368',
            'macaron': '1f36c', 'meringue': '1f36c', 'madeleine': '1f36c', 'petit four': '1f36c', 'truffle': '1f36c',
            'kimchi': '1f9c5', 'sauerkraut': '1f9c5', 'pickles': '1f9c5', 'fermented': '1f9c5', 'preserved': '1f9c5',
            'kombucha': '1f375', 'kefir': '1f375', 'probiotic': '1f375', 'fermented tea': '1f375', 'cultured': '1f375',

            # Sports and Activities
            'football': '1f3c8', 'american football': '1f3c8', 'nfl': '1f3c8', 'touchdown': '1f3c8',
            'quarterback': '1f3c8',
            'soccer': '26bd', 'football': '26bd', 'fifa': '26bd', 'goal': '26bd', 'striker': '26bd',
            'basketball': '1f3c0', 'nba': '1f3c0', 'hoop': '1f3c0', 'dunk': '1f3c0', 'court': '1f3c0',
            'baseball': '26be', 'mlb': '26be', 'bat': '26be', 'pitcher': '26be', 'homerun': '26be',
            'hockey': '1f3d2', 'nhl': '1f3d2', 'puck': '1f3d2', 'stick': '1f3d2', 'goalie': '1f3d2',
            'tennis': '1f3be', 'racket': '1f3be', 'court': '1f3be', 'serve': '1f3be', 'wimbledon': '1f3be',
            'golf': '1f3cc', 'club': '1f3cc', 'course': '1f3cc', 'putt': '1f3cc', 'birdie': '1f3cc',
            'swimming': '1f3ca', 'freestyle': '1f3ca', 'backstroke': '1f3ca', 'butterfly': '1f3ca',
            'breaststroke': '1f3ca',
            'skiing': '1f3bf', 'snowboard': '1f3bf', 'slopes': '1f3bf', 'winter': '1f3bf', 'powder': '1f3bf',
            'surfing': '1f3c4', 'waves': '1f3c4', 'board': '1f3c4', 'beach': '1f3c4', 'pipeline': '1f3c4',
            'volleyball': '1f3d0', 'beach volleyball': '1f3d0', 'spike': '1f3d0', 'net': '1f3d0', 'serve': '1f3d0',
            'cricket': '1f3cf', 'wicket': '1f3cf', 'bat': '1f3cf', 'bowler': '1f3cf', 'pitch': '1f3cf',
            'rugby': '1f3c9', 'scrum': '1f3c9', 'tackle': '1f3c9', 'try': '1f3c9', 'conversion': '1f3c9',
            'boxing': '1f94a', 'gloves': '1f94a', 'punch': '1f94a', 'knockout': '1f94a', 'ring': '1f94a',
            'martial arts': '1f94b', 'karate': '1f94b', 'judo': '1f94b', 'taekwondo': '1f94b', 'mma': '1f94b',
            'yoga': '1f9d8', 'meditation': '1f9d8', 'pose': '1f9d8', 'stretch': '1f9d8', 'mindfulness': '1f9d8',
            'pilates': '1f9d8', 'core': '1f9d8', 'reformer': '1f9d8', 'flexibility': '1f9d8', 'strength': '1f9d8',
            'crossfit': '1f3cb', 'hiit': '1f3cb', 'wod': '1f3cb', 'functional': '1f3cb', 'intensity': '1f3cb',
            'climbing': '1f9d7', 'bouldering': '1f9d7', 'rock': '1f9d7', 'mountaineering': '1f9d7', 'belay': '1f9d7',
            'cycling': '1f6b4', 'biking': '1f6b4', 'peloton': '1f6b4', 'tour': '1f6b4', 'spin': '1f6b4',

            # Technology and Digital Terms
            'smartphone': '1f4f1', 'mobile': '1f4f1', 'iphone': '1f4f1', 'android': '1f4f1', 'cellphone': '1f4f1',
            'laptop': '1f4bb', 'notebook': '1f4bb', 'macbook': '1f4bb', 'computer': '1f4bb', 'pc': '1f4bb',
            'tablet': '1f4f1', 'ipad': '1f4f1', 'surface': '1f4f1', 'touchscreen': '1f4f1', 'slate': '1f4f1',
            'smartwatch': '231a', 'wearable': '231a', 'apple watch': '231a', 'fitness tracker': '231a',
            'garmin': '231a',
            'headphones': '1f3a7', 'earbuds': '1f3a7', 'airpods': '1f3a7', 'beats': '1f3a7', 'audio': '1f3a7',
            'camera': '1f4f7', 'dslr': '1f4f7', 'mirrorless': '1f4f7', 'canon': '1f4f7', 'nikon': '1f4f7',
            'drone': '1f6f8', 'uav': '1f6f8', 'aerial': '1f6f8', 'quadcopter': '1f6f8', 'dji': '1f6f8',
            'printer': '1f5a8', 'scanner': '1f5a8', 'copier': '1f5a8', 'multifunction': '1f5a8', 'inkjet': '1f5a8',
            'router': '1f4e1', 'modem': '1f4e1', 'wifi': '1f4e1', 'network': '1f4e1', 'connection': '1f4e1',
            'server': '1f5a5', 'cloud': '2601', 'hosting': '1f5a5', 'datacenter': '1f5a5', 'rack': '1f5a5',
            'software': '1f4be', 'app': '1f4be', 'program': '1f4be', 'application': '1f4be', 'code': '1f4be',
            'website': '1f310', 'webpage': '1f310', 'site': '1f310', 'url': '1f310', 'domain': '1f310',
            'email': '1f4e7', 'mail': '1f4e7', 'message': '1f4e7', 'inbox': '1f4e7', 'gmail': '1f4e7',
            'social media': '1f4f1', 'facebook': '1f4f1', 'instagram': '1f4f1', 'twitter': '1f4f1', 'tiktok': '1f4f1',
            'streaming': '1f4fa', 'netflix': '1f4fa', 'youtube': '1f4fa', 'hulu': '1f4fa', 'disney plus': '1f4fa',
            'gaming': '1f3ae', 'video games': '1f3ae', 'console': '1f3ae', 'playstation': '1f3ae', 'xbox': '1f3ae',
            'virtual reality': '1f97d', 'vr': '1f97d', 'oculus': '1f97d', 'metaverse': '1f97d', 'immersive': '1f97d',
            'artificial intelligence': '1f916', 'ai': '1f916', 'machine learning': '1f916', 'neural network': '1f916',
            'deep learning': '1f916',
            'blockchain': '1f512', 'crypto': '1f512', 'bitcoin': '1f512', 'ethereum': '1f512', 'nft': '1f512',
            'cybersecurity': '1f512', 'firewall': '1f512', 'antivirus': '1f512', 'encryption': '1f512',
            'protection': '1f512',

            # Vehicles and Transportation
            'car': '1f697', 'automobile': '1f697', 'sedan': '1f697', 'vehicle': '1f697', 'drive': '1f697',
            'suv': '1f699', 'crossover': '1f699', 'jeep': '1f699', '4x4': '1f699', 'offroad': '1f699',
            'truck': '1f69a', 'pickup': '1f69a', 'lorry': '1f69a', 'semi': '1f69a', 'hauler': '1f69a',
            'van': '1f69b', 'minivan': '1f69b', 'cargo': '1f69b', 'delivery': '1f69b', 'transit': '1f69b',
            'bus': '1f68c', 'coach': '1f68c', 'transit': '1f68c', 'shuttle': '1f68c', 'double decker': '1f68c',
            'motorcycle': '1f3cd', 'bike': '1f3cd', 'chopper': '1f3cd', 'harley': '1f3cd', 'scooter': '1f3cd',
            'bicycle': '1f6b2', 'bike': '1f6b2', 'cycling': '1f6b2', 'pedal': '1f6b2', 'mountain bike': '1f6b2',
            'train': '1f686', 'railway': '1f686', 'locomotive': '1f686', 'rail': '1f686', 'amtrak': '1f686',
            'subway': '1f687', 'metro': '1f687', 'underground': '1f687', 'tube': '1f687', 'transit': '1f687',
            'tram': '1f68a', 'streetcar': '1f68a', 'trolley': '1f68a', 'light rail': '1f68a', 'cable car': '1f68a',
            'airplane': '2708', 'plane': '2708', 'jet': '2708', 'aircraft': '2708', 'aviation': '2708',
            'helicopter': '1f681', 'chopper': '1f681', 'copter': '1f681', 'heli': '1f681', 'whirlybird': '1f681',
            'boat': '1f6a2', 'ship': '1f6a2', 'vessel': '1f6a2', 'sailing': '1f6a2', 'yacht': '1f6a2',
            'cruise ship': '1f6f3', 'liner': '1f6f3', 'ocean liner': '1f6f3', 'passenger ship': '1f6f3',
            'vacation': '1f6f3',
            'ferry': '26f4', 'boat': '26f4', 'passenger': '26f4', 'transport': '26f4', 'crossing': '26f4',
            'canoe': '1f6f6', 'kayak': '1f6f6', 'rowboat': '1f6f6', 'paddle': '1f6f6', 'small boat': '1f6f6',
            'submarine': '1f6a2', 'underwater': '1f6a2', 'submersible': '1f6a2', 'u-boat': '1f6a2', 'deep sea': '1f6a2',
            'rocket': '1f680', 'spacecraft': '1f680', 'missile': '1f680', 'launch': '1f680', 'space': '1f680',
            'satellite': '1f6f0', 'orbit': '1f6f0', 'space': '1f6f0', 'communications': '1f6f0', 'gps': '1f6f0',
            'ufo': '1f6f8', 'flying saucer': '1f6f8', 'alien': '1f6f8', 'extraterrestrial': '1f6f8', 'space': '1f6f8',

            # Weather and Natural Phenomena
            'sun': '2600', 'sunny': '2600', 'sunshine': '2600', 'clear': '2600', 'bright': '2600',
            'cloud': '2601', 'cloudy': '2601', 'overcast': '2601', 'gray sky': '2601', 'covered': '2601',
            'rain': '1f327', 'rainy': '1f327', 'shower': '1f327', 'precipitation': '1f327', 'drizzle': '1f327',
            'thunderstorm': '26c8', 'lightning': '26c8', 'thunder': '26c8', 'storm': '26c8', 'electrical': '26c8',
            'snow': '1f328', 'snowy': '1f328', 'snowflake': '1f328', 'winter': '1f328', 'blizzard': '1f328',
            'fog': '1f32b', 'foggy': '1f32b', 'mist': '1f32b', 'haze': '1f32b', 'smog': '1f32b',
            'wind': '1f32c', 'windy': '1f32c', 'breeze': '1f32c', 'gust': '1f32c', 'blow': '1f32c',
            'tornado': '1f32a', 'twister': '1f32a', 'cyclone': '1f32a', 'whirlwind': '1f32a', 'funnel': '1f32a',
            'hurricane': '1f300', 'typhoon': '1f300', 'tropical storm': '1f300', 'cyclone': '1f300',
            'disaster': '1f300',
            'rainbow': '1f308', 'spectrum': '1f308', 'colors': '1f308', 'arc': '1f308', 'after rain': '1f308',
            'sunrise': '1f305', 'dawn': '1f305', 'morning': '1f305', 'daybreak': '1f305', 'early': '1f305',
            'sunset': '1f307', 'dusk': '1f307', 'evening': '1f307', 'twilight': '1f307', 'sundown': '1f307',
            'earthquake': '1f30b', 'tremor': '1f30b', 'seismic': '1f30b', 'shake': '1f30b', 'fault': '1f30b',
            'volcano': '1f30b', 'eruption': '1f30b', 'lava': '1f30b', 'magma': '1f30b', 'ash': '1f30b',
            'tsunami': '1f30a', 'tidal wave': '1f30a', 'sea surge': '1f30a', 'disaster': '1f30a', 'coastal': '1f30a',
            'flood': '1f30a', 'flooding': '1f30a', 'inundation': '1f30a', 'deluge': '1f30a', 'overflow': '1f30a',
            'drought': '1f3dc', 'dry': '1f3dc', 'arid': '1f3dc', 'parched': '1f3dc', 'waterless': '1f3dc',
            'avalanche': '1f3d4', 'snowslide': '1f3d4', 'landslide': '1f3d4', 'collapse': '1f3d4', 'mountain': '1f3d4',
            'wildfire': '1f525', 'forest fire': '1f525', 'bushfire': '1f525', 'blaze': '1f525', 'inferno': '1f525',
            'aurora': '1f30a', 'northern lights': '1f30a', 'aurora borealis': '1f30a', 'polar': '1f30a',
            'night sky': '1f30a',

            # Clothing and Fashion Items
            'shirt': '1f455', 't-shirt': '1f455', 'top': '1f455', 'blouse': '1f455', 'button-up': '1f455',
            'pants': '1f456', 'trousers': '1f456', 'jeans': '1f456', 'slacks': '1f456', 'leggings': '1f456',
            'dress': '1f457', 'gown': '1f457', 'frock': '1f457', 'formal': '1f457', 'cocktail dress': '1f457',
            'skirt': '1f457', 'mini skirt': '1f457', 'maxi skirt': '1f457', 'pleated': '1f457', 'a-line': '1f457',
            'jacket': '1f9e5', 'coat': '1f9e5', 'blazer': '1f9e5', 'outerwear': '1f9e5', 'bomber': '1f9e5',
            'sweater': '1f9e6', 'pullover': '1f9e6', 'jumper': '1f9e6', 'cardigan': '1f9e6', 'hoodie': '1f9e6',
            'hat': '1f452', 'cap': '1f452', 'beanie': '1f452', 'fedora': '1f452', 'headwear': '1f452',
            'shoes': '1f45e', 'footwear': '1f45e', 'sneakers': '1f45e', 'trainers': '1f45e', 'loafers': '1f45e',
            'boots': '1f97e', 'ankle boots': '1f97e', 'winter boots': '1f97e', 'combat boots': '1f97e',
            'hiking boots': '1f97e',
            'heels': '1f460', 'high heels': '1f460', 'pumps': '1f460', 'stilettos': '1f460', 'platforms': '1f460',
            'sandals': '1f461', 'flip flops': '1f461', 'slides': '1f461', 'open toe': '1f461', 'summer shoes': '1f461',
            'socks': '1f9e6', 'stockings': '1f9e6', 'hosiery': '1f9e6', 'ankle socks': '1f9e6', 'crew socks': '1f9e6',
            'underwear': '1f45b', 'briefs': '1f45b', 'boxers': '1f45b', 'panties': '1f45b', 'undergarments': '1f45b',
            'bra': '1f459', 'brassiere': '1f459', 'sports bra': '1f459', 'lingerie': '1f459', 'undergarment': '1f459',
            'swimsuit': '1fa81', 'bikini': '1fa81', 'swimming trunks': '1fa81', 'bathing suit': '1fa81',
            'one-piece': '1fa81',
            'scarf': '1f9e3', 'muffler': '1f9e3', 'wrap': '1f9e3', 'shawl': '1f9e3', 'neck wear': '1f9e3',
            'gloves': '1f9e4', 'mittens': '1f9e4', 'hand wear': '1f9e4', 'winter': '1f9e4', 'protection': '1f9e4',
            'tie': '1f454', 'necktie': '1f454', 'bow tie': '1f454', 'formal wear': '1f454', 'business': '1f454',
            'belt': '1f94e', 'waistband': '1f94e', 'strap': '1f94e', 'leather belt': '1f94e', 'accessory': '1f94e',
            'jewelry': '1f48d', 'necklace': '1f48d', 'bracelet': '1f48d', 'earrings': '1f48d', 'accessories': '1f48d',

            # Household Items and Furniture
            'sofa': '1f6cb', 'couch': '1f6cb', 'loveseat': '1f6cb', 'settee': '1f6cb', 'chesterfield': '1f6cb',
            'chair': '1fa91', 'seat': '1fa91', 'armchair': '1fa91', 'recliner': '1fa91', 'stool': '1fa91',
            'table': '1f6a2', 'desk': '1f6a2', 'dining table': '1f6a2', 'coffee table': '1f6a2', 'end table': '1f6a2',
            'bed': '1f6cf', 'mattress': '1f6cf', 'bunk bed': '1f6cf', 'king size': '1f6cf', 'queen size': '1f6cf',
            'dresser': '1f5c4', 'chest of drawers': '1f5c4', 'bureau': '1f5c4', 'wardrobe': '1f5c4', 'armoire': '1f5c4',
            'bookshelf': '1f4da', 'bookcase': '1f4da', 'shelving': '1f4da', 'library': '1f4da', 'storage': '1f4da',
            'cabinet': '1f5c4', 'cupboard': '1f5c4', 'hutch': '1f5c4', 'sideboard': '1f5c4', 'credenza': '1f5c4',
            'lamp': '1f4a1', 'light': '1f4a1', 'floor lamp': '1f4a1', 'table lamp': '1f4a1', 'lighting': '1f4a1',
            'mirror': '1fa9e', 'looking glass': '1fa9e', 'reflection': '1fa9e', 'vanity': '1fa9e',
            'wall mirror': '1fa9e',
            'clock': '1f550', 'timepiece': '1f550', 'wall clock': '1f550', 'alarm clock': '1f550',
            'grandfather clock': '1f550',
            'rug': '1f9f1', 'carpet': '1f9f1', 'area rug': '1f9f1', 'floor covering': '1f9f1', 'mat': '1f9f1',
            'curtains': '1fa9f', 'drapes': '1fa9f', 'blinds': '1fa9f', 'shades': '1fa9f', 'window treatment': '1fa9f',
            'pillow': '1f6cf', 'cushion': '1f6cf', 'throw pillow': '1f6cf', 'bolster': '1f6cf', 'headrest': '1f6cf',
            'blanket': '1f9f6', 'throw': '1f9f6', 'comforter': '1f9f6', 'duvet': '1f9f6', 'quilt': '1f9f6',
            'refrigerator': '1f9ca', 'fridge': '1f9ca', 'freezer': '1f9ca', 'cooler': '1f9ca', 'icebox': '1f9ca',
            'stove': '1f373', 'oven': '1f373', 'range': '1f373', 'cooktop': '1f373', 'burner': '1f373',
            'microwave': '1f373', 'microwave oven': '1f373', 'nuker': '1f373', 'appliance': '1f373', 'kitchen': '1f373',
            'dishwasher': '1f9fa', 'dish machine': '1f9fa', 'appliance': '1f9fa', 'kitchen': '1f9fa',
            'cleaning': '1f9fa',
            'washing machine': '1f9fa', 'washer': '1f9fa', 'laundry': '1f9fa', 'appliance': '1f9fa',
            'cleaning': '1f9fa',
            'vacuum': '1f9f9', 'vacuum cleaner': '1f9f9', 'hoover': '1f9f9', 'sweeper': '1f9f9', 'cleaning': '1f9f9',

            # Professional Titles and Occupations
            'doctor': '1f468-200d-2695-fe0f', 'physician': '1f468-200d-2695-fe0f', 'md': '1f468-200d-2695-fe0f',
            'surgeon': '1f468-200d-2695-fe0f', 'medical': '1f468-200d-2695-fe0f',
            'nurse': '1f469-200d-2695-fe0f', 'rn': '1f469-200d-2695-fe0f', 'caregiver': '1f469-200d-2695-fe0f',
            'healthcare': '1f469-200d-2695-fe0f', 'medical': '1f469-200d-2695-fe0f',
            'teacher': '1f469-200d-1f3eb', 'educator': '1f469-200d-1f3eb', 'professor': '1f469-200d-1f3eb',
            'instructor': '1f469-200d-1f3eb', 'tutor': '1f469-200d-1f3eb',
            'engineer': '1f468-200d-1f527', 'technician': '1f468-200d-1f527', 'mechanic': '1f468-200d-1f527',
            'builder': '1f468-200d-1f527', 'technical': '1f468-200d-1f527',
            'programmer': '1f468-200d-1f4bb', 'developer': '1f468-200d-1f4bb', 'coder': '1f468-200d-1f4bb',
            'software': '1f468-200d-1f4bb', 'it': '1f468-200d-1f4bb',
            'lawyer': '1f468-200d-2696-fe0f', 'attorney': '1f468-200d-2696-fe0f', 'advocate': '1f468-200d-2696-fe0f',
            'counsel': '1f468-200d-2696-fe0f', 'legal': '1f468-200d-2696-fe0f',
            'chef': '1f468-200d-1f373', 'cook': '1f468-200d-1f373', 'culinary': '1f468-200d-1f373',
            'baker': '1f468-200d-1f373', 'kitchen': '1f468-200d-1f373',
            'police': '1f46e', 'officer': '1f46e', 'cop': '1f46e', 'detective': '1f46e', 'law enforcement': '1f46e',
            'firefighter': '1f468-200d-1f692', 'fireman': '1f468-200d-1f692', 'fire fighter': '1f468-200d-1f692',
            'rescue': '1f468-200d-1f692', 'emergency': '1f468-200d-1f692',
            'scientist': '1f468-200d-1f52c', 'researcher': '1f468-200d-1f52c', 'chemist': '1f468-200d-1f52c',
            'biologist': '1f468-200d-1f52c', 'physicist': '1f468-200d-1f52c',
            'artist': '1f468-200d-1f3a8', 'painter': '1f468-200d-1f3a8', 'creative': '1f468-200d-1f3a8',
            'designer': '1f468-200d-1f3a8', 'illustrator': '1f468-200d-1f3a8',
            'musician': '1f468-200d-1f3a4', 'singer': '1f468-200d-1f3a4', 'performer': '1f468-200d-1f3a4',
            'composer': '1f468-200d-1f3a4', 'band': '1f468-200d-1f3a4',
            'actor': '1f468-200d-1f3ad', 'actress': '1f469-200d-1f3ad', 'performer': '1f468-200d-1f3ad',
            'star': '1f468-200d-1f3ad', 'celebrity': '1f468-200d-1f3ad',
            'writer': '1f468-200d-1f4dd', 'author': '1f468-200d-1f4dd', 'journalist': '1f468-200d-1f4dd',
            'blogger': '1f468-200d-1f4dd', 'novelist': '1f468-200d-1f4dd',
            'pilot': '1f468-200d-2708-fe0f', 'aviator': '1f468-200d-2708-fe0f', 'captain': '1f468-200d-2708-fe0f',
            'flight': '1f468-200d-2708-fe0f', 'airline': '1f468-200d-2708-fe0f',
            'farmer': '1f468-200d-1f33e', 'agriculture': '1f468-200d-1f33e', 'grower': '1f468-200d-1f33e',
            'rancher': '1f468-200d-1f33e', 'cultivator': '1f468-200d-1f33e',
            'accountant': '1f468-200d-1f4c8', 'cpa': '1f468-200d-1f4c8', 'bookkeeper': '1f468-200d-1f4c8',
            'financial': '1f468-200d-1f4c8', 'auditor': '1f468-200d-1f4c8',
            'dentist': '1f468-200d-1f9b7', 'orthodontist': '1f468-200d-1f9b7', 'dental': '1f468-200d-1f9b7',
            'oral': '1f468-200d-1f9b7', 'teeth': '1f468-200d-1f9b7',
            'veterinarian': '1f468-200d-1f408', 'vet': '1f468-200d-1f408', 'animal doctor': '1f468-200d-1f408',
            'pet doctor': '1f468-200d-1f408', 'animal care': '1f468-200d-1f408',

            # Musical Instruments
            'guitar': '1f3b8', 'acoustic guitar': '1f3b8', 'electric guitar': '1f3b8', 'bass guitar': '1f3b8',
            'strings': '1f3b8',
            'piano': '1f3b9', 'keyboard': '1f3b9', 'grand piano': '1f3b9', 'upright piano': '1f3b9', 'keys': '1f3b9',
            'violin': '1f3bb', 'viola': '1f3bb', 'cello': '1f3bb', 'fiddle': '1f3bb', 'string instrument': '1f3bb',
            'trumpet': '1f3ba', 'horn': '1f3ba', 'brass': '1f3ba', 'bugle': '1f3ba', 'cornet': '1f3ba',
            'saxophone': '1f3b7', 'sax': '1f3b7', 'jazz': '1f3b7', 'brass': '1f3b7', 'woodwind': '1f3b7',
            'flute': '1f3b5', 'piccolo': '1f3b5', 'recorder': '1f3b5', 'woodwind': '1f3b5', 'wind instrument': '1f3b5',
            'clarinet': '1f3b5', 'oboe': '1f3b5', 'bassoon': '1f3b5', 'reed': '1f3b5', 'woodwind': '1f3b5',
            'drums': '1f941', 'percussion': '1f941', 'drumset': '1f941', 'snare': '1f941', 'cymbals': '1f941',
            'harp': '1f3bb', 'lyre': '1f3bb', 'strings': '1f3bb', 'classical': '1f3bb', 'pluck': '1f3bb',
            'banjo': '1f3b8', 'mandolin': '1f3b8', 'ukulele': '1f3b8', 'folk': '1f3b8', 'bluegrass': '1f3b8',
            'accordion': '1f3b7', 'concertina': '1f3b7', 'squeeze box': '1f3b7', 'polka': '1f3b7', 'folk': '1f3b7',
            'harmonica': '1f3b5', 'mouth organ': '1f3b5', 'blues harp': '1f3b5', 'blues': '1f3b5', 'folk': '1f3b5',
            'bagpipes': '1f3b5', 'scottish': '1f3b5', 'pipes': '1f3b5', 'celtic': '1f3b5', 'traditional': '1f3b5',
            'maracas': '1f941', 'shakers': '1f941', 'percussion': '1f941', 'rhythm': '1f941', 'latin': '1f941',
            'tambourine': '1f941', 'percussion': '1f941', 'rhythm': '1f941', 'jingle': '1f941', 'hand drum': '1f941',
            'xylophone': '1f3b9', 'glockenspiel': '1f3b9', 'marimba': '1f3b9', 'vibraphone': '1f3b9',
            'percussion': '1f3b9',
            'synthesizer': '1f3b9', 'synth': '1f3b9', 'electronic': '1f3b9', 'digital': '1f3b9', 'keyboard': '1f3b9',
            'turntable': '1f3b6', 'dj': '1f3b6', 'record player': '1f3b6', 'vinyl': '1f3b6', 'mixing': '1f3b6',
            'microphone': '1f3a4', 'mic': '1f3a4', 'recording': '1f3a4', 'vocal': '1f3a4', 'singing': '1f3a4',
            'headphones': '1f3a7', 'earphones': '1f3a7', 'audio': '1f3a7', 'listening': '1f3a7', 'sound': '1f3a7',

            # Science and Academic Fields
            'physics': '269b', 'quantum': '269b', 'mechanics': '269b', 'relativity': '269b', 'thermodynamics': '269b',
            'chemistry': '2697', 'molecules': '2697', 'elements': '2697', 'compounds': '2697', 'reactions': '2697',
            'biology': '1f9ec', 'life science': '1f9ec', 'organisms': '1f9ec', 'cells': '1f9ec', 'genetics': '1f9ec',
            'astronomy': '1f30c', 'cosmos': '1f30c', 'stars': '1f30c', 'planets': '1f30c', 'galaxies': '1f30c',
            'geology': '1f30b', 'earth science': '1f30b', 'rocks': '1f30b', 'minerals': '1f30b', 'tectonics': '1f30b',
            'mathematics': '1f522', 'algebra': '1f522', 'calculus': '1f522', 'geometry': '1f522', 'statistics': '1f522',
            'computer science': '1f4bb', 'programming': '1f4bb', 'algorithms': '1f4bb', 'data structures': '1f4bb',
            'coding': '1f4bb',
            'psychology': '1f9e0', 'behavior': '1f9e0', 'cognition': '1f9e0', 'mental': '1f9e0', 'mind': '1f9e0',
            'sociology': '1f465', 'society': '1f465', 'social': '1f465', 'groups': '1f465', 'communities': '1f465',
            'anthropology': '1f3fb', 'culture': '1f3fb', 'human evolution': '1f3fb', 'archaeology': '1f3fb',
            'ethnography': '1f3fb',
            'economics': '1f4b8', 'markets': '1f4b8', 'finance': '1f4b8', 'trade': '1f4b8', 'business': '1f4b8',
            'political science': '1f5fa', 'government': '1f5fa', 'politics': '1f5fa', 'policy': '1f5fa',
            'international relations': '1f5fa',
            'history': '1f4dc', 'past': '1f4dc', 'civilization': '1f4dc', 'events': '1f4dc', 'chronology': '1f4dc',
            'linguistics': '1f4ac', 'language': '1f4ac', 'grammar': '1f4ac', 'phonetics': '1f4ac', 'semantics': '1f4ac',
            'philosophy': '1f914', 'ethics': '1f914', 'logic': '1f914', 'metaphysics': '1f914', 'epistemology': '1f914',
            'literature': '1f4da', 'fiction': '1f4da', 'poetry': '1f4da', 'drama': '1f4da', 'prose': '1f4da',
            'engineering': '1f527', 'mechanical': '1f527', 'electrical': '1f527', 'civil': '1f527', 'chemical': '1f527',
            'medicine': '1fa7a', 'healthcare': '1fa7a', 'clinical': '1fa7a', 'diagnosis': '1fa7a', 'treatment': '1fa7a',
            'environmental science': '1f331', 'ecology': '1f331', 'conservation': '1f331', 'sustainability': '1f331',
            'climate': '1f331',
            'neuroscience': '1f9e0', 'brain': '1f9e0', 'neural': '1f9e0', 'cognitive': '1f9e0',
            'nervous system': '1f9e0',

            # Holidays and Celebrations
            'christmas': '1f384', 'xmas': '1f384', 'december 25': '1f384', 'santa': '1f384', 'noel': '1f384',
            'halloween': '1f383', 'october 31': '1f383', 'spooky': '1f383', 'costume': '1f383',
            'trick or treat': '1f383',
            'thanksgiving': '1f983', 'turkey day': '1f983', 'november': '1f983', 'harvest': '1f983',
            'gratitude': '1f983',
            'new year': '1f386', 'january 1': '1f386', 'resolution': '1f386', 'countdown': '1f386',
            'celebration': '1f386',
            'easter': '1f430', 'bunny': '1f430', 'eggs': '1f430', 'spring': '1f430', 'resurrection': '1f430',
            'valentine': '1f496', 'february 14': '1f496', 'love': '1f496', 'romance': '1f496', 'hearts': '1f496',
            'independence day': '1f386', 'july 4': '1f386', 'fourth of july': '1f386', 'fireworks': '1f386',
            'patriotic': '1f386',
            'birthday': '1f382', 'cake': '1f382', 'candles': '1f382', 'party': '1f382', 'celebration': '1f382',
            'wedding': '1f492', 'marriage': '1f492', 'bride': '1f492', 'groom': '1f492', 'ceremony': '1f492',
            'graduation': '1f393', 'commencement': '1f393', 'diploma': '1f393', 'academic': '1f393',
            'achievement': '1f393',
            'anniversary': '1f48d', 'yearly': '1f48d', 'celebration': '1f48d', 'milestone': '1f48d',
            'commemoration': '1f48d',
            'hanukkah': '1f54e', 'chanukah': '1f54e', 'menorah': '1f54e', 'festival of lights': '1f54e',
            'jewish': '1f54e',
            'diwali': '1f386', 'deepavali': '1f386', 'festival of lights': '1f386', 'hindu': '1f386', 'lamps': '1f386',
            'ramadan': '1f319', 'eid': '1f319', 'fasting': '1f319', 'islamic': '1f319', 'muslim': '1f319',
            'chinese new year': '1f42d', 'lunar new year': '1f42d', 'spring festival': '1f42d',
            'red envelopes': '1f42d', 'zodiac': '1f42d',
            'kwanzaa': '1f3b9', 'african american': '1f3b9', 'unity': '1f3b9', 'heritage': '1f3b9',
            'celebration': '1f3b9',
            'st patrick': '1f340', 'march 17': '1f340', 'irish': '1f340', 'green': '1f340', 'shamrock': '1f340',
            'cinco de mayo': '1f1f2-1f1fd', 'may 5': '1f1f2-1f1fd', 'mexican': '1f1f2-1f1fd',
            'celebration': '1f1f2-1f1fd', 'heritage': '1f1f2-1f1fd',
            'mothers day': '1f490', 'moms': '1f490', 'maternal': '1f490', 'may': '1f490', 'flowers': '1f490',
            'fathers day': '1f454', 'dads': '1f454', 'paternal': '1f454', 'june': '1f454', 'tie': '1f454',

            # Financial Terms and Concepts
            'money': '1f4b5', 'cash': '1f4b5', 'currency': '1f4b5', 'dollars': '1f4b5', 'funds': '1f4b5',
            'bank': '1f3e6', 'banking': '1f3e6', 'financial institution': '1f3e6', 'savings': '1f3e6',
            'checking': '1f3e6',
            'credit card': '1f4b3', 'debit card': '1f4b3', 'plastic': '1f4b3', 'payment card': '1f4b3',
            'charge': '1f4b3',
            'loan': '1f4b8', 'borrow': '1f4b8', 'debt': '1f4b8', 'financing': '1f4b8', 'credit': '1f4b8',
            'mortgage': '1f3e0', 'home loan': '1f3e0', 'property financing': '1f3e0', 'real estate loan': '1f3e0',
            'house payment': '1f3e0',
            'investment': '1f4c8', 'stocks': '1f4c8', 'bonds': '1f4c8', 'portfolio': '1f4c8', 'securities': '1f4c8',
            'stock market': '1f4c8', 'wall street': '1f4c8', 'exchange': '1f4c8', 'trading': '1f4c8', 'shares': '1f4c8',
            'bitcoin': '1f4b0', 'cryptocurrency': '1f4b0', 'crypto': '1f4b0', 'digital currency': '1f4b0',
            'blockchain': '1f4b0',
            'budget': '1f4b2', 'financial plan': '1f4b2', 'spending plan': '1f4b2', 'income allocation': '1f4b2',
            'expense tracking': '1f4b2',
            'savings': '1f4b0', 'emergency fund': '1f4b0', 'rainy day': '1f4b0', 'nest egg': '1f4b0',
            'reserve': '1f4b0',
            'retirement': '1f3dd', '401k': '1f3dd', 'ira': '1f3dd', 'pension': '1f3dd', 'golden years': '1f3dd',
            'tax': '1f4b2', 'taxation': '1f4b2', 'irs': '1f4b2', 'revenue': '1f4b2', 'duty': '1f4b2',
            'insurance': '1f6e1', 'coverage': '1f6e1', 'policy': '1f6e1', 'protection': '1f6e1',
            'risk management': '1f6e1',
            'interest': '1f4b8', 'apr': '1f4b8', 'rate': '1f4b8', 'yield': '1f4b8', 'return': '1f4b8',
            'inflation': '1f4c9', 'rising prices': '1f4c9', 'cost increase': '1f4c9', 'purchasing power': '1f4c9',
            'devaluation': '1f4c9',
            'recession': '1f4c9', 'economic downturn': '1f4c9', 'contraction': '1f4c9', 'slump': '1f4c9',
            'depression': '1f4c9',
            'dividend': '1f4b0', 'payout': '1f4b0', 'distribution': '1f4b0', 'shareholder return': '1f4b0',
            'profit sharing': '1f4b0',
            'capital': '1f4b0', 'assets': '1f4b0', 'wealth': '1f4b0', 'net worth': '1f4b0', 'resources': '1f4b0',
            'equity': '1f4b0', 'ownership': '1f4b0', 'stake': '1f4b0', 'share': '1f4b0', 'investment': '1f4b0',
            'liability': '1f4b8', 'debt': '1f4b8', 'obligation': '1f4b8', 'financial burden': '1f4b8',
            'responsibility': '1f4b8',

            # Medical Terms and Health Conditions
            'doctor': '1f468-200d-2695-fe0f', 'physician': '1f468-200d-2695-fe0f', 'medical': '1f468-200d-2695-fe0f',
            'healthcare': '1f468-200d-2695-fe0f', 'practitioner': '1f468-200d-2695-fe0f',
            'nurse': '1f469-200d-2695-fe0f', 'nursing': '1f469-200d-2695-fe0f', 'caregiver': '1f469-200d-2695-fe0f',
            'healthcare': '1f469-200d-2695-fe0f', 'medical': '1f469-200d-2695-fe0f',
            'hospital': '1f3e5', 'medical center': '1f3e5', 'clinic': '1f3e5', 'emergency room': '1f3e5',
            'healthcare facility': '1f3e5',
            'ambulance': '1f691', 'emergency': '1f691', 'paramedic': '1f691', 'ems': '1f691',
            'medical transport': '1f691',
            'medicine': '1f48a', 'medication': '1f48a', 'pill': '1f48a', 'drug': '1f48a', 'pharmaceutical': '1f48a',
            'prescription': '1f4c3', 'rx': '1f4c3', 'script': '1f4c3', 'doctor order': '1f4c3',
            'medication order': '1f4c3',
            'surgery': '1f52c', 'operation': '1f52c', 'procedure': '1f52c', 'surgical': '1f52c', 'incision': '1f52c',
            'vaccine': '1f489', 'immunization': '1f489', 'shot': '1f489', 'inoculation': '1f489', 'prevention': '1f489',
            'virus': '1f9a0', 'infection': '1f9a0', 'pathogen': '1f9a0', 'microbe': '1f9a0', 'contagion': '1f9a0',
            'bacteria': '1f9a0', 'microorganism': '1f9a0', 'germ': '1f9a0', 'microbe': '1f9a0', 'infection': '1f9a0',
            'cancer': '1f3e5', 'tumor': '1f3e5', 'oncology': '1f3e5', 'malignancy': '1f3e5', 'carcinoma': '1f3e5',
            'heart disease': '1fa78', 'cardiac': '1fa78', 'cardiovascular': '1fa78', 'coronary': '1fa78',
            'heart attack': '1fa78',
            'diabetes': '1f36c', 'blood sugar': '1f36c', 'insulin': '1f36c', 'glucose': '1f36c', 'endocrine': '1f36c',
            'asthma': '1f4a8', 'respiratory': '1f4a8', 'breathing': '1f4a8', 'inhaler': '1f4a8', 'lungs': '1f4a8',
            'allergy': '1f927', 'allergic': '1f927', 'hypersensitivity': '1f927', 'reaction': '1f927',
            'histamine': '1f927',
            'depression': '1f614', 'mental health': '1f614', 'mood disorder': '1f614', 'psychological': '1f614',
            'emotional': '1f614',
            'anxiety': '1f630', 'stress': '1f630', 'worry': '1f630', 'panic': '1f630', 'nervousness': '1f630',
            'alzheimer': '1f9e0', 'dementia': '1f9e0', 'memory loss': '1f9e0', 'cognitive decline': '1f9e0',
            'neurological': '1f9e0',
            'arthritis': '1f9b4', 'joint pain': '1f9b4', 'inflammation': '1f9b4', 'rheumatism': '1f9b4',
            'stiffness': '1f9b4',
            'hypertension': '1f4a8', 'high blood pressure': '1f4a8', 'elevated bp': '1f4a8', 'cardiovascular': '1f4a8',
            'pressure': '1f4a8',

            # Art and Creative Fields
            'painting': '1f3a8', 'canvas': '1f3a8', 'artwork': '1f3a8', 'oil painting': '1f3a8', 'acrylic': '1f3a8',
            'drawing': '270f', 'sketch': '270f', 'illustration': '270f', 'pencil art': '270f', 'graphite': '270f',
            'sculpture': '1f5ff', 'statue': '1f5ff', 'carving': '1f5ff', '3d art': '1f5ff', 'modeling': '1f5ff',
            'photography': '1f4f7', 'camera': '1f4f7', 'photo': '1f4f7', 'picture': '1f4f7', 'snapshot': '1f4f7',
            'film': '1f3ac', 'movie': '1f3ac', 'cinema': '1f3ac', 'motion picture': '1f3ac', 'video': '1f3ac',
            'animation': '1f3ac', 'cartoon': '1f3ac', 'anime': '1f3ac', 'motion graphics': '1f3ac', 'cgi': '1f3ac',
            'graphic design': '1f3a8', 'visual design': '1f3a8', 'digital art': '1f3a8', 'layout': '1f3a8',
            'typography': '1f3a8',
            'fashion design': '1f457', 'clothing design': '1f457', 'apparel': '1f457', 'costume': '1f457',
            'style': '1f457',
            'architecture': '1f3db', 'building design': '1f3db', 'structural': '1f3db', 'blueprint': '1f3db',
            'construction plan': '1f3db',
            'interior design': '1f6cb', 'decor': '1f6cb', 'furnishing': '1f6cb', 'space planning': '1f6cb',
            'home design': '1f6cb',
            'literature': '1f4d6', 'writing': '1f4d6', 'fiction': '1f4d6', 'prose': '1f4d6', 'novel': '1f4d6',
            'poetry': '1f4d6', 'verse': '1f4d6', 'poem': '1f4d6', 'lyric': '1f4d6', 'stanza': '1f4d6',
            'music': '1f3b5', 'composition': '1f3b5', 'song': '1f3b5', 'melody': '1f3b5', 'harmony': '1f3b5',
            'dance': '1f483', 'choreography': '1f483', 'movement': '1f483', 'performance': '1f483', 'ballet': '1f483',
            'theater': '1f3ad', 'drama': '1f3ad', 'stage': '1f3ad', 'acting': '1f3ad', 'performance': '1f3ad',
            'comedy': '1f3ad', 'humor': '1f3ad', 'standup': '1f3ad', 'jokes': '1f3ad', 'funny': '1f3ad',
            'ceramics': '1f5ff', 'pottery': '1f5ff', 'clay': '1f5ff', 'earthenware': '1f5ff', 'porcelain': '1f5ff',
            'printmaking': '1f5b3', 'lithography': '1f5b3', 'etching': '1f5b3', 'screen printing': '1f5b3',
            'woodcut': '1f5b3',
            'calligraphy': '270d', 'handwriting': '270d', 'lettering': '270d', 'penmanship': '270d', 'script': '270d',
            'jewelry design': '1f48d', 'metalwork': '1f48d', 'goldsmith': '1f48d', 'silversmith': '1f48d',
            'accessories': '1f48d',

            # Plants and Flowers
            'tree': '1f333', 'oak': '1f333', 'pine': '1f333', 'maple': '1f333', 'evergreen': '1f333',
            'flower': '1f33c', 'bloom': '1f33c', 'blossom': '1f33c', 'petal': '1f33c', 'floral': '1f33c',
            'rose': '1f339', 'red flower': '1f339', 'romance': '1f339', 'thorns': '1f339', 'valentine': '1f339',
            'tulip': '1f337', 'spring flower': '1f337', 'bulb': '1f337', 'dutch': '1f337', 'garden': '1f337',
            'sunflower': '1f33b', 'helianthus': '1f33b', 'yellow flower': '1f33b', 'seeds': '1f33b',
            'tall flower': '1f33b',
            'daisy': '1f33c', 'simple flower': '1f33c', 'white petals': '1f33c', 'yellow center': '1f33c',
            'meadow': '1f33c',
            'lily': '1f33a', 'elegant flower': '1f33a', 'fragrant': '1f33a', 'white flower': '1f33a', 'easter': '1f33a',
            'orchid': '1f33a', 'exotic flower': '1f33a', 'tropical': '1f33a', 'delicate': '1f33a', 'rare': '1f33a',
            'cactus': '1f335', 'desert plant': '1f335', 'succulent': '1f335', 'spines': '1f335', 'arid': '1f335',
            'palm tree': '1f334', 'coconut': '1f334', 'tropical': '1f334', 'beach': '1f334', 'vacation': '1f334',
            'fern': '1f33f', 'frond': '1f33f', 'forest plant': '1f33f', 'shade plant': '1f33f', 'prehistoric': '1f33f',
            'vine': '1f33f', 'climbing plant': '1f33f', 'ivy': '1f33f', 'creeper': '1f33f', 'grape vine': '1f33f',
            'moss': '1f33f', 'green plant': '1f33f', 'damp': '1f33f', 'forest floor': '1f33f', 'soft': '1f33f',
            'grass': '1f33f', 'lawn': '1f33f', 'turf': '1f33f', 'meadow': '1f33f', 'pasture': '1f33f',
            'bamboo': '1f38d', 'tall grass': '1f38d', 'asian plant': '1f38d', 'panda food': '1f38d',
            'fast growing': '1f38d',
            'maple leaf': '1f341', 'autumn': '1f341', 'fall': '1f341', 'canada': '1f341', 'red leaf': '1f341',
            'herb': '1f33f', 'basil': '1f33f', 'mint': '1f33f', 'oregano': '1f33f', 'culinary': '1f33f',
            'mushroom': '1f344', 'fungus': '1f344', 'toadstool': '1f344', 'forest floor': '1f344', 'edible': '1f344',
            'bonsai': '1f333', 'miniature tree': '1f333', 'japanese': '1f333', 'potted': '1f333', 'pruned': '1f333',
            'houseplant': '1f331', 'indoor plant': '1f331', 'potted plant': '1f331', 'decor': '1f331',
            'greenery': '1f331',

            # Mythical Creatures and Fantasy Elements
            'dragon': '1f409', 'mythical': '1f409', 'fire breathing': '1f409', 'winged serpent': '1f409',
            'fantasy': '1f409',
            'unicorn': '1f984', 'magical horse': '1f984', 'horn': '1f984', 'fantasy': '1f984', 'mythical': '1f984',
            'mermaid': '1f9dc', 'merman': '1f9dc', 'sea creature': '1f9dc', 'half fish': '1f9dc', 'ocean myth': '1f9dc',
            'fairy': '1f9da', 'pixie': '1f9da', 'sprite': '1f9da', 'magical being': '1f9da', 'wings': '1f9da',
            'vampire': '1f9db', 'dracula': '1f9db', 'blood': '1f9db', 'fangs': '1f9db', 'undead': '1f9db',
            'zombie': '1f9df', 'undead': '1f9df', 'walking dead': '1f9df', 'brain eater': '1f9df', 'horror': '1f9df',
            'ghost': '1f47b', 'spirit': '1f47b', 'apparition': '1f47b', 'phantom': '1f47b', 'spooky': '1f47b',
            'alien': '1f47d', 'extraterrestrial': '1f47d', 'ufo': '1f47d', 'space creature': '1f47d',
            'martian': '1f47d',
            'monster': '1f47e', 'creature': '1f47e', 'beast': '1f47e', 'fiend': '1f47e', 'scary': '1f47e',
            'goblin': '1f47a', 'imp': '1f47a', 'gremlin': '1f47a', 'mischievous': '1f47a', 'evil': '1f47a',
            'wizard': '1f9d9', 'sorcerer': '1f9d9', 'magic user': '1f9d9', 'spellcaster': '1f9d9', 'mage': '1f9d9',
            'witch': '1f9d9', 'sorceress': '1f9d9', 'magic user': '1f9d9', 'spellcaster': '1f9d9',
            'enchantress': '1f9d9',
            'elf': '1f9dd', 'magical being': '1f9dd', 'pointed ears': '1f9dd', 'fantasy race': '1f9dd',
            'woodland': '1f9dd',
            'genie': '1f9de', 'djinn': '1f9de', 'wish granter': '1f9de', 'magical being': '1f9de', 'lamp': '1f9de',
            'phoenix': '1f426', 'firebird': '1f426', 'rebirth': '1f426', 'flames': '1f426', 'immortal': '1f426',
            'griffin': '1f985', 'gryphon': '1f985', 'eagle lion': '1f985', 'mythical beast': '1f985',
            'heraldic': '1f985',
            'centaur': '1f3c7', 'half human half horse': '1f3c7', 'mythical': '1f3c7', 'greek': '1f3c7',
            'hybrid': '1f3c7',
            'minotaur': '1f402', 'bull man': '1f402', 'labyrinth': '1f402', 'greek myth': '1f402', 'crete': '1f402',
            'pegasus': '1f40e', 'winged horse': '1f40e', 'flying steed': '1f40e', 'mythical': '1f40e',
            'magical': '1f40e',
            'kraken': '1f991', 'sea monster': '1f991', 'giant squid': '1f991', 'ocean beast': '1f991',
            'tentacles': '1f991',

            # Celestial Objects and Space
            'sun': '2600', 'star': '2600', 'solar': '2600', 'daylight': '2600', 'sunshine': '2600',
            'moon': '1f319', 'lunar': '1f319', 'crescent': '1f319', 'night sky': '1f319', 'satellite': '1f319',
            'star': '2b50', 'celestial body': '2b50', 'twinkling': '2b50', 'night sky': '2b50', 'stellar': '2b50',
            'planet': '1fa90', 'world': '1fa90', 'celestial body': '1fa90', 'orbit': '1fa90', 'solar system': '1fa90',
            'earth': '1f30d', 'globe': '1f30d', 'world': '1f30d', 'planet': '1f30d', 'blue planet': '1f30d',
            'mars': '1fa90', 'red planet': '1fa90', 'fourth planet': '1fa90', 'martian': '1fa90', 'rover': '1fa90',
            'jupiter': '1fa90', 'gas giant': '1fa90', 'largest planet': '1fa90', 'great red spot': '1fa90',
            'fifth planet': '1fa90',
            'saturn': '1fa90', 'rings': '1fa90', 'sixth planet': '1fa90', 'gas giant': '1fa90', 'titan': '1fa90',
            'comet': '2604', 'icy body': '2604', 'tail': '2604', 'celestial': '2604', 'halley': '2604',
            'asteroid': '1fa90', 'space rock': '1fa90', 'meteor': '1fa90', 'belt': '1fa90', 'impact': '1fa90',
            'meteor': '1f320', 'shooting star': '1f320', 'falling star': '1f320', 'wish': '1f320', 'streak': '1f320',
            'galaxy': '1f30c', 'milky way': '1f30c', 'stars': '1f30c', 'universe': '1f30c', 'cosmic': '1f30c',
            'black hole': '26ab', 'gravity well': '26ab', 'singularity': '26ab', 'space time': '26ab',
            'event horizon': '26ab',
            'nebula': '1f30c', 'star cloud': '1f30c', 'cosmic dust': '1f30c', 'stellar nursery': '1f30c',
            'space cloud': '1f30c',
            'supernova': '1f4a5', 'star explosion': '1f4a5', 'stellar death': '1f4a5', 'cosmic blast': '1f4a5',
            'bright': '1f4a5',
            'constellation': '2b50', 'star pattern': '2b50', 'zodiac': '2b50', 'night sky': '2b50',
            'celestial group': '2b50',
            'satellite': '1f6f0', 'orbiter': '1f6f0', 'space tech': '1f6f0', 'communications': '1f6f0',
            'artificial': '1f6f0',
            'space station': '1f6f0', 'iss': '1f6f0', 'orbital platform': '1f6f0', 'habitat': '1f6f0',
            'research': '1f6f0',
            'telescope': '1f52d', 'observatory': '1f52d', 'stargazing': '1f52d', 'astronomy': '1f52d', 'lens': '1f52d',
            'astronaut': '1f680', 'spaceman': '1f680', 'cosmonaut': '1f680', 'space traveler': '1f680',
            'explorer': '1f680',

            # Geological Formations
            'mountain': '26f0', 'peak': '26f0', 'summit': '26f0', 'elevation': '26f0', 'highland': '26f0',
            'volcano': '1f30b', 'eruption': '1f30b', 'lava': '1f30b', 'magma': '1f30b', 'crater': '1f30b',
            'canyon': '1f3de', 'gorge': '1f3de', 'ravine': '1f3de', 'valley': '1f3de', 'chasm': '1f3de',
            'cave': '1f573', 'cavern': '1f573', 'grotto': '1f573', 'underground': '1f573', 'hollow': '1f573',
            'cliff': '1f5fb', 'bluff': '1f5fb', 'precipice': '1f5fb', 'escarpment': '1f5fb', 'drop': '1f5fb',
            'glacier': '1f9ca', 'ice sheet': '1f9ca', 'frozen river': '1f9ca', 'ice flow': '1f9ca', 'alpine': '1f9ca',
            'desert': '1f3dc', 'sand': '1f3dc', 'arid': '1f3dc', 'dune': '1f3dc', 'barren': '1f3dc',
            'oasis': '1f334', 'water source': '1f334', 'desert spring': '1f334', 'palm trees': '1f334',
            'refuge': '1f334',
            'plateau': '1f3de', 'mesa': '1f3de', 'tableland': '1f3de', 'elevated plain': '1f3de', 'flat top': '1f3de',
            'valley': '1f3de', 'dale': '1f3de', 'glen': '1f3de', 'vale': '1f3de', 'depression': '1f3de',
            'geyser': '1f30a', 'hot spring': '1f30a', 'thermal vent': '1f30a', 'eruption': '1f30a',
            'yellowstone': '1f30a',
            'waterfall': '1f30a', 'cascade': '1f30a', 'falls': '1f30a', 'cataract': '1f30a', 'plunge': '1f30a',
            'reef': '1f41f', 'coral': '1f41f', 'atoll': '1f41f', 'barrier': '1f41f', 'marine': '1f41f',
            'island': '1f3dd', 'isle': '1f3dd', 'atoll': '1f3dd', 'landmass': '1f3dd', 'isolated land': '1f3dd',
            'peninsula': '1f30d', 'cape': '1f30d', 'promontory': '1f30d', 'headland': '1f30d',
            'land extension': '1f30d',
            'isthmus': '1f30d', 'land bridge': '1f30d', 'narrow connection': '1f30d', 'panama': '1f30d',
            'geography': '1f30d',
            'fjord': '1f30a', 'inlet': '1f30a', 'norwegian': '1f30a', 'sea valley': '1f30a', 'glacial': '1f30a',
            'delta': '1f30a', 'river mouth': '1f30a', 'sediment': '1f30a', 'alluvial': '1f30a', 'nile': '1f30a',
            'basin': '1f30a', 'watershed': '1f30a', 'drainage': '1f30a', 'catchment': '1f30a', 'depression': '1f30a',
            'butte': '1f3de', 'mesa': '1f3de', 'flat top': '1f3de', 'isolated': '1f3de', 'erosion': '1f3de',

            # More Sports and Activities
            'archery': '1f3f9', 'bow and arrow': '1f3f9', 'target': '1f3f9', 'bullseye': '1f3f9', 'precision': '1f3f9',
            'fencing': '1f93a', 'sword fighting': '1f93a', 'foil': '1f93a', 'epee': '1f93a', 'saber': '1f93a',
            'skateboarding': '1f6f9', 'skater': '1f6f9', 'board': '1f6f9', 'trick': '1f6f9', 'halfpipe': '1f6f9',
            'snowboarding': '1f3c2', 'winter sport': '1f3c2', 'board': '1f3c2', 'snow': '1f3c2', 'mountain': '1f3c2',
            'gymnastics': '1f938', 'acrobatics': '1f938', 'balance beam': '1f938', 'vault': '1f938',
            'floor exercise': '1f938',
            'weightlifting': '1f3cb', 'strength': '1f3cb', 'barbell': '1f3cb', 'powerlifting': '1f3cb',
            'olympic': '1f3cb',
            'rowing': '1f6a3', 'crew': '1f6a3', 'sculling': '1f6a3', 'oar': '1f6a3', 'regatta': '1f6a3',
            'sailing': '26f5', 'boating': '26f5', 'yacht': '26f5', 'regatta': '26f5', 'nautical': '26f5',
            'canoeing': '1f6f6', 'kayaking': '1f6f6', 'paddle': '1f6f6', 'water sport': '1f6f6', 'river': '1f6f6',
            'water polo': '1f3d0', 'pool': '1f3d0', 'swimming': '1f3d0', 'team sport': '1f3d0', 'ball': '1f3d0',
            'diving': '1f3ca', 'springboard': '1f3ca', 'platform': '1f3ca', 'plunge': '1f3ca', 'underwater': '1f3ca',
            'equestrian': '1f3c7', 'horseback': '1f3c7', 'riding': '1f3c7', 'dressage': '1f3c7',
            'show jumping': '1f3c7',
            'lacrosse': '1f94d', 'stick': '1f94d', 'team sport': '1f94d', 'native american': '1f94d', 'ball': '1f94d',
            'handball': '1f3d0', 'team sport': '1f3d0', 'indoor': '1f3d0', 'olympic': '1f3d0', 'ball': '1f3d0',
            'squash': '1f3d3', 'racquet': '1f3d3', 'court': '1f3d3', 'indoor': '1f3d3', 'ball': '1f3d3',
            'racquetball': '1f3d3', 'indoor': '1f3d3', 'court': '1f3d3', 'racquet': '1f3d3', 'ball': '1f3d3',
            'curling': '1f94c', 'ice': '1f94c', 'stone': '1f94c', 'broom': '1f94c', 'winter sport': '1f94c',
            'bobsled': '1f3bf', 'bobsleigh': '1f3bf', 'winter sport': '1f3bf', 'ice': '1f3bf', 'sled': '1f3bf',
            'luge': '1f3bf', 'sled': '1f3bf', 'winter sport': '1f3bf', 'ice': '1f3bf', 'olympic': '1f3bf',
            'skeleton': '1f3bf', 'sled': '1f3bf', 'winter sport': '1f3bf', 'ice': '1f3bf', 'headfirst': '1f3bf',

            # More Technology Terms
            'algorithm': '1f4bb', 'code': '1f4bb', 'procedure': '1f4bb', 'computation': '1f4bb', 'logic': '1f4bb',
            'database': '1f4be', 'data storage': '1f4be', 'sql': '1f4be', 'records': '1f4be', 'information': '1f4be',
            'encryption': '1f512', 'cryptography': '1f512', 'security': '1f512', 'cipher': '1f512',
            'protection': '1f512',
            'bandwidth': '1f4e1', 'data rate': '1f4e1', 'throughput': '1f4e1', 'capacity': '1f4e1', 'speed': '1f4e1',
            'firewall': '1f6e1', 'security': '1f6e1', 'protection': '1f6e1', 'barrier': '1f6e1', 'filter': '1f6e1',
            'malware': '1f47e', 'virus': '1f47e', 'trojan': '1f47e', 'spyware': '1f47e', 'infection': '1f47e',
            'backup': '1f4be', 'copy': '1f4be', 'redundancy': '1f4be', 'safety': '1f4be', 'recovery': '1f4be',
            'browser': '1f310', 'web': '1f310', 'internet': '1f310', 'chrome': '1f310', 'firefox': '1f310',
            'cookie': '1f36a', 'web data': '1f36a', 'tracking': '1f36a', 'browser': '1f36a', 'storage': '1f36a',
            'cache': '1f4be', 'temporary storage': '1f4be', 'memory': '1f4be', 'quick access': '1f4be',
            'buffer': '1f4be',
            'plugin': '1f4e5', 'extension': '1f4e5', 'addon': '1f4e5', 'module': '1f4e5', 'component': '1f4e5',
            'interface': '1f5a5', 'ui': '1f5a5', 'user interface': '1f5a5', 'interaction': '1f5a5', 'display': '1f5a5',
            'protocol': '1f4c4', 'standard': '1f4c4', 'procedure': '1f4c4', 'rules': '1f4c4', 'convention': '1f4c4',
            'bandwidth': '1f4e1', 'data rate': '1f4e1', 'throughput': '1f4e1', 'capacity': '1f4e1', 'speed': '1f4e1',
            'latency': '23f3', 'delay': '23f3', 'lag': '23f3', 'response time': '23f3', 'ping': '23f3',
            'resolution': '1f5a5', 'pixels': '1f5a5', 'clarity': '1f5a5', 'display quality': '1f5a5',
            'definition': '1f5a5',
            'processor': '1f4bb', 'cpu': '1f4bb', 'chip': '1f4bb', 'computing': '1f4bb', 'silicon': '1f4bb',
            'memory': '1f4be', 'ram': '1f4be', 'storage': '1f4be', 'data': '1f4be', 'capacity': '1f4be',
            'motherboard': '1f4bb', 'mainboard': '1f4bb', 'pcb': '1f4bb', 'circuit board': '1f4bb', 'hardware': '1f4bb',
            'graphics card': '1f3ae', 'gpu': '1f3ae', 'video card': '1f3ae', 'display adapter': '1f3ae',
            'rendering': '1f3ae',

            # More Food and Cuisine Types
            'mediterranean': '1f96a', 'olive oil': '1f96a', 'hummus': '1f96a', 'falafel': '1f96a', 'pita': '1f96a',
            'thai': '1f35b', 'pad thai': '1f35b', 'tom yum': '1f35b', 'curry': '1f35b', 'spicy': '1f35b',
            'vietnamese': '1f35c', 'pho': '1f35c', 'banh mi': '1f35c', 'fresh herbs': '1f35c', 'rice paper': '1f35c',
            'korean': '1f35b', 'kimchi': '1f35b', 'bibimbap': '1f35b', 'bulgogi': '1f35b', 'korean bbq': '1f35b',
            'greek': '1f96a', 'gyro': '1f96a', 'souvlaki': '1f96a', 'moussaka': '1f96a', 'feta': '1f96a',
            'cajun': '1f35b', 'creole': '1f35b', 'jambalaya': '1f35b', 'gumbo': '1f35b', 'louisiana': '1f35b',
            'soul food': '1f357', 'southern': '1f357', 'comfort food': '1f357', 'collard greens': '1f357',
            'cornbread': '1f357',
            'caribbean': '1f34c', 'jerk': '1f34c', 'plantain': '1f34c', 'rice and peas': '1f34c', 'tropical': '1f34c',
            'ethiopian': '1f35b', 'injera': '1f35b', 'wat': '1f35b', 'berbere': '1f35b', 'african': '1f35b',
            'moroccan': '1f35b', 'tagine': '1f35b', 'couscous': '1f35b', 'harissa': '1f35b', 'mint tea': '1f35b',
            'lebanese': '1f96a', 'tabbouleh': '1f96a', 'fattoush': '1f96a', 'shawarma': '1f96a', 'mezze': '1f96a',
            'turkish': '1f96a', 'kebab': '1f96a', 'baklava': '1f96a', 'turkish delight': '1f96a', 'pide': '1f96a',
            'peruvian': '1f35b', 'ceviche': '1f35b', 'lomo saltado': '1f35b', 'aji': '1f35b', 'quinoa': '1f35b',
            'argentinian': '1f356', 'asado': '1f356', 'chimichurri': '1f356', 'empanadas': '1f356', 'mate': '1f356',
            'brazilian': '1f35a', 'feijoada': '1f35a', 'churrasco': '1f35a', 'pão de queijo': '1f35a', 'açaí': '1f35a',
            'spanish': '1f958', 'paella': '1f958', 'tapas': '1f958', 'gazpacho': '1f958', 'tortilla': '1f958',
            'portuguese': '1f41f', 'bacalhau': '1f41f', 'caldo verde': '1f41f', 'pastel de nata': '1f41f',
            'seafood': '1f41f',
            'german': '1f357', 'bratwurst': '1f357', 'sauerkraut': '1f357', 'schnitzel': '1f357', 'pretzel': '1f357',
            'polish': '1f35b', 'pierogi': '1f35b', 'kielbasa': '1f35b', 'bigos': '1f35b', 'golabki': '1f35b',
            'russian': '1f35b', 'borscht': '1f35b', 'blini': '1f35b', 'pelmeni': '1f35b', 'caviar': '1f35b',

            # More Clothing and Fashion Items
            'tuxedo': '1f935', 'formal wear': '1f935', 'black tie': '1f935', 'suit': '1f935', 'groom': '1f935',
            'wedding dress': '1f470', 'bridal gown': '1f470', 'white dress': '1f470', 'veil': '1f470', 'bride': '1f470',
            'kimono': '1f458', 'japanese': '1f458', 'traditional': '1f458', 'robe': '1f458', 'silk': '1f458',
            'sari': '1f459', 'indian': '1f459', 'traditional': '1f459', 'draped': '1f459', 'colorful': '1f459',
            'turban': '1f473', 'head wrap': '1f473', 'religious': '1f473', 'sikh': '1f473', 'cultural': '1f473',
            'hijab': '1f9d5', 'headscarf': '1f9d5', 'muslim': '1f9d5', 'islamic': '1f9d5', 'modest': '1f9d5',
            'kippah': '1f473', 'yarmulke': '1f473', 'jewish': '1f473', 'religious': '1f473', 'head covering': '1f473',
            'bowtie': '1f454', 'formal': '1f454', 'elegant': '1f454', 'neck wear': '1f454', 'dressy': '1f454',
            'suspenders': '1f454', 'braces': '1f454', 'straps': '1f454', 'pants': '1f454', 'formal': '1f454',
            'cufflinks': '1f4ff', 'formal': '1f4ff', 'sleeve': '1f4ff', 'accessory': '1f4ff', 'elegant': '1f4ff',
            'pocket square': '1f4a7', 'handkerchief': '1f4a7', 'suit': '1f4a7', 'formal': '1f4a7', 'accessory': '1f4a7',
            'vest': '1f455', 'waistcoat': '1f455', 'formal': '1f455', 'suit': '1f455', 'layering': '1f455',
            'cummerbund': '1f454', 'formal': '1f454', 'waist': '1f454', 'tuxedo': '1f454', 'sash': '1f454',
            'shawl': '1f9e3', 'wrap': '1f9e3', 'shoulder': '1f9e3', 'covering': '1f9e3', 'elegant': '1f9e3',
            'poncho': '1f9e5', 'cape': '1f9e5', 'covering': '1f9e5', 'rain': '1f9e5', 'traditional': '1f9e5',
            'overalls': '1f455', 'dungarees': '1f455', 'bib pants': '1f455', 'work wear': '1f455', 'denim': '1f455',
            'romper': '1f457', 'jumpsuit': '1f457', 'one piece': '1f457', 'playsuit': '1f457', 'casual': '1f457',
            'tunic': '1f455', 'long top': '1f455', 'loose': '1f455', 'flowing': '1f455', 'ethnic': '1f455',
            'leggings': '1f456', 'tights': '1f456', 'yoga pants': '1f456', 'stretch': '1f456', 'athletic': '1f456',
            'tracksuit': '1f3c3', 'sweatsuit': '1f3c3', 'athletic': '1f3c3', 'jogging': '1f3c3', 'sporty': '1f3c3',

            # Technology Companies
            'apple': '1f34e', 'iphone': '1f4f1', 'ipad': '1f4bb', 'macbook': '1f4bb', 'ios': '1f34e',
            'google': '1f4dd', 'android': '1f47e', 'chrome': '1fa9f', 'gmail': '1f4e7', 'youtube': '1f3a5',
            'microsoft': '1f5a5', 'windows': '1fa9f', 'xbox': '1f3ae', 'office': '1f4bc', 'surface': '1f5a5',
            'amazon': '1f4e6', 'aws': '1f310', 'alexa': '1f916', 'kindle': '1f4d6', 'prime': '1f4e6',
            'facebook': '1f4f7', 'meta': '1f4f7', 'instagram': '1f4f8', 'whatsapp': '1f4ac', 'oculus': '1f453',
            'tesla': '1f697', 'spacex': '1f680', 'cybertruck': '1f697', 'elon musk': '1f916', 'starlink': '1f4e1',
            'samsung': '1f4f1', 'galaxy': '1f30c', 'android': '1f47e', 'smartphone': '1f4f1', 'tv': '1f4fa',
            'sony': '1f3a5', 'playstation': '1f3ae', 'bluetooth': '1f4f6', 'camera': '1f4f7', 'headphones': '1f3a7',
            'nvidia': '1f5a5', 'gpu': '1f5a5', 'graphics': '1f5a5', 'ai': '1f916', 'computing': '1f4bb',
            'intel': '1f5a5', 'processor': '1f5a5', 'chip': '1f5a5', 'technology': '1f4bb', 'silicon': '1f5a5',
            'amd': '1f5a5', 'ryzen': '1f5a5', 'radeon': '1f5a5', 'cpu': '1f5a5', 'hardware': '1f5a5',
            'netflix': '1f3ac', 'streaming': '1f4fa', 'movies': '1f3ac', 'tv shows': '1f4fa', 'entertainment': '1f3ac',
            'disney': '1f3a0', 'pixar': '1f3a8', 'marvel': '1f9d8', 'starwars': '1f50d', 'mickey': '1f3a0',
            'twitter': '1f426', 'x': '1f426', 'tweet': '1f426', 'social media': '1f4f1', 'blue bird': '1f426',
            'linkedin': '1f4bc', 'career': '1f4bc', 'professional': '1f4bc', 'networking': '1f4bc', 'jobs': '1f4bc',
            'tiktok': '1f3a4', 'viral': '1f4e2', 'dance': '1f483', 'short video': '1f4f8', 'social media': '1f4f1',
            'spotify': '1f3b5', 'music': '1f3b5', 'streaming': '1f4fa', 'playlist': '1f3b6', 'premium': '1f3b5',
            'adobe': '1f58c', 'photoshop': '1f58c', 'creative cloud': '1f3a8', 'design': '1f3a8',
            'illustrator': '1f58c',
            'uber': '1f695', 'ride': '1f695', 'taxi': '1f695', 'transport': '1f68c', 'sharing economy': '1f695',
            'airbnb': '1f3e0', 'vacation': '1f3e0', 'rental': '1f3e0', 'travel': '1f6eb', 'home sharing': '1f3e0',
            'nike': '1f45f', 'swoosh': '1f45f', 'just do it': '1f3c3', 'sneakers': '1f45f', 'athletic': '1f3c3',
            'adidas': '1f45f', 'sport': '1f3c3', 'three stripes': '1f45f', 'sneakers': '1f45f', 'football': '26bd',
            'starbucks': '2615', 'coffee': '2615', 'latte': '2615', 'frappuccino': '2615', 'pumpkin spice': '2615',
            'mcdonalds': '1f354', 'mcd': '1f354', 'fast food': '1f354', 'fries': '1f35f', 'golden arches': '1f354',
            'coca cola': '1f964', 'coke': '1f964', 'soda': '1f964', 'cola': '1f964', 'soft drink': '1f964',
            'pepsi': '1f964', 'cola': '1f964', 'soda': '1f964', 'blue logo': '1f964', 'soft drink': '1f964',
            'red bull': '1f4aa', 'energy drink': '1f4aa', 'wings': '1f4aa', 'gives you wings': '1f4aa',
            'extreme': '1f4aa',
            'gucci': '1f45e', 'luxury': '1f48e', 'fashion': '1f45e', 'designer': '1f45e', 'expensive': '1f48e',
            'louis vuitton': '1f45e', 'lv': '1f45e', 'luxury': '1f48e', 'handbag': '1f45c', 'fashion': '1f45e',
            'chanel': '1f45e', 'luxury': '1f48e', 'fashion': '1f45e', 'perfume': '1f5ff', 'designer': '1f45e',
            'mercedes': '1f697', 'benz': '1f697', 'luxury car': '1f697', 'german': '1f697', 'automobile': '1f697',
            'bmw': '1f697', 'car': '1f697', 'german': '1f697', 'luxury': '1f48e', 'automobile': '1f697',
            'audi': '1f697', 'car': '1f697', 'german': '1f697', 'luxury': '1f48e', 'four rings': '1f697',
            'toyota': '1f697', 'japanese': '1f697', 'car': '1f697', 'reliable': '1f697', 'automobile': '1f697',
            'honda': '1f697', 'japanese': '1f697', 'car': '1f697', 'motorcycle': '1f3cd', 'automobile': '1f697',
            'ford': '1f697', 'american': '1f697', 'car': '1f697', 'truck': '1f69a', 'automobile': '1f697',
            'volkswagen': '1f697', 'vw': '1f697', 'german': '1f697', 'car': '1f697', 'automobile': '1f697',
            'tesla': '1f697', 'electric': '1f50b', 'car': '1f697', 'elon musk': '1f916', 'automobile': '1f697',
            'spacex': '1f680', 'rocket': '1f680', 'elon musk': '1f916', 'mars': '1f680', 'space travel': '1f680',
            'boeing': '2708', 'airplane': '2708', 'aviation': '2708', '787': '2708', 'dreamliner': '2708',
            'airbus': '2708', 'airplane': '2708', 'aviation': '2708', 'a380': '2708', 'superjumbo': '2708',
            'samsung': '1f4f1', 'galaxy': '1f30c', 'smartphone': '1f4f1', 'android': '1f47e', 'technology': '1f4bb',
            'huawei': '1f4f1', 'smartphone': '1f4f1', 'chinese': '1f4f1', 'technology': '1f4bb', '5g': '1f4f1',
            'xiaomi': '1f4f1', 'smartphone': '1f4f1', 'chinese': '1f4f1', 'technology': '1f4bb', 'budget': '1f4f1',
            'oneplus': '1f4f1', 'smartphone': '1f4f1', 'flagship': '1f4f1', 'technology': '1f4bb', 'android': '1f47e',
            'oppo': '1f4f1', 'smartphone': '1f4f1', 'chinese': '1f4f1', 'technology': '1f4bb', 'camera': '1f4f7',
            'vivo': '1f4f1', 'smartphone': '1f4f1', 'chinese': '1f4f1', 'technology': '1f4bb', 'selfie': '1f4f7',
            'nokia': '1f4f1', 'smartphone': '1f4f1', 'finland': '1f4f1', 'technology': '1f4bb', 'durable': '1f4f1',
            'blackberry': '1f4f1', 'smartphone': '1f4f1', 'keyboard': '2328', 'business': '1f4bc', 'secure': '1f512',
            'motorola': '1f4f1', 'smartphone': '1f4f1', 'razr': '1f4f1', 'technology': '1f4bb', 'flip phone': '1f4f1',
            'lg': '1f4fa', 'smartphone': '1f4f1', 'tv': '1f4fa', 'technology': '1f4bb', 'electronics': '1f4bb',
            'sony': '1f3a5', 'playstation': '1f3ae', 'camera': '1f4f7', 'technology': '1f4bb', 'electronics': '1f4bb',
            'panasonic': '1f4fa', 'tv': '1f4fa', 'technology': '1f4bb', 'electronics': '1f4bb', 'japanese': '1f4fa',
            'sharp': '1f4fa', 'tv': '1f4fa', 'technology': '1f4bb', 'electronics': '1f4bb', 'japanese': '1f4fa',
            'toshiba': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'electronics': '1f4bb', 'japanese': '1f4bb',
            'hitachi': '1f4bb', 'technology': '1f4bb', 'electronics': '1f4bb', 'japanese': '1f4bb',
            'industrial': '1f4bb',
            'fujitsu': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'electronics': '1f4bb', 'japanese': '1f4bb',
            'ibm': '1f4bb', 'computer': '1f4bb', 'technology': '1f4bb', 'mainframe': '1f4bb', 'enterprise': '1f4bb',
            'hp': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'printer': '1f5a8', 'computer': '1f4bb',
            'dell': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'computer': '1f4bb', 'enterprise': '1f4bb',
            'lenovo': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'computer': '1f4bb', 'chinese': '1f4bb',
            'acer': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'computer': '1f4bb', 'taiwan': '1f4bb',
            'asus': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'computer': '1f4bb', 'taiwan': '1f4bb',
            'msi': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'computer': '1f4bb', 'gaming': '1f3ae',
            'razer': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'computer': '1f4bb', 'gaming': '1f3ae',
            'alienware': '1f4bb', 'laptop': '1f4bb', 'technology': '1f4bb', 'computer': '1f4bb', 'gaming': '1f3ae',
            'logitech': '1f5b1', 'mouse': '1f5b1', 'keyboard': '2328', 'technology': '1f4bb', 'peripherals': '1f5b1',
            'steelseries': '1f3ae', 'gaming': '1f3ae', 'mouse': '1f5b1', 'keyboard': '2328', 'headset': '1f3a7',
            'corsair': '1f4bb', 'computer': '1f4bb', 'technology': '1f4bb', 'gaming': '1f3ae', 'memory': '1f4bb',
            'hyperx': '1f3ae', 'gaming': '1f3ae', 'headset': '1f3a7', 'keyboard': '2328', 'memory': '1f4bb',
            'nzxt': '1f4bb', 'computer': '1f4bb', 'technology': '1f4bb', 'gaming': '1f3ae', 'case': '1f4bb',
            'cooler master': '1f4bb', 'computer': '1f4bb', 'technology': '1f4bb', 'gaming': '1f3ae', 'cooling': '1f4bb',
            'thermaltake': '1f4bb', 'computer': '1f4bb', 'technology': '1f4bb', 'gaming': '1f3ae', 'cooling': '1f4bb',
            'evga': '1f5a5', 'gpu': '1f5a5', 'technology': '1f4bb', 'gaming': '1f3ae', 'graphics': '1f5a5',
            'amd': '1f5a5', 'cpu': '1f5a5', 'technology': '1f4bb', 'gaming': '1f3ae', 'processor': '1f5a5',
            'intel': '1f5a5', 'cpu': '1f5a5', 'technology': '1f4bb', 'processor': '1f5a5', 'silicon': '1f5a5',
            'nvidia': '1f5a5', 'gpu': '1f5a5', 'technology': '1f4bb', 'gaming': '1f3ae', 'graphics': '1f5a5',
            'qualcomm': '1f4f1', 'smartphone': '1f4f1', 'technology': '1f4bb', 'processor': '1f5a5',
            'snapdragon': '1f4f1',
            'mediatek': '1f4f1', 'smartphone': '1f4f1', 'technology': '1f4bb', 'processor': '1f5a5', 'budget': '1f4f1',
            'arm': '1f5a5', 'processor': '1f5a5', 'technology': '1f4bb', 'mobile': '1f4f1', 'architecture': '1f5a5',
            'broadcom': '1f4e1', 'wifi': '1f4e1', 'technology': '1f4bb', 'chip': '1f5a5', 'networking': '1f4e1',
            'texas instruments': '1f5a5', 'calculator': '1f5a5', 'technology': '1f4bb', 'chip': '1f5a5',
            'electronics': '1f4bb',
            'ibm': '1f4bb', 'computer': '1f4bb', 'technology': '1f4bb', 'mainframe': '1f4bb', 'enterprise': '1f4bb',
            'oracle': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'enterprise': '1f4bb', 'software': '1f4be',
            'sap': '1f4bb', 'enterprise': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'salesforce': '1f4bb', 'crm': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'adobe': '1f58c', 'photoshop': '1f58c', 'technology': '1f4bb', 'software': '1f4be', 'creative': '1f3a8',
            'autodesk': '1f58c', 'cad': '1f58c', 'technology': '1f4bb', 'software': '1f4be', 'design': '1f3a8',
            'microsoft': '1f5a5', 'windows': '1fa9f', 'technology': '1f4bb', 'software': '1f4be', 'office': '1f4bc',
            'google': '1f4dd', 'search': '1f50d', 'technology': '1f4bb', 'software': '1f4be', 'android': '1f47e',
            'amazon': '1f4e6', 'aws': '1f310', 'technology': '1f4bb', 'software': '1f4be', 'ecommerce': '1f4e6',
            'alibaba': '1f4e6', 'ecommerce': '1f4e6', 'technology': '1f4bb', 'software': '1f4be', 'chinese': '1f4e6',
            'tencent': '1f4e6', 'wechat': '1f4e6', 'technology': '1f4bb', 'software': '1f4be', 'chinese': '1f4e6',
            'baidu': '1f50d', 'search': '1f50d', 'technology': '1f4bb', 'software': '1f4be', 'chinese': '1f50d',
            'xiaomi': '1f4f1', 'smartphone': '1f4f1', 'technology': '1f4bb', 'software': '1f4be', 'chinese': '1f4f1',
            'bytedance': '1f4f8', 'tiktok': '1f4f8', 'technology': '1f4bb', 'software': '1f4be', 'chinese': '1f4f8',
            'huawei': '1f4f1', 'smartphone': '1f4f1', 'technology': '1f4bb', 'software': '1f4be', 'chinese': '1f4f1',
            'zoho': '1f4bb', 'software': '1f4be', 'technology': '1f4bb', 'business': '1f4bc', 'india': '1f4bb',
            'infosys': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'india': '1f4bb',
            'tcs': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'india': '1f4bb',
            'wipro': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'india': '1f4bb',
            'tech mahindra': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'india': '1f4bb',
            'hcl': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'india': '1f4bb',
            'accenture': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'consulting': '1f4bb',
            'capgemini': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'consulting': '1f4bb',
            'cognizant': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'consulting': '1f4bb',
            'ibm': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'consulting': '1f4bb',
            'oracle': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'enterprise': '1f4bb',
            'sap': '1f4bb', 'enterprise': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'salesforce': '1f4bb', 'crm': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'workday': '1f4bb', 'hr': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'servicenow': '1f4bb', 'it': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'atlassian': '1f4bb', 'jira': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'slack': '1f4ac', 'communication': '1f4ac', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'zoom': '1f4f9', 'video': '1f4f9', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'microsoft teams': '1f4ac', 'communication': '1f4ac', 'technology': '1f4bb', 'software': '1f4be',
            'business': '1f4bc',
            'cisco': '1f4e1', 'networking': '1f4e1', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'vmware': '1f4bb', 'virtualization': '1f4bb', 'technology': '1f4bb', 'software': '1f4be',
            'business': '1f4bc',
            'red hat': '1f4bb', 'linux': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'canonical': '1f4bb', 'ubuntu': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'docker': '1f433', 'container': '1f433', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'kubernetes': '1f433', 'container': '1f433', 'technology': '1f4bb', 'software': '1f4be',
            'business': '1f4bc',
            'ansible': '1f4bb', 'automation': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'terraform': '1f4bb', 'iac': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'jenkins': '1f4bb', 'ci/cd': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'github': '1f4bb', 'git': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'gitlab': '1f4bb', 'git': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'bitbucket': '1f4bb', 'git': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'jira': '1f4bb', 'agile': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'confluence': '1f4bb', 'wiki': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'trello': '1f4bb', 'kanban': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'asana': '1f4bb', 'project': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'basecamp': '1f4bb', 'project': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'monday.com': '1f4bb', 'project': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'clickup': '1f4bb', 'project': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'notion': '1f4bb', 'notes': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'evernote': '1f4bb', 'notes': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'dropbox': '1f4bb', 'storage': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'box': '1f4bb', 'storage': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'google drive': '1f4bb', 'storage': '1f4bb', 'technology': '1f4bb', 'software': '1f4be',
            'business': '1f4bc',
            'onedrive': '1f4bb', 'storage': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'icloud': '1f4bb', 'storage': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'aws': '1f310', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'azure': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'google cloud': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'ibm cloud': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'oracle cloud': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'alibaba cloud': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'digitalocean': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'linode': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'heroku': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'netlify': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'vercel': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'firebase': '2601', 'cloud': '2601', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'mongodb': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'mysql': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'postgresql': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'sql server': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'oracle': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'redis': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'elasticsearch': '1f4bb', 'search': '1f4bb', 'technology': '1f4bb', 'software': '1f4be',
            'business': '1f4bc',
            'dynamodb': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'cassandra': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'couchbase': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'neo4j': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'influxdb': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'snowflake': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb', 'software': '1f4be', 'business': '1f4bc',
            'bigquery': '1f4bb', 'database': '1f4bb', 'technology': '1f4bb',

            # Transportation and vehicles - COMPREHENSIVE
            'transportation': '1f68c', 'transport': '1f68c', 'vehicle': '1f68c', 'travel': '1f68c', 'mobility': '1f68c',
            'public transport': '1f68c', 'transit': '1f68c', 'commute': '1f68c', 'passenger': '1f68c',
            'service': '1f68c',
            'traffic': '1f6a6', 'congestion': '1f6a6', 'jam': '1f6a6', 'road': '1f6a6', 'signal': '1f6a6',
            'fuel': '26fd', 'gas': '26fd', 'gasoline': '26fd', 'petrol': '26fd', 'diesel': '26fd',
            'engine': '1f6e0', 'motor': '1f6e0', 'power': '1f6e0', 'mechanical': '1f6e0', 'drive': '1f6e0',
            'wheel': '1f6de', 'tire': '1f6de', 'round': '1f6de', 'rubber': '1f6de', 'rotation': '1f6de',
            'brake': '1f6d1', 'stop': '1f6d1', 'slow': '1f6d1', 'safety': '1f6d1', 'control': '1f6d1',
            'steering': '1f6e0', 'direction': '1f6e0', 'control': '1f6e0', 'navigate': '1f6e0', 'turn': '1f6e0',
            'license': '1f4c4', 'permit': '1f4c4', 'driving': '1f4c4', 'legal': '1f4c4', 'document': '1f4c4',
            'insurance': '1f4c4', 'coverage': '1f4c4', 'protection': '1f4c4', 'policy': '1f4c4', 'safety': '1f4c4',
            'accident': '1f4a5', 'crash': '1f4a5', 'collision': '1f4a5', 'damage': '1f4a5', 'emergency': '1f4a5',
            'repair': '1f527', 'fix': '1f527', 'maintenance': '1f527', 'service': '1f527', 'mechanic': '1f527',
            'parking': '1f17f', 'space': '1f17f', 'lot': '1f17f', 'garage': '1f17f', 'meter': '1f17f',
            'route': '1f5fa', 'path': '1f5fa', 'way': '1f5fa', 'direction': '1f5fa', 'navigation': '1f5fa',
            'map': '1f5fa', 'navigation': '1f5fa', 'gps': '1f5fa', 'location': '1f5fa', 'geography': '1f5fa',
            'destination': '1f3c1', 'target': '1f3c1', 'goal': '1f3c1', 'endpoint': '1f3c1', 'arrival': '1f3c1',
            'journey': '1f6e4', 'trip': '1f6e4', 'travel': '1f6e4', 'voyage': '1f6e4', 'expedition': '1f6e4',
            'passenger': '1f9d1', 'rider': '1f9d1', 'traveler': '1f9d1', 'commuter': '1f9d1', 'tourist': '1f9d1',
            'driver': '1f9d1-200d-1f692', 'operator': '1f9d1-200d-1f692', 'pilot': '1f9d1-200d-1f692',
            'chauffeur': '1f9d1-200d-1f692',

            # Technology and digital - COMPREHENSIVE
            'technology': '1f4bb', 'tech': '1f4bb', 'digital': '1f4bb', 'electronic': '1f4bb', 'modern': '1f4bb',
            'computer': '1f4bb', 'pc': '1f4bb', 'laptop': '1f4bb', 'desktop': '1f4bb', 'machine': '1f4bb',
            'software': '1f4be', 'program': '1f4be', 'application': '1f4be', 'app': '1f4be', 'code': '1f4be',
            'hardware': '1f5a5', 'components': '1f5a5', 'parts': '1f5a5', 'physical': '1f5a5', 'equipment': '1f5a5',
            'internet': '1f310', 'web': '1f310', 'online': '1f310', 'network': '1f310', 'worldwide': '1f310',
            'website': '1f310', 'webpage': '1f310', 'site': '1f310', 'portal': '1f310', 'domain': '1f310',
            'email': '1f4e7', 'mail': '1f4e7', 'message': '1f4e7', 'electronic': '1f4e7', 'communication': '1f4e7',
            'password': '1f512', 'security': '1f512', 'access': '1f512', 'login': '1f512', 'authentication': '1f512',
            'data': '1f4ca', 'information': '1f4ca', 'digital': '1f4ca', 'storage': '1f4ca', 'database': '1f4ca',
            'file': '1f4c1', 'document': '1f4c1', 'folder': '1f4c1', 'directory': '1f4c1', 'archive': '1f4c1',
            'download': '1f4e5', 'upload': '1f4e4', 'transfer': '1f4e5', 'save': '1f4e5', 'receive': '1f4e5',
            'backup': '1f4be', 'copy': '1f4be', 'duplicate': '1f4be', 'save': '1f4be', 'preserve': '1f4be',
            'virus': '1f4a5', 'malware': '1f4a5', 'infection': '1f4a5', 'bug': '1f4a5', 'corruption': '1f4a5',
            'update': '1f504', 'upgrade': '1f504', 'refresh': '1f504', 'new version': '1f504', 'patch': '1f504',
            'install': '1f4e6', 'setup': '1f4e6', 'configure': '1f4e6', 'deploy': '1f4e6', 'implement': '1f4e6',
            'programming': '1f4bb', 'coding': '1f4bb', 'development': '1f4bb', 'scripting': '1f4bb',
            'algorithm': '1f4bb',
            'artificial intelligence': '1f916', 'ai': '1f916', 'machine learning': '1f916', 'robot': '1f916',
            'automation': '1f916',
            'virtual reality': '1f97d', 'vr': '1f97d', 'simulation': '1f97d', 'immersive': '1f97d',
            'digital world': '1f97d',
            'social media': '1f4f1', 'social network': '1f4f1', 'platform': '1f4f1', 'sharing': '1f4f1',
            'connection': '1f4f1',
            'smartphone': '1f4f1', 'mobile phone': '1f4f1', 'cell phone': '1f4f1', 'device': '1f4f1',
            'handheld': '1f4f1',
            'bluetooth': '1f4f6', 'wireless': '1f4f6', 'connection': '1f4f6', 'pairing': '1f4f6',
            'short range': '1f4f6',
            'wifi': '1f4f6', 'wireless internet': '1f4f6', 'network': '1f4f6', 'connection': '1f4f6', 'signal': '1f4f6',
            'cloud': '2601', 'cloud computing': '2601', 'online storage': '2601', 'remote': '2601', 'server': '2601',
            'streaming': '1f4fa', 'video': '1f4fa', 'audio': '1f4fa', 'live': '1f4fa', 'broadcast': '1f4fa',
            'gaming': '1f3ae', 'video games': '1f3ae', 'console': '1f3ae', 'controller': '1f3ae',
            'entertainment': '1f3ae',

            # Positions and directions
            'up': '2b06', 'upward': '2b06', 'above': '2b06', 'higher': '2b06', 'top': '2b06',
            'down': '2b07', 'downward': '2b07', 'below': '2b07', 'lower': '2b07', 'bottom': '2b07',
            'left': '2b05', 'leftward': '2b05', 'west': '2b05', 'port': '2b05', 'sinister': '2b05',
            'right': '27a1', 'rightward': '27a1', 'east': '27a1', 'starboard': '27a1', 'dexter': '27a1',
            'forward': '23e9', 'ahead': '23e9', 'front': '23e9', 'advance': '23e9', 'onward': '23e9',
            'backward': '23ea', 'back': '23ea', 'reverse': '23ea', 'retreat': '23ea', 'behind': '23ea',
            'north': '1f9ed', 'northern': '1f9ed', 'arctic': '1f9ed', 'pole': '1f9ed', 'compass': '1f9ed',
            'south': '1f9ed', 'southern': '1f9ed', 'antarctic': '1f9ed', 'pole': '1f9ed', 'compass': '1f9ed',
            'east': '1f9ed', 'eastern': '1f9ed', 'orient': '1f9ed', 'sunrise': '1f9ed', 'compass': '1f9ed',
            'west': '1f9ed', 'western': '1f9ed', 'occident': '1f9ed', 'sunset': '1f9ed', 'compass': '1f9ed',
            'center': '1f3af', 'middle': '1f3af', 'central': '1f3af', 'core': '1f3af', 'heart': '1f3af',
            'edge': '1f4cf', 'border': '1f4cf', 'boundary': '1f4cf', 'perimeter': '1f4cf', 'margin': '1f4cf',
            'corner': '1f4cf', 'angle': '1f4cf', 'vertex': '1f4cf', 'intersection': '1f4cf', 'junction': '1f4cf',
            'inside': '1f4e6', 'interior': '1f4e6', 'within': '1f4e6', 'inner': '1f4e6', 'internal': '1f4e6',
            'outside': '1f4e6', 'exterior': '1f4e6', 'beyond': '1f4e6', 'outer': '1f4e6', 'external': '1f4e6',
            'near': '1f4cd', 'close': '1f4cd', 'nearby': '1f4cd', 'adjacent': '1f4cd', 'proximity': '1f4cd',
            'far': '1f4cd', 'distant': '1f4cd', 'remote': '1f4cd', 'away': '1f4cd', 'faraway': '1f4cd',
            'here': '1f4cd', 'this place': '1f4cd', 'current location': '1f4cd', 'present': '1f4cd', 'spot': '1f4cd',
            'there': '1f4cd', 'that place': '1f4cd', 'other location': '1f4cd', 'distant': '1f4cd',
            'over there': '1f4cd',
            'everywhere': '1f30d', 'all places': '1f30d', 'universal': '1f30d', 'global': '1f30d', 'worldwide': '1f30d',
            'nowhere': '1f6ab', 'no place': '1f6ab', 'absent': '1f6ab', 'missing': '1f6ab', 'void': '1f6ab',
            'somewhere': '1f4cd', 'some place': '1f4cd', 'location': '1f4cd', 'position': '1f4cd', 'spot': '1f4cd',
            'anywhere': '1f30d', 'any place': '1f30d', 'wherever': '1f30d', 'universal': '1f30d', 'flexible': '1f30d',

            # Understanding and clarity
            'clear': '1f50d', 'obvious': '1f440', 'visible': '1f440', 'transparent': '1f50d', 'evident': '1f440',
            'explain': '1f4dd', 'description': '1f4dd', 'clarify': '1f4dd', 'elaborate': '1f4dd', 'detail': '1f4dd',
            'understand': '1f4a1', 'comprehend': '1f4a1', 'grasp': '1f4a1', 'realize': '1f4a1', 'get it': '1f4a1',
            'hidden': '1f648', 'secret': '1f648', 'concealed': '1f648', 'invisible': '1f648', 'obscure': '1f648',
            'show': '1f449', 'display': '1f449', 'reveal': '1f449', 'demonstrate': '1f449', 'exhibit': '1f449',
            'hide': '1f648', 'conceal': '1f648', 'cover': '1f648', 'mask': '1f648', 'obscure': '1f648',
            'simple': '1f44d', 'easy': '1f44d', 'basic': '1f44d', 'straightforward': '1f44d', 'uncomplicated': '1f44d',
            'complex': '1f913', 'complicated': '1f913', 'difficult': '1f913', 'intricate': '1f913',
            'sophisticated': '1f913',
            'confusing': '1f615', 'puzzling': '1f615', 'bewildering': '1f615', 'perplexing': '1f615',
            'unclear': '1f615',
            'obvious': '1f440', 'clear': '1f440', 'evident': '1f440', 'apparent': '1f440', 'plain': '1f440',
            'mysterious': '1f575', 'enigmatic': '1f575', 'puzzling': '1f575', 'cryptic': '1f575', 'unknown': '1f575',
            'transparent': '1f50d', 'see-through': '1f50d', 'clear': '1f50d', 'honest': '1f50d', 'open': '1f50d',
            'opaque': '1f648', 'solid': '1f648', 'blocked': '1f648', 'impenetrable': '1f648', 'dense': '1f648',

            # Money and wealth - COMPREHENSIVE
            'money': '1f4b8', 'currency': '1f4b8', 'cash': '1f4b5', 'bills': '1f4b5', 'notes': '1f4b5',
            'coin': '1fa99', 'coins': '1fa99', 'change': '1fa99', 'metal money': '1fa99', 'small denomination': '1fa99',
            'dollar': '1f4b5', 'usd': '1f4b5', 'american': '1f4b5', 'greenback': '1f4b5', 'buck': '1f4b5',
            'euro': '1f4b6', 'eur': '1f4b6', 'european': '1f4b6', 'continental': '1f4b6', 'single currency': '1f4b6',
            'pound': '1f4b7', 'gbp': '1f4b7', 'sterling': '1f4b7', 'british': '1f4b7', 'uk currency': '1f4b7',
            'yen': '1f4b4', 'jpy': '1f4b4', 'japanese': '1f4b4', 'asian': '1f4b4', 'oriental': '1f4b4',
            'wealthy': '1f4b0', 'rich': '1f4b0', 'affluent': '1f4b0', 'prosperous': '1f4b0', 'well-off': '1f4b0',
            'poor': '1f4c9', 'broke': '1f4c9', 'impoverished': '1f4c9', 'needy': '1f4c9', 'destitute': '1f4c9',
            'expensive': '1f48e', 'costly': '1f48e', 'pricey': '1f48e', 'high-priced': '1f48e', 'luxury': '1f48e',
            'cheap': '1f4b5', 'inexpensive': '1f4b5', 'affordable': '1f4b5', 'budget': '1f4b5', 'economical': '1f4b5',
            'free': '1f193', 'complimentary': '1f193', 'no cost': '1f193', 'gratis': '1f193', 'zero price': '1f193',
            'price': '1f3f7', 'cost': '1f3f7', 'value': '1f3f7', 'amount': '1f3f7', 'rate': '1f3f7',
            'sale': '1f3f7', 'discount': '1f3f7', 'bargain': '1f3f7', 'deal': '1f3f7', 'reduced': '1f3f7',
            'profit': '1f4c8', 'gain': '1f4c8', 'earnings': '1f4c8', 'income': '1f4c8', 'revenue': '1f4c8',
            'loss': '1f4c9', 'deficit': '1f4c9', 'debt': '1f4c9', 'negative': '1f4c9', 'red ink': '1f4c9',
            'investment': '1f4b0', 'stock': '1f4b0', 'share': '1f4b0', 'portfolio': '1f4b0', 'finance': '1f4b0',
            'bank': '1f3e6', 'banking': '1f3e6', 'financial': '1f3e6', 'deposit': '1f3e6', 'withdrawal': '1f3e6',
            'credit': '1f4b3', 'loan': '1f4b3', 'borrow': '1f4b3', 'advance': '1f4b3', 'finance': '1f4b3',
            'debit': '1f4b3', 'charge': '1f4b3', 'subtract': '1f4b3', 'remove': '1f4b3', 'take out': '1f4b3',
            'budget': '1f4ca', 'plan': '1f4ca', 'allocate': '1f4ca', 'financial plan': '1f4ca', 'spending': '1f4ca',
            'tax': '1f4c4', 'taxation': '1f4c4', 'government fee': '1f4c4', 'levy': '1f4c4', 'duty': '1f4c4',
            'salary': '1f4b0', 'wage': '1f4b0', 'pay': '1f4b0', 'income': '1f4b0', 'earnings': '1f4b0',
            'bonus': '1f4b0', 'extra pay': '1f4b0', 'incentive': '1f4b0', 'reward': '1f4b0', 'additional': '1f4b0',
            'tip': '1f4b5', 'gratuity': '1f4b5', 'service charge': '1f4b5', 'extra': '1f4b5', 'appreciation': '1f4b5',
            'receipt': '1f9fe', 'proof': '1f9fe', 'record': '1f9fe', 'transaction': '1f9fe', 'payment': '1f9fe',
            'bill': '1f4c4', 'invoice': '1f4c4', 'statement': '1f4c4', 'charge': '1f4c4', 'account': '1f4c4',
            'change': '1fa99', 'difference': '1fa99', 'remainder': '1fa99', 'leftover': '1fa99', 'balance': '1fa99',
            'refund': '1f4b5', 'return': '1f4b5', 'payback': '1f4b5', 'reimbursement': '1f4b5', 'give back': '1f4b5',

            # Zone and area definitions
            'zone': '1f3af', 'area': '1f5fa', 'region': '1f5fa', 'place': '1f4cd', 'location': '1f4cd',
            'district': '1f3d8', 'neighborhood': '1f3d8', 'quarter': '1f3d8', 'sector': '1f3d8', 'precinct': '1f3d8',
            'territory': '1f5fa', 'domain': '1f5fa', 'realm': '1f5fa', 'land': '1f5fa', 'country': '1f5fa',
            'boundary': '1f6a7', 'border': '1f6a7', 'limit': '1f6a7', 'edge': '1f6a7', 'perimeter': '1f6a7',
            'space': '1f30c', 'room': '1f30c', 'gap': '1f30c', 'opening': '1f30c', 'void': '1f30c',
            'field': '1f33e', 'meadow': '1f33e', 'pasture': '1f33e', 'grassland': '1f33e', 'plain': '1f33e',
            'ground': '1f30d', 'earth': '1f30d', 'soil': '1f30d', 'land': '1f30d', 'surface': '1f30d',
            'floor': '1f3e0', 'bottom': '1f3e0', 'base': '1f3e0', 'foundation': '1f3e0', 'lower': '1f3e0',
            'ceiling': '1f3e0', 'top': '1f3e0', 'upper': '1f3e0', 'overhead': '1f3e0', 'roof': '1f3e0',
            'wall': '1f9f1', 'barrier': '1f9f1', 'partition': '1f9f1', 'side': '1f9f1', 'vertical': '1f9f1',

            # Martial arts and Filipino culture
            'martial': '1f94b', 'karate': '1f94b', 'taekwondo': '1f94b', 'kung fu': '1f94b', 'combat': '1f94b',
            'filipino': '1f1f5-1f1ed', 'philippines': '1f1f5-1f1ed', 'pinoy': '1f1f5-1f1ed', 'asian': '1f1f5-1f1ed',
            'famous': '1f31f', 'popular': '1f31f', 'well-known': '1f31f', 'celebrated': '1f31f', 'renowned': '1f31f',
            'art': '1f3a8', 'arts': '1f3a8', 'creative': '1f3a8', 'skill': '1f3a8', 'technique': '1f3a8',
            'sport': '26bd', 'sports': '26bd', 'athletic': '26bd', 'competition': '26bd', 'game': '26bd',
            'fighting': '1f94a', 'combat': '1f94a', 'battle': '1f94a', 'conflict': '1f94a', 'struggle': '1f94a',
            'defense': '1f6e1', 'protection': '1f6e1', 'guard': '1f6e1', 'shield': '1f6e1', 'safety': '1f6e1',
            'attack': '2694', 'assault': '2694', 'strike': '2694', 'hit': '2694', 'offensive': '2694',
            'training': '1f4aa', 'practice': '1f4aa', 'exercise': '1f4aa', 'workout': '1f4aa', 'preparation': '1f4aa',
            'discipline': '1f4aa', 'control': '1f4aa', 'order': '1f4aa', 'focus': '1f4aa', 'concentration': '1f4aa',
            'technique': '1f4aa', 'method': '1f4aa', 'skill': '1f4aa', 'approach': '1f4aa', 'way': '1f4aa',
            'master': '1f451', 'expert': '1f451', 'teacher': '1f451', 'guru': '1f451', 'sensei': '1f451',
            'student': '1f9d1-200d-1f393', 'learner': '1f9d1-200d-1f393', 'pupil': '1f9d1-200d-1f393',
            'apprentice': '1f9d1-200d-1f393',
            'belt': '1f94e', 'rank': '1f94e', 'level': '1f94e', 'grade': '1f94e', 'degree': '1f94e',
            'uniform': '1f45a', 'gi': '1f45a', 'outfit': '1f45a', 'clothing': '1f45a', 'attire': '1f45a',
            'dojo': '1f3e2', 'training hall': '1f3e2', 'school': '1f3e2', 'academy': '1f3e2', 'gym': '1f3e2',
            'tournament': '1f3c6', 'competition': '1f3c6', 'contest': '1f3c6', 'championship': '1f3c6',
            'match': '1f3c6',
            'victory': '1f3c6', 'win': '1f3c6', 'triumph': '1f3c6', 'success': '1f3c6', 'achievement': '1f3c6',
            'defeat': '274c', 'loss': '274c', 'failure': '274c', 'beaten': '274c', 'unsuccessful': '274c',

            # Mammals
            'dog': '1f436', 'puppy': '1f436', 'canine': '1f436', 'hound': '1f436', 'mutt': '1f436',
            'cat': '1f431', 'kitten': '1f431', 'feline': '1f431', 'kitty': '1f431', 'tabby': '1f431',
            'horse': '1f434', 'stallion': '1f434', 'mare': '1f434', 'pony': '1f434', 'equine': '1f434',
            'cow': '1f42e', 'bull': '1f402', 'cattle': '1f42e', 'bovine': '1f42e', 'steer': '1f402',
            'pig': '1f437', 'hog': '1f437', 'swine': '1f437', 'boar': '1f417', 'piglet': '1f437',
            'sheep': '1f411', 'lamb': '1f411', 'ewe': '1f411', 'ram': '1f40f', 'wool': '1f411',
            'goat': '1f410', 'kid': '1f410', 'billy goat': '1f410', 'nanny goat': '1f410', 'caprine': '1f410',
            'rabbit': '1f430', 'bunny': '1f430', 'hare': '1f430', 'cottontail': '1f430', 'jackrabbit': '1f430',
            'mouse': '1f42d', 'mice': '1f42d', 'rodent': '1f42d', 'field mouse': '1f42d', 'house mouse': '1f42d',
            'rat': '1f400', 'rodent': '1f400', 'sewer rat': '1f400', 'lab rat': '1f400', 'pest': '1f400',
            'hamster': '1f439', 'gerbil': '1f439', 'guinea pig': '1f439', 'pet rodent': '1f439', 'wheel': '1f439',
            'squirrel': '1f43f', 'chipmunk': '1f43f', 'tree rat': '1f43f', 'acorn': '1f43f', 'bushy tail': '1f43f',
            'bear': '1f43b', 'grizzly': '1f43b', 'black bear': '1f43b', 'brown bear': '1f43b', 'ursine': '1f43b',
            'panda': '1f43c', 'giant panda': '1f43c', 'bamboo bear': '1f43c', 'chinese bear': '1f43c',
            'black and white': '1f43c',
            'koala': '1f428', 'marsupial': '1f428', 'eucalyptus': '1f428', 'australian': '1f428', 'tree bear': '1f428',
            'lion': '1f981', 'king of jungle': '1f981', 'mane': '1f981', 'pride': '1f981', 'roar': '1f981',
            'tiger': '1f405', 'stripes': '1f405', 'big cat': '1f405', 'bengal': '1f405', 'siberian': '1f405',
            'leopard': '1f406', 'spots': '1f406', 'big cat': '1f406', 'rosettes': '1f406', 'panther': '1f406',
            'cheetah': '1f406', 'fastest': '1f406', 'spotted': '1f406', 'big cat': '1f406', 'speed': '1f406',
            'wolf': '1f43a', 'pack': '1f43a', 'howl': '1f43a', 'alpha': '1f43a', 'wild dog': '1f43a',
            'fox': '1f98a', 'red fox': '1f98a', 'cunning': '1f98a', 'sly': '1f98a', 'bushy tail': '1f98a',
            'deer': '1f98c', 'doe': '1f98c', 'buck': '1f98c', 'antlers': '1f98c', 'venison': '1f98c',
            'elk': '1f98c', 'moose': '1f98c', 'caribou': '1f98c', 'reindeer': '1f98c', 'antlers': '1f98c',
            'elephant': '1f418', 'trunk': '1f418', 'tusks': '1f418', 'pachyderm': '1f418', 'mammoth': '1f418',
            'rhino': '1f98f', 'rhinoceros': '1f98f', 'horn': '1f98f', 'thick skin': '1f98f', 'charge': '1f98f',
            'hippo': '1f99b', 'hippopotamus': '1f99b', 'river horse': '1f99b', 'water': '1f99b', 'massive': '1f99b',
            'giraffe': '1f992', 'tall': '1f992', 'neck': '1f992', 'spots': '1f992', 'african': '1f992',
            'zebra': '1f993', 'stripes': '1f993', 'black and white': '1f993', 'african': '1f993', 'equine': '1f993',
            'monkey': '1f412', 'ape': '1f412', 'primate': '1f412', 'banana': '1f412', 'swing': '1f412',
            'gorilla': '1f98d', 'silverback': '1f98d', 'ape': '1f98d', 'primate': '1f98d', 'chest beat': '1f98d',
            'orangutan': '1f9a7', 'ape': '1f9a7', 'red hair': '1f9a7', 'primate': '1f9a7', 'tree swinger': '1f9a7',
            'chimpanzee': '1f412', 'chimp': '1f412', 'ape': '1f412', 'primate': '1f412', 'intelligent': '1f412',
            'sloth': '1f9a5', 'slow': '1f9a5', 'tree': '1f9a5', 'lazy': '1f9a5', 'hanging': '1f9a5',
            'bat': '1f987', 'flying mammal': '1f987', 'nocturnal': '1f987', 'cave': '1f987', 'vampire': '1f987',
            'whale': '1f40b', 'blue whale': '1f40b', 'humpback': '1f40b', 'sperm whale': '1f40b', 'cetacean': '1f40b',
            'dolphin': '1f42c', 'porpoise': '1f42c', 'intelligent': '1f42c', 'playful': '1f42c', 'marine': '1f42c',
            'seal': '1f9ad', 'sea lion': '1f9ad', 'pinniped': '1f9ad', 'flipper': '1f9ad', 'marine mammal': '1f9ad',
            'walrus': '1f9ad', 'tusks': '1f9ad', 'whiskers': '1f9ad', 'arctic': '1f9ad', 'blubber': '1f9ad',
            'otter': '1f9a6', 'sea otter': '1f9a6', 'river otter': '1f9a6', 'playful': '1f9a6', 'swimming': '1f9a6',
            'beaver': '1f9ab', 'dam': '1f9ab', 'teeth': '1f9ab', 'tail': '1f9ab', 'wood': '1f9ab',
            'hedgehog': '1f994', 'spines': '1f994', 'quills': '1f994', 'roll up': '1f994', 'prickly': '1f994',
            'skunk': '1f9a8', 'spray': '1f9a8', 'smell': '1f9a8', 'black and white': '1f9a8', 'stink': '1f9a8',
            'raccoon': '1f99d', 'bandit': '1f99d', 'mask': '1f99d', 'trash panda': '1f99d', 'ringtail': '1f99d',
            'opossum': '1f43e', 'possum': '1f43e', 'marsupial': '1f43e', 'play dead': '1f43e', 'pouch': '1f43e',
            'kangaroo': '1f998', 'joey': '1f998', 'pouch': '1f998', 'hop': '1f998', 'australian': '1f998',
            'llama': '1f999', 'alpaca': '1f999', 'spit': '1f999', 'wool': '1f999', 'andean': '1f999',
            'camel': '1f42a', 'hump': '1f42a', 'desert': '1f42a', 'dromedary': '1f42a', 'bactrian': '1f42a',

            # Birds

            'eagle': '1f985', 'bald eagle': '1f985', 'golden eagle': '1f985', 'raptor': '1f985', 'soar': '1f985',
            'hawk': '1f985', 'falcon': '1f985', 'kestrel': '1f985', 'peregrine': '1f985', 'bird of prey': '1f985',
            'blackbird': '1f426',
            'sparrow': '1f426', 'house sparrow': '1f426', 'small bird': '1f426', 'chirp': '1f426', 'common': '1f426',
            'cardinal': '1f426', 'red bird': '1f426', 'songbird': '1f426', 'crest': '1f426', 'winter': '1f426',
            'woodpecker': '1f426', 'peck': '1f426', 'tree': '1f426', 'drum': '1f426', 'red head': '1f426',
            'hummingbird': '1f426', 'tiny': '1f426', 'hover': '1f426', 'nectar': '1f426', 'fast wings': '1f426',
            'duck': '1f986', 'mallard': '1f986', 'quack': '1f986', 'pond': '1f986', 'waterfowl': '1f986',
            'goose': '1f986', 'honk': '1f986', 'migration': '1f986', 'v formation': '1f986', 'waterfowl': '1f986',
            'chicken': '1f414', 'hen': '1f414', 'rooster': '1f413', 'cock': '1f413', 'poultry': '1f414',
            'turkey': '1f983', 'gobble': '1f983', 'thanksgiving': '1f983', 'tom': '1f983', 'feathers': '1f983',
            'peacock': '1f99a', 'peafowl': '1f99a', 'plumage': '1f99a', 'colorful': '1f99a', 'display': '1f99a',
            'flamingo': '1f9a9', 'pink': '1f9a9', 'long neck': '1f9a9', 'one leg': '1f9a9', 'tropical': '1f9a9',
            'pelican': '1f426', 'pouch': '1f426', 'fish': '1f426', 'large beak': '1f426', 'coastal': '1f426',
            'seagull': '1f426', 'gull': '1f426', 'beach': '1f426', 'scavenger': '1f426', 'coastal': '1f426',
            'penguin': '1f427', 'antarctic': '1f427', 'tuxedo': '1f427', 'waddle': '1f427', 'flightless': '1f427',
            'ostrich': '1f426', 'large bird': '1f426', 'flightless': '1f426', 'fast runner': '1f426',
            'african': '1f426',
            'parrot': '1f99c', 'colorful': '1f99c', 'talk': '1f99c', 'tropical': '1f99c', 'mimic': '1f99c',
            'canary': '1f426', 'yellow': '1f426', 'sing': '1f426', 'cage bird': '1f426', 'pet': '1f426',
            'pigeon': '1f54a', 'dove': '1f54a', 'city bird': '1f54a', 'messenger': '1f54a', 'coo': '1f54a',

            # Fish and Marine Life
            'fish': '1f41f', 'swimming': '1f41f', 'fins': '1f41f', 'gills': '1f41f', 'scales': '1f41f',
            'shark': '1f988', 'great white': '1f988', 'predator': '1f988', 'teeth': '1f988', 'ocean': '1f988',
            'goldfish': '1f420', 'pet fish': '1f420', 'bowl': '1f420', 'orange': '1f420', 'aquarium': '1f420',
            'tuna': '1f41f', 'salmon': '1f41f', 'trout': '1f41f', 'bass': '1f41f', 'cod': '1f41f',
            'octopus': '1f419', 'tentacles': '1f419', 'eight arms': '1f419', 'intelligent': '1f419', 'ink': '1f419',
            'squid': '1f991', 'tentacles': '1f991', 'calamari': '1f991', 'ink': '1f991', 'deep sea': '1f991',
            'jellyfish': '1f41f', 'sting': '1f41f', 'transparent': '1f41f', 'tentacles': '1f41f', 'drift': '1f41f',
            'starfish': '2b50', 'sea star': '2b50', 'five arms': '2b50', 'regenerate': '2b50', 'tide pool': '2b50',
            'seahorse': '1f41f', 'curved': '1f41f', 'pouch': '1f41f', 'male pregnancy': '1f41f', 'coral': '1f41f',
            'crab': '1f980', 'claws': '1f980', 'sideways': '1f980', 'shell': '1f980', 'beach': '1f980',
            'lobster': '1f99e', 'claws': '1f99e', 'red': '1f99e', 'seafood': '1f99e', 'expensive': '1f99e',
            'shrimp': '1f990', 'prawn': '1f990', 'small': '1f990', 'seafood': '1f990', 'cocktail': '1f990',

            # Reptiles and Amphibians
            'snake': '1f40d', 'serpent': '1f40d', 'slither': '1f40d', 'scales': '1f40d', 'venom': '1f40d',
            'lizard': '1f98e', 'gecko': '1f98e', 'iguana': '1f98e', 'chameleon': '1f98e', 'cold blooded': '1f98e',
            'turtle': '1f422', 'tortoise': '1f422', 'shell': '1f422', 'slow': '1f422', 'reptile': '1f422',
            'crocodile': '1f40a', 'alligator': '1f40a', 'teeth': '1f40a', 'swamp': '1f40a', 'predator': '1f40a',
            'frog': '1f438', 'toad': '1f438', 'hop': '1f438', 'pond': '1f438', 'amphibian': '1f438',
            'salamander': '1f98e', 'newt': '1f98e', 'amphibian': '1f98e', 'moist skin': '1f98e', 'regenerate': '1f98e',

            # Insects and Bugs
            'bee': '1f41d', 'honey': '1f41d', 'buzz': '1f41d', 'sting': '1f41d', 'hive': '1f41d',
            'butterfly': '1f98b', 'colorful': '1f98b', 'wings': '1f98b', 'metamorphosis': '1f98b', 'flutter': '1f98b',
            'ant': '1f41c', 'colony': '1f41c', 'worker': '1f41c', 'strong': '1f41c', 'march': '1f41c',
            'spider': '1f577', 'web': '1f577', 'eight legs': '1f577', 'arachnid': '1f577', 'bite': '1f577',
            'fly': '1fab2', 'buzz': '1fab2', 'pest': '1fab2', 'wings': '1fab2', 'annoying': '1fab2',
            'ladybug': '1f41e', 'spots': '1f41e', 'red': '1f41e', 'good luck': '1f41e', 'garden': '1f41e',
            'beetle': '1fab2', 'hard shell': '1fab2', 'insect': '1fab2', 'crawl': '1fab2', 'bug': '1fab2',
            'cocoon': '1f41b',

            # Time and Age
            'clock': '1f570', 'time': '23f0', 'hour': '23f0', 'minute': '23f0', 'second': '23f0',
            'watch': '231a', 'timepiece': '231a', 'wrist': '231a', 'tick': '231a', 'alarm': '23f0',
            'age': '1f4c5', 'years': '1f4c5', 'old': '1f4c5', 'young': '1f4c5', 'birthday': '1f382',
            'day': '1f4c5', 'night': '1f319', 'morning': '1f305', 'evening': '1f307', 'noon': '2600',
            'week': '1f4c5', 'month': '1f4c5', 'year': '1f4c5', 'decade': '1f4c5', 'century': '1f4c5',
            'season': '1f341', 'spring': '1f33c', 'summer': '2600', 'autumn': '1f341', 'winter': '2744',
            'yesterday': '1f4c5', 'today': '1f4c5', 'tomorrow': '1f4c5', 'past': '1f4c5', 'future': '1f4c5',

            # Writing and School Supplies
            'pencil': '270f', 'pen': '1f58a', 'marker': '1f58d', 'crayon': '1f58d', 'chalk': '270f',
            'eraser': '270f', 'rubber': '270f', 'sharpener': '270f', 'lead': '270f', 'graphite': '270f',
            'paper': '1f4c4', 'notebook': '1f4d3', 'journal': '1f4d4', 'diary': '1f4d4', 'pad': '1f4c4',
            'book': '1f4d6', 'novel': '1f4d5', 'textbook': '1f4da', 'dictionary': '1f4d6', 'encyclopedia': '1f4da',
            'ruler': '1f4cf', 'measure': '1f4cf', 'straight edge': '1f4cf', 'inches': '1f4cf', 'centimeters': '1f4cf',
            'scissors': '2702', 'cut': '2702', 'snip': '2702', 'blades': '2702', 'sharp': '2702',
            'glue': '1f4ce', 'paste': '1f4ce', 'stick': '1f4ce', 'adhesive': '1f4ce', 'bond': '1f4ce',
            'stapler': '1f4ce', 'staples': '1f4ce', 'clip': '1f4ce', 'attach': '1f4ce', 'bind': '1f4ce',
            'tape': '1f4ce', 'sticky': '1f4ce', 'roll': '1f4ce', 'transparent': '1f4ce', 'adhesive': '1f4ce',
            'folder': '1f4c1', 'binder': '1f4c2', 'organize': '1f4c1', 'file': '1f4c1', 'documents': '1f4c1',
            'backpack': '1f392', 'bag': '1f45c', 'satchel': '1f392', 'carry': '1f392', 'school': '1f392',
            'calculator': '1f5a9', 'math': '1f5a9', 'numbers': '1f5a9', 'compute': '1f5a9', 'add': '1f5a9',

            # Household Objects
            'door': '1f6aa', 'entrance': '1f6aa', 'exit': '1f6aa', 'knob': '1f6aa', 'handle': '1f6aa',
            'window': '1fa9f', 'glass': '1fa9f', 'pane': '1fa9f', 'view': '1fa9f', 'light': '1fa9f',
            'wall': '1f9f1', 'barrier': '1f9f1', 'partition': '1f9f1', 'surface': '1f9f1', 'paint': '1f9f1',
            'floor': '1f9f1', 'ground': '1f9f1', 'carpet': '1f9f1', 'tile': '1f9f1', 'wood': '1f9f1',
            'ceiling': '1f9f1', 'roof': '1f3e0', 'top': '1f9f1', 'overhead': '1f9f1', 'cover': '1f9f1',
            'stairs': '1f9f1', 'steps': '1f9f1', 'climb': '1f9f1', 'up': '2b06', 'down': '2b07',
            'key': '1f511', 'lock': '1f512', 'unlock': '1f513', 'secure': '1f512', 'open': '1f513',
            'mirror': '1fa9e', 'reflection': '1fa9e', 'glass': '1fa9e', 'image': '1fa9e', 'vanity': '1fa9e',
            'lamp': '1f4a1', 'light': '1f4a1', 'bulb': '1f4a1', 'bright': '1f4a1', 'illuminate': '1f4a1',
            'candle': '1f56f', 'flame': '1f525', 'wax': '1f56f', 'wick': '1f56f', 'romantic': '1f56f',
            'curtain': '1f9f1', 'drapes': '1f9f1', 'blind': '1f9f1', 'cover': '1f9f1', 'privacy': '1f9f1',
            'pillow': '1f9f1', 'cushion': '1f9f1', 'soft': '1f9f1', 'comfort': '1f9f1', 'head': '1f9f1',
            'blanket': '1f9f1', 'cover': '1f9f1', 'warm': '1f9f1', 'cozy': '1f9f1', 'bed': '1f6cf',
            'towel': '1f9f1', 'dry': '1f9f1', 'cloth': '1f9f1', 'bathroom': '1f6c1', 'absorb': '1f9f1',

            # Personal Hygiene Products
            'shampoo': '1f9f4', 'hair wash': '1f9f4', 'cleanse': '1f9f4', 'lather': '1f9f4', 'scalp': '1f9f4',
            'conditioner': '1f9f4', 'hair care': '1f9f4', 'smooth': '1f9f4', 'detangle': '1f9f4', 'soft': '1f9f4',
            'soap': '1f9fc', 'bar soap': '1f9fc', 'hand soap': '1f9fc', 'body wash': '1f9fc', 'cleanse': '1f9fc',
            'body wash': '1f9fc', 'shower gel': '1f9fc', 'liquid soap': '1f9fc', 'bubbles': '1f9fc', 'clean': '1f9fc',
            'toothpaste': '1faa5', 'dental': '1faa5', 'fluoride': '1faa5', 'mint': '1faa5', 'brush': '1faa5',
            'toothbrush': '1faa5', 'bristles': '1faa5', 'dental': '1faa5', 'clean teeth': '1faa5', 'oral': '1faa5',
            'mouthwash': '1f9ea', 'rinse': '1f9ea', 'antiseptic': '1f9ea', 'fresh breath': '1f9ea', 'gargle': '1f9ea',
            'floss': '1faa5', 'dental floss': '1faa5', 'string': '1faa5', 'between teeth': '1faa5', 'gums': '1faa5',
            'deodorant': '1f9f4', 'antiperspirant': '1f9f4', 'underarm': '1f9f4', 'fresh': '1f9f4', 'odor': '1f9f4',
            'perfume': '1f490', 'cologne': '1f490', 'fragrance': '1f490', 'scent': '1f490', 'spray': '1f490',
            'lotion': '1f9f4', 'moisturizer': '1f9f4', 'cream': '1f9f4', 'skin care': '1f9f4', 'hydrate': '1f9f4',
            'sunscreen': '1f9f4', 'sunblock': '1f9f4', 'spf': '1f9f4', 'protection': '1f9f4', 'uv': '1f9f4',
            'lip balm': '1f48b', 'chapstick': '1f48b', 'lips': '1f48b', 'moisture': '1f48b', 'protect': '1f48b',
            'face wash': '1f9fc', 'cleanser': '1f9fc', 'facial': '1f9fc', 'pores': '1f9fc', 'skin': '1f9fc',
            'moisturizer': '1f9f4', 'face cream': '1f9f4', 'hydrating': '1f9f4', 'anti-aging': '1f9f4',
            'smooth': '1f9f4',
            'toner': '1f9f4', 'astringent': '1f9f4', 'facial': '1f9f4', 'balance': '1f9f4', 'refresh': '1f9f4',
            'serum': '1f9f4', 'treatment': '1f9f4', 'concentrated': '1f9f4', 'skin care': '1f9f4', 'active': '1f9f4',
            'exfoliant': '1f9f4', 'scrub': '1f9f4', 'dead skin': '1f9f4', 'smooth': '1f9f4', 'polish': '1f9f4',
            'face mask': '1f9f4', 'treatment': '1f9f4', 'clay': '1f9f4', 'hydrating': '1f9f4', 'peel': '1f9f4',
            'eye cream': '1f9f4', 'under eye': '1f9f4', 'wrinkles': '1f9f4', 'dark circles': '1f9f4',
            'delicate': '1f9f4',

            # Hair Care Products
            'hair gel': '1f9f4', 'styling': '1f9f4', 'hold': '1f9f4', 'slick': '1f9f4', 'wet look': '1f9f4',
            'hair spray': '1f9f4', 'aerosol': '1f9f4', 'hold': '1f9f4', 'freeze': '1f9f4', 'style': '1f9f4',
            'mousse': '1f9f4', 'foam': '1f9f4', 'volume': '1f9f4', 'lift': '1f9f4', 'body': '1f9f4',
            'pomade': '1f9f4', 'wax': '1f9f4', 'shine': '1f9f4', 'slick': '1f9f4', 'vintage': '1f9f4',
            'hair oil': '1f9f4', 'treatment': '1f9f4', 'nourish': '1f9f4', 'shine': '1f9f4', 'repair': '1f9f4',
            'dry shampoo': '1f9f4', 'powder': '1f9f4', 'absorb': '1f9f4', 'refresh': '1f9f4', 'volume': '1f9f4',
            'leave-in conditioner': '1f9f4', 'detangler': '1f9f4', 'protect': '1f9f4', 'smooth': '1f9f4',
            'daily': '1f9f4',
            'hair dye': '1f3a8', 'color': '1f3a8', 'tint': '1f3a8', 'bleach': '1f3a8', 'highlights': '1f3a8',
            'hair mask': '1f9f4', 'deep treatment': '1f9f4', 'repair': '1f9f4', 'intensive': '1f9f4', 'weekly': '1f9f4',

            # Shaving and Grooming
            'razor': '1fa92', 'shave': '1fa92', 'blade': '1fa92', 'sharp': '1fa92', 'cut': '1fa92',
            'shaving cream': '1f9fc', 'foam': '1f9fc', 'lather': '1f9fc', 'smooth': '1f9fc', 'protect': '1f9fc',
            'aftershave': '1f9f4', 'soothe': '1f9f4', 'sting': '1f9f4', 'antiseptic': '1f9f4', 'cool': '1f9f4',
            'electric razor': '1fa92', 'shaver': '1fa92', 'electric': '1fa92', 'cordless': '1fa92', 'quick': '1fa92',
            'tweezers': '1f527', 'pluck': '1f527', 'eyebrows': '1f527', 'precision': '1f527', 'hair removal': '1f527',
            'nail clippers': '1f527', 'trim': '1f527', 'cut nails': '1f527', 'manicure': '1f485', 'grooming': '1f527',
            'nail file': '1f485', 'emery board': '1f485', 'shape': '1f485', 'smooth': '1f485', 'manicure': '1f485',
            'cuticle cream': '1f485', 'nail care': '1f485', 'soften': '1f485', 'moisturize': '1f485',
            'healthy': '1f485',

            # Cosmetics and Makeup
            'foundation': '1f484', 'base': '1f484', 'coverage': '1f484', 'even': '1f484', 'skin tone': '1f484',
            'concealer': '1f484', 'cover': '1f484', 'hide': '1f484', 'blemish': '1f484', 'dark circles': '1f484',
            'powder': '1f484', 'setting': '1f484', 'matte': '1f484', 'finish': '1f484', 'compact': '1f484',
            'blush': '1f484', 'rouge': '1f484', 'cheeks': '1f484', 'color': '1f484', 'glow': '1f484',
            'bronzer': '1f484', 'tan': '1f484', 'contour': '1f484', 'warm': '1f484', 'sun-kissed': '1f484',
            'highlighter': '1f484', 'glow': '1f484', 'shimmer': '1f484', 'cheekbones': '1f484', 'radiant': '1f484',
            'eyeshadow': '1f484', 'eye makeup': '1f484', 'color': '1f484', 'palette': '1f484', 'blend': '1f484',
            'eyeliner': '1f484', 'define': '1f484', 'eyes': '1f484', 'pencil': '1f484', 'liquid': '1f484',
            'mascara': '1f484', 'lashes': '1f484', 'volume': '1f484', 'length': '1f484', 'black': '1f484',
            'lipstick': '1f484', 'lips': '1f484', 'color': '1f484', 'matte': '1f484', 'glossy': '1f484',
            'lip gloss': '1f484', 'shine': '1f484', 'glossy': '1f484', 'wet look': '1f484', 'plump': '1f484',
            'nail polish': '1f485', 'lacquer': '1f485', 'color': '1f485', 'manicure': '1f485', 'shine': '1f485',
            'makeup remover': '1f9fc', 'cleanse': '1f9fc', 'remove': '1f9fc', 'gentle': '1f9fc', 'wipes': '1f9fc',
            'primer': '1f484', 'base': '1f484', 'prep': '1f484', 'smooth': '1f484', 'long-lasting': '1f484',
            'setting spray': '1f484', 'lock': '1f484', 'finish': '1f484', 'all day': '1f484', 'mist': '1f484',

            # Bathroom Items
            'toilet paper': '1f9fb', 'tissue': '1f9fb', 'roll': '1f9fb', 'bathroom': '1f6bd', 'soft': '1f9fb',
            'tissues': '1f9fb', 'kleenex': '1f9fb', 'nose': '1f9fb', 'sneeze': '1f9fb', 'blow': '1f9fb',
            'cotton balls': '1f9fb', 'cotton pads': '1f9fb', 'makeup removal': '1f9fb', 'soft': '1f9fb',
            'absorbent': '1f9fb',
            'q-tips': '1f9fb', 'cotton swabs': '1f9fb', 'ears': '1f9fb', 'clean': '1f9fb', 'precision': '1f9fb',
            'bath towel': '1f9fb', 'dry': '1f9fb', 'absorbent': '1f9fb', 'fluffy': '1f9fb', 'cotton': '1f9fb',
            'hand towel': '1f9fb', 'small towel': '1f9fb', 'guest': '1f9fb', 'quick dry': '1f9fb',
            'convenient': '1f9fb',
            'washcloth': '1f9fb', 'face cloth': '1f9fb', 'scrub': '1f9fb', 'exfoliate': '1f9fb', 'texture': '1f9fb',
            'bath mat': '1f9fb', 'rug': '1f9fb', 'non-slip': '1f9fb', 'safety': '1f9fb', 'absorbent': '1f9fb',
            'shower curtain': '1f6bf', 'privacy': '1f6bf', 'water barrier': '1f6bf', 'plastic': '1f6bf',
            'hooks': '1f6bf',
            'bath bomb': '1f6c1', 'fizz': '1f6c1', 'relax': '1f6c1', 'fragrance': '1f6c1', 'colorful': '1f6c1',
            'bubble bath': '1f6c1', 'foam': '1f6c1', 'relaxing': '1f6c1', 'soak': '1f6c1', 'luxury': '1f6c1',
            'bath salts': '1f6c1', 'epsom': '1f6c1', 'soak': '1f6c1', 'muscle relief': '1f6c1', 'therapeutic': '1f6c1',
            'loofa': '1f9fd', 'sponge': '1f9fd', 'exfoliate': '1f9fd', 'scrub': '1f9fd', 'natural': '1f9fd',
            'body sponge': '1f9fd', 'wash': '1f9fd', 'lather': '1f9fd', 'soft': '1f9fd', 'cleanse': '1f9fd',

            # Cleaning Products
            'detergent': '1f9fd', 'laundry soap': '1f9fd', 'wash clothes': '1f9fd', 'clean': '1f9fd', 'suds': '1f9fd',
            'fabric softener': '1f9fd', 'conditioner': '1f9fd', 'soft clothes': '1f9fd', 'fresh scent': '1f9fd',
            'static': '1f9fd',
            'bleach': '1f9fd', 'whiten': '1f9fd', 'disinfect': '1f9fd', 'chlorine': '1f9fd', 'stain removal': '1f9fd',
            'stain remover': '1f9fd', 'spot treatment': '1f9fd', 'pre-treat': '1f9fd', 'tough stains': '1f9fd',
            'enzyme': '1f9fd',
            'dish soap': '1f9fd', 'dishwashing liquid': '1f9fd', 'grease cutting': '1f9fd', 'bubbles': '1f9fd',
            'clean dishes': '1f9fd',
            'dishwasher detergent': '1f9fd', 'pods': '1f9fd', 'automatic': '1f9fd', 'rinse aid': '1f9fd',
            'spotless': '1f9fd',
            'all-purpose cleaner': '1f9fd', 'multi-surface': '1f9fd', 'spray': '1f9fd', 'disinfect': '1f9fd',
            'versatile': '1f9fd',
            'glass cleaner': '1f9fd', 'window cleaner': '1f9fd', 'streak-free': '1f9fd', 'ammonia': '1f9fd',
            'clear': '1f9fd',
            'bathroom cleaner': '1f9fd', 'tub cleaner': '1f9fd', 'tile cleaner': '1f9fd', 'mildew': '1f9fd',
            'soap scum': '1f9fd',
            'toilet bowl cleaner': '1f9fd', 'disinfectant': '1f9fd', 'germs': '1f9fd', 'sanitize': '1f9fd',
            'fresh': '1f9fd',
            'floor cleaner': '1f9fd', 'mop solution': '1f9fd', 'hardwood': '1f9fd', 'tile': '1f9fd', 'shine': '1f9fd',
            'carpet cleaner': '1f9fd', 'upholstery': '1f9fd', 'steam clean': '1f9fd', 'deep clean': '1f9fd',
            'fresh': '1f9fd',
            'furniture polish': '1f9fd', 'wood cleaner': '1f9fd', 'shine': '1f9fd', 'protect': '1f9fd', 'dust': '1f9fd',
            'air freshener': '1f32c', 'spray': '1f32c', 'plug-in': '1f32c', 'scent': '1f32c',
            'odor eliminator': '1f32c',
            'disinfectant': '1f9fd', 'kill germs': '1f9fd', 'sanitize': '1f9fd', 'antibacterial': '1f9fd',
            'virus': '1f9fd',
            'hand sanitizer': '1f9fc', 'alcohol': '1f9fc', 'gel': '1f9fc', 'kill germs': '1f9fc', 'portable': '1f9fc',

            # Laundry Products
            'dryer sheets': '1f9fd', 'fabric softener': '1f9fd', 'static cling': '1f9fd', 'fresh scent': '1f9fd',
            'soft': '1f9fd',
            'laundry pods': '1f9fd', 'detergent pods': '1f9fd', 'concentrated': '1f9fd', 'convenient': '1f9fd',
            'pre-measured': '1f9fd',
            'color-safe bleach': '1f9fd', 'oxygen bleach': '1f9fd', 'brighten': '1f9fd', 'gentle': '1f9fd',
            'color protection': '1f9fd',
            'wrinkle releaser': '1f9fd', 'spray': '1f9fd', 'smooth': '1f9fd', 'iron alternative': '1f9fd',
            'quick fix': '1f9fd',
            'starch': '1f9fd', 'crisp': '1f9fd', 'stiff': '1f9fd', 'professional': '1f9fd', 'iron': '1f9fd',
            'lint roller': '1f9fd', 'sticky': '1f9fd', 'pet hair': '1f9fd', 'remove lint': '1f9fd',
            'clean clothes': '1f9fd',

            # Health and Medicine
            'vitamins': '1f48a', 'supplements': '1f48a', 'health': '1f48a', 'nutrition': '1f48a', 'daily': '1f48a',
            'aspirin': '1f48a', 'pain relief': '1f48a', 'headache': '1f48a', 'fever': '1f48a',
            'anti-inflammatory': '1f48a',
            'ibuprofen': '1f48a', 'advil': '1f48a', 'pain killer': '1f48a', 'reduce swelling': '1f48a',
            'fever reducer': '1f48a',
            'acetaminophen': '1f48a', 'tylenol': '1f48a', 'pain relief': '1f48a', 'fever': '1f48a', 'safe': '1f48a',
            'antacid': '1f48a', 'heartburn': '1f48a', 'acid reflux': '1f48a', 'stomach': '1f48a', 'relief': '1f48a',
            'cough drops': '1f48a', 'throat lozenges': '1f48a', 'soothe': '1f48a', 'menthol': '1f48a', 'cough': '1f48a',
            'cough syrup': '1f48a', 'liquid medicine': '1f48a', 'suppress cough': '1f48a', 'sleep': '1f48a',
            'cherry': '1f48a',
            'allergy medicine': '1f48a', 'antihistamine': '1f48a', 'seasonal': '1f48a', 'pollen': '1f48a',
            'runny nose': '1f48a',
            'nasal spray': '1f48a', 'decongestant': '1f48a', 'stuffy nose': '1f48a', 'sinus': '1f48a',
            'breathe': '1f48a',
            'eye drops': '1f48a', 'dry eyes': '1f48a', 'irritation': '1f48a', 'redness': '1f48a', 'soothe': '1f48a',
            'bandages': '1fa79', 'band-aids': '1fa79', 'adhesive': '1fa79', 'wound care': '1fa79', 'protect': '1fa79',
            'antiseptic': '1f48a', 'disinfectant': '1f48a', 'wound care': '1f48a', 'prevent infection': '1f48a',
            'clean': '1f48a',
            'thermometer': '1f321', 'temperature': '1f321', 'fever': '1f321', 'digital': '1f321', 'health': '1f321',
            'heating pad': '1f525', 'warmth': '1f525', 'muscle pain': '1f525', 'comfort': '1f525', 'electric': '1f525',
            'ice pack': '1f9ca', 'cold therapy': '1f9ca', 'swelling': '1f9ca', 'injury': '1f9ca', 'numb': '1f9ca',

            # Personal Care Tools
            'hair dryer': '1f4a8', 'blow dryer': '1f4a8', 'hot air': '1f4a8', 'style': '1f4a8', 'quick dry': '1f4a8',
            'curling iron': '1f525', 'hot tool': '1f525', 'curls': '1f525', 'waves': '1f525', 'style': '1f525',
            'flat iron': '1f525', 'straightener': '1f525', 'smooth': '1f525', 'sleek': '1f525', 'hot plates': '1f525',
            'hair brush': '1f9f4', 'detangle': '1f9f4', 'smooth': '1f9f4', 'bristles': '1f9f4', 'style': '1f9f4',
            'comb': '1f9f4', 'teeth': '1f9f4', 'part hair': '1f9f4', 'neat': '1f9f4', 'plastic': '1f9f4',
            'makeup brush': '1f484', 'application': '1f484', 'blend': '1f484', 'soft bristles': '1f484',
            'precision': '1f484',
            'makeup sponge': '1f484', 'beauty blender': '1f484', 'blend': '1f484', 'foundation': '1f484',
            'seamless': '1f484',
            'eyelash curler': '1f484', 'curl lashes': '1f484', 'lift': '1f484', 'open eyes': '1f484', 'metal': '1f484',
            'compact mirror': '1fa9e', 'portable': '1fa9e', 'purse mirror': '1fa9e', 'touch up': '1fa9e',
            'small': '1fa9e',
            'magnifying mirror': '1fa9e', 'zoom': '1fa9e', 'detail': '1fa9e', 'precision': '1fa9e', 'makeup': '1fa9e',

            # Feminine Care
            'tampons': '1f9f7', 'feminine hygiene': '1f9f7', 'menstrual': '1f9f7', 'period': '1f9f7',
            'protection': '1f9f7',
            'pads': '1f9f7', 'sanitary pads': '1f9f7', 'menstrual pads': '1f9f7', 'absorbent': '1f9f7',
            'comfort': '1f9f7',
            'panty liners': '1f9f7', 'daily protection': '1f9f7', 'light days': '1f9f7', 'freshness': '1f9f7',
            'thin': '1f9f7',
            'menstrual cup': '1f9f7', 'reusable': '1f9f7', 'eco-friendly': '1f9f7', 'silicone': '1f9f7',
            'sustainable': '1f9f7',

            # Baby Care Products
            'baby shampoo': '1f476', 'gentle': '1f476', 'no tears': '1f476', 'mild': '1f476', 'infant': '1f476',
            'baby lotion': '1f476', 'sensitive skin': '1f476', 'moisturize': '1f476', 'gentle': '1f476',
            'hypoallergenic': '1f476',
            'baby powder': '1f476', 'talc': '1f476', 'dry': '1f476', 'rash prevention': '1f476', 'soft': '1f476',
            'diaper cream': '1f476', 'rash cream': '1f476', 'zinc oxide': '1f476', 'protect': '1f476',
            'soothe': '1f476',
            'baby wipes': '1f476', 'gentle cleansing': '1f476', 'sensitive': '1f476', 'convenient': '1f476',
            'portable': '1f476',
            'diapers': '1f476', 'nappies': '1f476', 'absorbent': '1f476', 'leak protection': '1f476',
            'comfort': '1f476',

            # Oral Care Accessories
            'dental picks': '1faa5', 'toothpicks': '1faa5', 'between teeth': '1faa5', 'plaque': '1faa5',
            'clean': '1faa5',
            'water flosser': '1faa5', 'oral irrigator': '1faa5', 'water pressure': '1faa5', 'gum health': '1faa5',
            'deep clean': '1faa5',
            'tongue scraper': '1faa5', 'tongue cleaner': '1faa5', 'bad breath': '1faa5', 'oral hygiene': '1faa5',
            'fresh': '1faa5',
            'denture cleaner': '1faa5', 'denture tablets': '1faa5', 'soak': '1faa5', 'clean dentures': '1faa5',
            'fresh': '1faa5',
            'whitening strips': '1faa5', 'teeth whitening': '1faa5', 'bleach': '1faa5', 'bright smile': '1faa5',
            'cosmetic': '1faa5',
            'whitening toothpaste': '1faa5', 'stain removal': '1faa5', 'bright teeth': '1faa5', 'whitening': '1faa5',
            'smile': '1faa5',

            # Furniture
            'chair': '1fa91', 'seat': '1fa91', 'sit': '1fa91', 'furniture': '1fa91', 'legs': '1fa91',
            'table': '1f9f1', 'desk': '1f9f1', 'surface': '1f9f1', 'furniture': '1f9f1', 'flat': '1f9f1',
            'bed': '1f6cf', 'sleep': '1f6cf', 'mattress': '1f6cf', 'rest': '1f6cf', 'bedroom': '1f6cf',
            'sofa': '1f6cb', 'couch': '1f6cb', 'furniture': '1f6cb', 'living room': '1f6cb', 'sit': '1f6cb',
            'shelf': '1f9f1', 'bookshelf': '1f4da', 'storage': '1f9f1', 'display': '1f9f1', 'organize': '1f9f1',
            'cabinet': '1f9f1', 'cupboard': '1f9f1', 'storage': '1f9f1', 'doors': '1f9f1', 'kitchen': '1f9f1',
            'drawer': '1f9f1', 'pull': '1f9f1', 'storage': '1f9f1', 'slide': '1f9f1', 'compartment': '1f9f1',
            'closet': '1f9f1', 'wardrobe': '1f9f1', 'clothes': '1f456', 'storage': '1f9f1', 'hang': '1f9f1',

            # Kitchen Items
            'spoon': '1f944', 'fork': '1f374', 'knife': '1f52a', 'plate': '1f37d', 'bowl': '1f963',
            'cup': '2615', 'mug': '2615', 'glass': '1f943', 'drink': '1f943', 'beverage': '1f943',
            'pot': '1f372', 'pan': '1f373', 'cook': '1f372', 'stove': '1f372', 'kitchen': '1f372',
            'oven': '1f372', 'bake': '1f372', 'heat': '1f525', 'cook': '1f372', 'appliance': '1f372',
            'refrigerator': '1f9ca', 'fridge': '1f9ca', 'cold': '1f976', 'food': '1f374', 'appliance': '1f9ca',
            'microwave': '1f372', 'heat': '1f525', 'quick': '26a1', 'appliance': '1f372', 'radiation': '1f372',
            'toaster': '1f35e', 'bread': '1f35e', 'toast': '1f35e', 'appliance': '1f35e', 'breakfast': '1f35e',
            'sink': '1f6b0', 'wash': '1f6b0', 'water': '1f4a7', 'clean': '1f6b0', 'dishes': '1f37d',
            'stove': '1f525', 'burner': '1f525', 'gas': '1f525', 'electric': '26a1', 'cook': '1f372',

            # Body Parts
            'head': '1f9d1', 'face': '1f9d1', 'hair': '1f9b1', 'forehead': '1f9d1', 'skull': '1f480',
            'eye': '1f441', 'eyes': '1f440', 'see': '1f441', 'vision': '1f441', 'look': '1f441',
            'nose': '1f443', 'smell': '1f443', 'sniff': '1f443', 'breathe': '1f443', 'nostrils': '1f443',
            'mouth': '1f444', 'lips': '1f444', 'speak': '1f444', 'eat': '1f374', 'kiss': '1f48b',
            'ear': '1f442', 'hear': '1f442', 'listen': '1f442', 'sound': '1f50a', 'deaf': '1f9cf',
            'tooth': '1f9b7', 'teeth': '1f9b7', 'bite': '1f9b7', 'chew': '1f9b7', 'smile': '1f604',
            'tongue': '1f445', 'taste': '1f445', 'lick': '1f445', 'speak': '1f445', 'pink': '1f445',
            'neck': '1f9d1', 'throat': '1f9d1', 'swallow': '1f9d1', 'turn': '1f9d1', 'collar': '1f9d1',
            'shoulder': '1f9d1', 'arm': '1f4aa', 'elbow': '1f4aa', 'wrist': '1f4aa', 'hand': '270b',
            'finger': '1f448', 'thumb': '1f44d', 'point': '1f448', 'touch': '270b', 'nail': '1f485',
            'chest': '1f9d1', 'heart': '2764', 'lungs': '1fac1', 'breathe': '1fac1', 'ribs': '1f9d1',
            'back': '1f9d1', 'spine': '1f9d1', 'shoulder blade': '1f9d1', 'posture': '1f9d1', 'support': '1f9d1',
            'stomach': '1f9d1', 'belly': '1f9d1', 'abdomen': '1f9d1', 'digest': '1f9d1', 'hungry': '1f37d',
            'leg': '1f9b5', 'thigh': '1f9b5', 'knee': '1f9b5', 'shin': '1f9b5', 'calf': '1f9b5',
            'foot': '1f9b6', 'toe': '1f9b6', 'heel': '1f9b6', 'ankle': '1f9b6', 'walk': '1f6b6',

            # Tools and Implements
            'hammer': '1f528', 'nail': '1f528', 'build': '1f528', 'hit': '1f528', 'tool': '1f527',
            'screwdriver': '1f527', 'screw': '1f527', 'turn': '1f527', 'fix': '1f527', 'tool': '1f527',
            'wrench': '1f527', 'bolt': '1f527', 'tighten': '1f527', 'loosen': '1f527', 'tool': '1f527',
            'saw': '1f527', 'cut': '1f527', 'wood': '1f527', 'teeth': '1f527', 'tool': '1f527',
            'drill': '1f527', 'hole': '1f527', 'bore': '1f527', 'power': '1f527', 'tool': '1f527',
            'pliers': '1f527', 'grip': '1f527', 'twist': '1f527', 'wire': '1f527', 'tool': '1f527',
            'shovel': '1f527', 'dig': '1f527', 'dirt': '1f527', 'garden': '1f527', 'tool': '1f527',
            'rake': '1f527', 'leaves': '1f341', 'gather': '1f527', 'garden': '1f527', 'tool': '1f527',
            'broom': '1f9f9', 'sweep': '1f9f9', 'clean': '1f9f9', 'bristles': '1f9f9', 'dust': '1f9f9',
            'mop': '1f9f9', 'clean': '1f9f9', 'floor': '1f9f9', 'wet': '1f4a7', 'bucket': '1faa3',

            # Abstract Concepts
            'love': '2764', 'heart': '2764', 'romance': '1f496', 'affection': '1f49e', 'care': '1f49d',
            'hate': '1f494', 'anger': '1f621', 'mad': '1f620', 'fury': '1f92c', 'rage': '1f621',
            'fear': '1f628', 'scared': '1f631', 'afraid': '1f630', 'terror': '1f631', 'phobia': '1f628',
            'joy': '1f604', 'happy': '1f604', 'glad': '1f60a', 'cheerful': '1f60a', 'delight': '1f604',
            'sadness': '1f622', 'cry': '1f622', 'tears': '1f622', 'sorrow': '1f614', 'grief': '1f622',
            'hope': '1f31f', 'wish': '1f31f', 'dream': '1f4ad', 'aspire': '1f31f', 'optimism': '1f31f',
            'peace': '262e', 'calm': '1f60c', 'quiet': '1f910', 'serene': '1f60c', 'tranquil': '1f60c',
            'war': '2694', 'battle': '2694', 'fight': '1f94a', 'conflict': '2694', 'violence': '2694',
            'truth': '2705', 'honest': '2705', 'fact': '2705', 'real': '2705', 'genuine': '2705',
            'lie': '274c', 'false': '274c', 'fake': '274c', 'deception': '274c', 'dishonest': '274c',
            'wisdom': '1f9e0', 'smart': '1f9e0', 'knowledge': '1f4da', 'intelligence': '1f9e0', 'clever': '1f9e0',
            'foolish': '1f921', 'silly': '1f921', 'stupid': '1f921', 'dumb': '1f921', 'ignorant': '1f921',

            # Natural Elements
            'fire': '1f525', 'flame': '1f525', 'burn': '1f525', 'hot': '1f525', 'heat': '1f525',
            'water': '1f4a7', 'liquid': '1f4a7', 'wet': '1f4a7', 'drop': '1f4a7', 'ocean': '1f30a',
            'air': '1f4a8', 'wind': '1f4a8', 'breeze': '1f4a8', 'breath': '1f4a8', 'atmosphere': '1f4a8',
            'earth': '1f30d', 'soil': '1f30d', 'dirt': '1f30d', 'ground': '1f30d', 'land': '1f30d',
            'ice': '1f9ca', 'frozen': '1f976', 'cold': '1f976', 'crystal': '1f9ca', 'solid': '1f9ca',
            'steam': '1f4a8', 'vapor': '1f4a8', 'mist': '1f32b', 'fog': '1f32b', 'cloud': '2601',
            'lightning': '26a1', 'thunder': '26a1', 'storm': '26c8', 'electric': '26a1', 'bolt': '26a1',
            'rain': '1f327', 'shower': '1f327', 'drizzle': '1f327', 'precipitation': '1f327', 'wet': '1f4a7',
            'snow': '2744', 'flake': '2744', 'white': '2744', 'winter': '2744', 'cold': '1f976',
            'sunshine': '2600', 'bright': '2600', 'warm': '2600', 'yellow': '1f7e1', 'daylight': '2600',

            # Size and Measurement
            'big': '1f4cf', 'large': '1f4cf', 'huge': '1f4cf', 'giant': '1f4cf', 'enormous': '1f4cf',
            'small': '1f4cf', 'tiny': '1f4cf', 'little': '1f4cf', 'miniature': '1f4cf', 'microscopic': '1f4cf',
            'tall': '1f4cf', 'high': '1f4cf', 'long': '1f4cf', 'extended': '1f4cf', 'lengthy': '1f4cf',
            'short': '1f4cf', 'low': '1f4cf', 'brief': '1f4cf', 'compact': '1f4cf', 'petite': '1f4cf',
            'wide': '1f4cf', 'broad': '1f4cf', 'thick': '1f4cf', 'fat': '1f4cf', 'bulky': '1f4cf',
            'narrow': '1f4cf', 'thin': '1f4cf', 'slim': '1f4cf', 'skinny': '1f4cf', 'lean': '1f4cf',
            'heavy': '1f4cf', 'weight': '1f4cf', 'dense': '1f4cf', 'massive': '1f4cf', 'solid': '1f4cf',
            'light': '1f4cf', 'weightless': '1f4cf', 'airy': '1f4cf', 'feather': '1fab6', 'float': '1f4cf',

            # Colors
            'red': '1f534', 'crimson': '1f534', 'scarlet': '1f534', 'cherry': '1f352', 'rose': '1f339',
            'blue': '1f535', 'navy': '1f535', 'azure': '1f535', 'cobalt': '1f535', 'sapphire': '1f48e',
            'green': '1f7e2', 'emerald': '1f7e2', 'lime': '1f7e2', 'forest': '1f7e2', 'mint': '1f7e2',
            'yellow': '1f7e1', 'gold': '1f7e1', 'lemon': '1f34b', 'banana': '1f34c', 'sunshine': '2600',
            'orange': '1f7e0', 'tangerine': '1f34a', 'peach': '1f351', 'amber': '1f7e0', 'rust': '1f7e0',
            'purple': '1f7e3', 'violet': '1f7e3', 'lavender': '1f7e3', 'plum': '1f7e3', 'grape': '1f347',
            'pink': '1f7e4', 'rose': '1f339', 'magenta': '1f7e4', 'fuchsia': '1f7e4', 'blush': '1f7e4',
            'brown': '1f7e4', 'tan': '1f7e4', 'beige': '1f7e4', 'chocolate': '1f36b', 'coffee': '2615',
            'black': '26ab', 'dark': '26ab', 'ebony': '26ab', 'coal': '26ab', 'midnight': '26ab',
            'white': '26aa', 'snow': '2744', 'ivory': '26aa', 'pearl': '26aa', 'cream': '26aa',
            'gray': '1f7e8', 'grey': '1f7e8', 'silver': '1f7e8', 'ash': '1f7e8', 'slate': '1f7e8',

            # Shapes
            'circle': '1f7e0', 'round': '1f7e0', 'ring': '1f48d', 'sphere': '1f7e0', 'ball': '26bd',
            'square': '1f7e9', 'box': '1f4e6', 'cube': '1f7e9', 'rectangle': '1f7e9', 'block': '1f9f1',
            'triangle': '1f53a', 'pyramid': '1f53a', 'arrow': '2b06', 'point': '1f53a', 'corner': '1f53a',
            'star': '2b50', 'pointed': '2b50', 'celestial': '2b50', 'twinkle': '2728', 'shine': '2728',
            'heart': '2764', 'love': '2764', 'valentine': '1f496', 'romance': '1f496', 'affection': '1f49e',
            'diamond': '1f48e', 'gem': '1f48e', 'jewel': '1f48e', 'precious': '1f48e', 'sparkle': '2728',
            'oval': '1f7e0', 'ellipse': '1f7e0', 'egg': '1f95a', 'elongated': '1f7e0', 'stretched': '1f7e0',
            'line': '2796', 'straight': '2796', 'row': '2796', 'stripe': '2796', 'dash': '2796',
            'curve': '27b0', 'bend': '27b0', 'arc': '27b0', 'wave': '1f30a', 'spiral': '1f300',
            'angle': '1f4d0', 'corner': '1f4d0', 'bend': '1f4d0', 'turn': '1f4d0', 'sharp': '1f4d0',

            # Common academic and quiz terms (ENHANCED)
            'synonym': '1f4dd', 'antonym': '1f4dd', 'meaning': '1f4dd', 'definition': '1f4dd', 'word': '1f4dd',
            'language': '1f5e3', 'vocabulary': '1f5e3', 'grammar': '1f5e3', 'speech': '1f5e3', 'communication': '1f5e3',
            'question': '2753', 'ask': '2753', 'inquiry': '2753', 'quiz': '2753', 'test': '2753',
            'answer': '2705', 'solution': '2705', 'response': '2705', 'reply': '2705', 'result': '2705',
            'correct': '2714', 'right': '2714', 'accurate': '2714', 'true': '2714', 'proper': '2714',
            'wrong': '274c', 'incorrect': '274c', 'false': '274c', 'mistake': '274c', 'error': '274c',
            'yes': '2705', 'agree': '2705', 'positive': '2705', 'affirmative': '2705', 'confirm': '2705',
            'no': '274c', 'disagree': '274c', 'negative': '274c', 'deny': '274c', 'refuse': '274c',
            'good': '1f44d', 'great': '1f44d', 'excellent': '1f44d', 'wonderful': '1f44d', 'amazing': '1f44d',
            'bad': '1f44e', 'poor': '1f44e', 'terrible': '1f44e', 'awful': '1f44e', 'horrible': '1f44e',
            'best': '1f947', 'winner': '1f947', 'champion': '1f947', 'top': '1f947', 'first': '1f947',
            'worst': '1f4a9', 'loser': '1f4a9', 'last': '1f4a9', 'bottom': '1f4a9', 'terrible': '1f4a9',
            'new': '1f195', 'fresh': '1f195', 'recent': '1f195', 'modern': '1f195', 'latest': '1f195',
            'old': '1f4c5', 'ancient': '1f4c5', 'vintage': '1f4c5', 'aged': '1f4c5', 'antique': '1f4c5',
            'easy': '1f44d', 'simple': '1f44d', 'basic': '1f44d', 'effortless': '1f44d', 'straightforward': '1f44d',
            'hard': '1f4aa', 'difficult': '1f4aa', 'tough': '1f4aa', 'challenging': '1f4aa', 'complex': '1f4aa',
            'important': '2757', 'significant': '2757', 'crucial': '2757', 'vital': '2757', 'essential': '2757',
            'unimportant': '1f6ab', 'insignificant': '1f6ab', 'trivial': '1f6ab', 'minor': '1f6ab',
            'negligible': '1f6ab',
            'interesting': '1f914', 'fascinating': '1f914', 'engaging': '1f914', 'intriguing': '1f914',
            'captivating': '1f914',
            'boring': '1f634', 'dull': '1f634', 'tedious': '1f634', 'uninteresting': '1f634', 'monotonous': '1f634',
            'beautiful': '1f338', 'pretty': '1f338', 'lovely': '1f338', 'gorgeous': '1f338', 'attractive': '1f338',
            'ugly': '1f4a9', 'hideous': '1f4a9', 'unattractive': '1f4a9', 'repulsive': '1f4a9', 'unsightly': '1f4a9',
            'clean': '1f9fd', 'pure': '1f9fd', 'spotless': '1f9fd', 'pristine': '1f9fd', 'sanitary': '1f9fd',
            'dirty': '1f4a9', 'filthy': '1f4a9', 'messy': '1f4a9', 'unclean': '1f4a9', 'contaminated': '1f4a9',
            'safe': '1f6e1', 'secure': '1f6e1', 'protected': '1f6e1', 'harmless': '1f6e1', 'risk-free': '1f6e1',
            'dangerous': '26a0', 'risky': '26a0', 'hazardous': '26a0', 'unsafe': '26a0', 'threatening': '26a0',
            'helpful': '1f91d', 'useful': '1f91d', 'beneficial': '1f91d', 'supportive': '1f91d', 'valuable': '1f91d',
            'harmful': '26a0', 'damaging': '26a0', 'destructive': '26a0', 'detrimental': '26a0', 'toxic': '26a0',
            'possible': '2705', 'feasible': '2705', 'achievable': '2705', 'doable': '2705', 'realistic': '2705',
            'impossible': '274c', 'unfeasible': '274c', 'unachievable': '274c', 'unrealistic': '274c',
            'hopeless': '274c',
            'real': '2705', 'actual': '2705', 'genuine': '2705', 'authentic': '2705', 'true': '2705',
            'fake': '274c', 'false': '274c', 'artificial': '274c', 'counterfeit': '274c', 'imitation': '274c',
        }

        # Country famous associations for better matching
        self.country_associations = {
            'usa': ['freedom', 'eagle', 'hamburger', 'baseball', 'hollywood'],
            'canada': ['maple', 'hockey', 'cold', 'polite', 'mountains'],
            'mexico': ['tacos', 'sombrero', 'spicy', 'mariachi', 'desert'],
            'brazil': ['soccer', 'carnival', 'amazon', 'coffee', 'beach'],
            'france': ['wine', 'cheese', 'fashion', 'art', 'romance'],
            'italy': ['pizza', 'pasta', 'art', 'fashion', 'history'],
            'germany': ['beer', 'cars', 'engineering', 'precision', 'oktoberfest'],
            'japan': ['sushi', 'technology', 'anime', 'cherry blossoms', 'honor'],
            'china': ['dragon', 'panda', 'tea', 'great wall', 'martial arts'],
            'india': ['curry', 'spices', 'elephant', 'yoga', 'bollywood'],
            'australia': ['kangaroo', 'koala', 'surfing', 'outback', 'barbecue'],
            'russia': ['vodka', 'bear', 'cold', 'ballet', 'space'],
            'egypt': ['pyramid', 'pharaoh', 'desert', 'nile', 'ancient'],
            'greece': ['olives', 'philosophy', 'islands', 'democracy', 'mythology'],
            'spain': ['flamenco', 'bull', 'paella', 'siesta', 'passion'],
            'thailand': ['elephant', 'spicy', 'temple', 'smile', 'tropical'],
            'philippines': ['islands', 'coconut', 'rice', 'tropical', 'friendly'],
        }

        # COMPREHENSIVE CITY ASSOCIATIONS - What each city is famous for according to human feelings
        self.city_associations = {
            # North America
            'new york': ['🗽', 'skyscraper', 'broadway', 'central park', 'statue of liberty', 'pizza', 'taxi',
                         'finance'],
            'nyc': ['🗽', 'skyscraper', 'broadway', 'central park', 'statue of liberty', 'pizza', 'taxi', 'finance'],
            'los angeles': ['🌴', 'hollywood', 'movies', 'beach', 'palm trees', 'celebrities', 'sunshine', 'cars'],
            'la': ['🌴', 'hollywood', 'movies', 'beach', 'palm trees', 'celebrities', 'sunshine', 'cars'],
            'hollywood': ['🎬', 'movies', 'stars', 'cinema', 'entertainment', 'glamour', 'celebrities', 'films'],
            'chicago': ['🌬️', 'deep dish pizza', 'wind', 'architecture', 'blues music', 'cold', 'skyscrapers'],
            'miami': ['🌴', 'beach', 'art deco', 'nightlife', 'sunshine', 'tropical', 'ocean', 'vacation'],
            'las vegas': ['🎰', 'casino', 'gambling', 'shows', 'entertainment', 'desert', 'lights', 'money'],
            'vegas': ['🎰', 'casino', 'gambling', 'shows', 'entertainment', 'desert', 'lights', 'money'],
            'san francisco': ['🌉', 'golden gate bridge', 'hills', 'technology', 'silicon valley', 'fog', 'cable cars'],
            'toronto': ['🏒', 'hockey', 'cn tower', 'cold', 'maple leaf', 'multicultural', 'friendly', 'snow'],
            'vancouver': ['🏔️', 'mountains', 'rain', 'nature', 'ocean', 'green', 'beautiful', 'outdoor'],
            'mexico city': ['🌮', 'aztec', 'tacos', 'culture', 'history', 'spicy', 'colorful', 'ancient'],
            'seattle': ['☕', 'coffee', 'rain', 'grunge', 'mountains', 'tech', 'microsoft', 'boeing'],
            'boston': ['⚾', 'history', 'harvard', 'tea party', 'freedom trail', 'seafood', 'education', 'sports'],
            'philadelphia': ['🔔', 'liberty bell', 'cheesesteaks', 'history', 'independence', 'rocky', 'brotherly love'],
            'washington dc': ['🏛️', 'politics', 'white house', 'capitol', 'government', 'monuments', 'cherry blossoms'],
            'denver': ['🏔️', 'mountains', 'skiing', 'outdoors', 'mile high', 'colorado', 'cannabis', 'sports'],
            'phoenix': ['🌵', 'desert', 'hot', 'cactus', 'golf', 'retirement', 'sunshine', 'sprawl'],
            'atlanta': ['🍑', 'peaches', 'southern', 'airport', 'coca cola', 'hip hop', 'civil rights', 'hot'],
            'orlando': ['🏰', 'disney world', 'theme parks', 'tourism', 'family', 'magic', 'entertainment', 'florida'],
            'nashville': ['🎸', 'country music', 'honky tonk', 'grand ole opry', 'southern', 'music city', 'cowboys'],
            'new orleans': ['🎺', 'jazz', 'mardi gras', 'cajun food', 'french quarter', 'party', 'culture', 'bourbon'],
            'dallas': ['🤠', 'cowboys', 'oil', 'big', 'texas', 'barbecue', 'business', 'football'],
            'houston': ['🚀', 'space center', 'nasa', 'oil', 'texas', 'humidity', 'energy', 'diversity'],
            'san antonio': ['🏛️', 'alamo', 'river walk', 'tex mex', 'history', 'fiesta', 'mexican culture', 'warm'],
            'austin': ['🎸', 'music', 'keep it weird', 'tech', 'bbq', 'festivals', 'university', 'hipster'],
            'salt lake city': ['⛷️', 'skiing', 'mormons', 'mountains', 'clean', 'family', 'outdoor', 'snow'],
            'portland': ['☕', 'hipster', 'rain', 'food trucks', 'craft beer', 'weird', 'liberal', 'green'],

            # Canada (additional cities)
            'montreal': ['🥐', 'french', 'poutine', 'culture', 'festivals', 'old world', 'bagels', 'bilingual'],
            'calgary': ['🤠', 'stampede', 'oil', 'cowboys', 'mountains', 'western', 'rodeo', 'energy'],
            'ottawa': ['🍁', 'parliament', 'government', 'tulips', 'canal', 'politics', 'bilingual', 'peaceful'],
            'quebec city': ['🏰', 'old world', 'french', 'cobblestone', 'winter carnival', 'fortress', 'european'],
            'winnipeg': ['❄️', 'cold', 'prairie', 'hockey', 'mosquitoes', 'ballet', 'arts', 'central'],
            'halifax': ['🦞', 'maritime', 'lobster', 'ocean', 'friendly', 'fishing', 'lighthouse', 'eastern'],

            # Europe
            'london': ['🫖', 'tea', 'big ben', 'royal', 'rain', 'red buses', 'queen', 'parliament'],
            'paris': ['🗼', 'eiffel tower', 'romance', 'art', 'fashion', 'wine', 'cheese', 'love'],
            'rome': ['🏛️', 'colosseum', 'history', 'ancient', 'pasta', 'vatican', 'gladiator', 'empire'],
            'barcelona': ['⚽', 'soccer', 'gaudi', 'architecture', 'beach', 'art', 'football', 'mediterranean'],
            'madrid': ['⚽', 'soccer', 'real madrid', 'art', 'museums', 'siesta', 'flamenco', 'culture'],
            'berlin': ['🧱', 'history', 'wall', 'beer', 'techno', 'war', 'freedom', 'culture'],
            'amsterdam': ['🌷', 'tulips', 'bicycles', 'canals', 'cheese', 'liberal', 'art', 'museums'],
            'vienna': ['🎼', 'classical music', 'mozart', 'coffee houses', 'waltz', 'culture', 'art', 'history'],
            'prague': ['🏰', 'fairy tale', 'castle', 'beer', 'beautiful', 'medieval', 'architecture', 'romantic'],
            'stockholm': ['❄️', 'cold', 'northern lights', 'vikings', 'design', 'snow', 'blonde', 'innovative'],
            'oslo': ['❄️', 'fjords', 'cold', 'northern lights', 'vikings', 'snow', 'nature', 'peace'],
            'copenhagen': ['🧜‍♀️', 'little mermaid', 'design', 'hygge', 'bicycles', 'happiness', 'scandinavia', 'cozy'],
            'zurich': ['💰', 'banks', 'money', 'swiss', 'mountains', 'chocolate', 'expensive', 'precision'],
            'geneva': ['💰', 'banks', 'international', 'diplomacy', 'expensive', 'lake', 'peace', 'mountains'],
            'dublin': ['🍀', 'irish', 'green', 'beer', 'leprechaun', 'rain', 'music', 'friendly'],
            'edinburgh': ['🏰', 'castle', 'scottish', 'kilts', 'whisky', 'bagpipes', 'highland', 'history'],
            'athens': ['🏛️', 'ancient', 'philosophy', 'democracy', 'acropolis', 'mythology', 'history', 'greece'],
            'istanbul': ['🕌', 'mosque', 'bridge', 'europe asia', 'culture', 'history', 'bosphorus', 'spices'],
            'moscow': ['🏰', 'kremlin', 'red square', 'cold', 'vodka', 'communism', 'bears', 'snow'],
            'st petersburg': ['❄️', 'palace', 'winter', 'art', 'culture', 'hermitage', 'russian', 'beautiful'],

            # Asia
            'tokyo': ['🗼', 'technology', 'anime', 'sushi', 'neon', 'crowded', 'modern', 'cherry blossoms'],
            'kyoto': ['🌸', 'cherry blossoms', 'temples', 'traditional', 'geisha', 'culture', 'peaceful', 'spiritual'],
            'osaka': ['🍜', 'food', 'takoyaki', 'cuisine', 'street food', 'delicious', 'friendly', 'castle'],
            'seoul': ['📱', 'technology', 'kpop', 'kimchi', 'modern', 'korean bbq', 'fashion', 'gaming'],
            'beijing': ['🏯', 'forbidden city', 'great wall', 'history', 'communism', 'pollution', 'ancient', 'power'],
            'shanghai': ['🏙️', 'skyscrapers', 'modern', 'business', 'future', 'growth', 'economy', 'bustling'],
            'hong kong': ['🏙️', 'skyscrapers', 'dim sum', 'finance', 'harbor', 'shopping', 'dense', 'international'],
            'singapore': ['🏙️', 'clean', 'modern', 'food', 'multicultural', 'efficient', 'garden city', 'strict'],
            'bangkok': ['🐘', 'temples', 'spicy food', 'tuk tuk', 'street food', 'golden', 'massage', 'tropical'],
            'mumbai': ['🏭', 'bollywood', 'crowds', 'slums', 'business', 'spicy', 'monsoon', 'chaotic'],
            'delhi': ['🕌', 'curry', 'spices', 'history', 'pollution', 'crowded', 'street food', 'culture'],
            'kolkata': ['📚', 'literature', 'culture', 'poverty', 'mother teresa', 'intellectual', 'art', 'fish'],
            'chennai': ['🌴', 'south indian', 'temple', 'classical music', 'dosa', 'hot weather', 'culture',
                        'technology'],
            'bangalore': ['💻', 'technology', 'software', 'silicon valley', 'gardens', 'pleasant weather', 'innovation'],
            'hyderabad': ['💎', 'pearls', 'biryani', 'technology', 'history', 'nizams', 'spices', 'it hub'],
            'pune': ['🎓', 'education', 'software', 'young', 'weather', 'cultural', 'IT', 'students'],
            'karachi': ['🌊', 'port', 'sea', 'business', 'crowded', 'street food', 'culture', 'commerce'],
            'lahore': ['🕌', 'culture', 'food', 'history', 'gardens', 'mughal', 'art', 'poetry'],
            'islamabad': ['🏛️', 'capital', 'planned', 'government', 'mountains', 'green', 'modern', 'peaceful'],
            'dhaka': ['🚣', 'rickshaw', 'crowded', 'rivers', 'textiles', 'monsoon', 'bengali', 'fish'],
            'colombo': ['🌴', 'tea', 'cricket', 'spices', 'tropical', 'beach', 'buddhist', 'gemstones'],
            'kathmandu': ['🏔️', 'mountains', 'everest', 'temples', 'trekking', 'spirituality', 'himalaya', 'peace'],

            # Middle East (expanded)
            'dubai': ['🏗️', 'skyscrapers', 'luxury', 'gold', 'shopping', 'desert', 'modern', 'wealth'],
            'abu dhabi': ['🛢️', 'oil', 'wealth', 'mosque', 'luxury', 'desert', 'modern', 'grand'],
            'riyadh': ['🛢️', 'oil', 'desert', 'conservative', 'wealth', 'sand', 'traditional', 'monarchy'],
            'doha': ['⚽', 'world cup', 'wealth', 'modern', 'desert', 'luxury', 'qatar', 'football'],
            'kuwait city': ['🛢️', 'oil', 'wealth', 'desert', 'hot', 'towers', 'finance', 'gulf'],
            'manama': ['💰', 'finance', 'banking', 'pearls', 'gulf', 'modern', 'business', 'small'],
            'muscat': ['🏔️', 'mountains', 'frankincense', 'tradition', 'sultan', 'oman', 'beautiful', 'peaceful'],
            'tehran': ['🏔️', 'mountains', 'carpets', 'history', 'persia', 'culture', 'pollution', 'ancient'],
            'isfahan': ['🕌', 'half the world', 'beautiful', 'persian', 'architecture', 'culture', 'traditional', 'art'],
            'shiraz': ['🍷', 'poetry', 'roses', 'wine', 'culture', 'hafez', 'gardens', 'literature'],
            'jerusalem': ['🕌', 'religious', 'holy', 'history', 'conflict', 'pilgrimage', 'ancient', 'sacred'],
            'tel aviv': ['🏖️', 'beach', 'technology', 'nightlife', 'startup', 'modern', 'liberal', 'innovation'],
            'beirut': ['🌊', 'mediterranean', 'nightlife', 'culture', 'phoenician', 'cuisine', 'cosmopolitan'],
            'damascus': ['🏛️', 'ancient', 'history', 'oldest city', 'culture', 'war', 'traditional', 'syria'],
            'baghdad': ['🏛️', 'ancient', 'mesopotamia', 'history', 'conflict', 'tigris', 'culture', 'cradle'],
            'amman': ['🏛️', 'ancient', 'jordan', 'desert', 'roman', 'middle eastern', 'culture', 'hills'],
            'jeddah': ['🕌', 'mecca gateway', 'pilgrimage', 'red sea', 'traditional', 'religious', 'port', 'historic'],

            # Africa (expanded)
            'cairo': ['🏜️', 'pyramids', 'ancient', 'nile', 'history', 'pharaohs', 'desert', 'sphinx'],
            'cape town': ['🏔️', 'table mountain', 'wine', 'beautiful', 'apartheid', 'penguins', 'scenery',
                          'rainbow nation'],
            'johannesburg': ['💎', 'gold', 'mining', 'apartheid', 'crime', 'business', 'mandela', 'wealth'],
            'durban': ['🏖️', 'beach', 'indian', 'curry', 'surfing', 'sharks', 'warm', 'subtropical'],
            'lagos': ['🌊', 'nollywood', 'crowded', 'business', 'oil', 'traffic', 'music', 'energy'],
            'casablanca': ['🎬', 'movie', 'romance', 'morocco', 'hassan mosque', 'business', 'atlantic', 'culture'],
            'marrakech': ['🏺', 'souks', 'red city', 'riads', 'berber', 'spices', 'desert', 'traditional'],
            'tunis': ['🏛️', 'carthage', 'ancient', 'mediterranean', 'culture', 'history', 'arab', 'revolution'],
            'algiers': ['🌊', 'white city', 'mediterranean', 'french', 'casbah', 'berber', 'oil', 'culture'],
            'addis ababa': ['☕', 'coffee', 'highlands', 'ethiopia', 'lucy', 'african union', 'culture', 'history'],
            'nairobi': ['🦁', 'safari', 'wildlife', 'national park', 'business hub', 'kenya', 'maasai', 'adventure'],
            'dar es salaam': ['🌊', 'swahili', 'port', 'tanzania', 'indian ocean', 'trade', 'culture', 'relaxed'],
            'kampala': ['🌍', 'seven hills', 'uganda', 'friendly', 'bananas', 'source of nile', 'green', 'culture'],
            'kigali': ['🏔️', 'clean', 'rwanda', 'genocide memorial', 'hills', 'progress', 'organized', 'healing'],
            'accra': ['🥁', 'drums', 'ghana', 'gold coast', 'friendly', 'music', 'cocoa', 'democracy'],
            'alexandria': ['📚', 'library', 'ancient', 'lighthouse', 'mediterranean', 'history', 'learning', 'culture'],
            'luxor': ['🏺', 'pharaohs', 'temples', 'ancient', 'valley of kings', 'tombs', 'history', 'monuments'],
            'fez': ['🏺', 'medina', 'leather', 'traditional', 'maze', 'culture', 'artisans', 'ancient'],
            'rabat': ['🏛️', 'capital', 'administrative', 'hassan tower', 'planned', 'government', 'ocean', 'modern'],
            'abidjan': ['🏙️', 'economic capital', 'ivory coast', 'business', 'modern', 'cocoa', 'french', 'tropical'],
            'dakar': ['🌊', 'westernmost', 'senegal', 'music', 'peanuts', 'port', 'french', 'colorful'],
            'kinshasa': ['🌍', 'congo river', 'music', 'diamonds', 'chaos', 'large', 'central africa', 'vibrant'],

            # South America (expanded)
            'rio de janeiro': ['🏖️', 'carnival', 'beach', 'christ statue', 'samba', 'copacabana', 'party', 'mountains'],
            'rio': ['🏖️', 'carnival', 'beach', 'christ statue', 'samba', 'copacabana', 'party', 'mountains'],
            'sao paulo': ['🏙️', 'business', 'largest city', 'coffee', 'immigration', 'traffic', 'skyscrapers',
                          'diverse'],
            'buenos aires': ['💃', 'tango', 'steak', 'european', 'soccer', 'passion', 'wine', 'culture'],
            'lima': ['🍃', 'ceviche', 'inca', 'machu picchu', 'cuisine', 'history', 'coast', 'culture'],
            'bogota': ['☕', 'coffee', 'emeralds', 'high altitude', 'culture', 'museums', 'cold', 'mountains'],
            'santiago': ['🏔️', 'andes', 'wine', 'mountains', 'earthquakes', 'modern', 'snow', 'business'],
            'caracas': ['🛢️', 'oil', 'mountains', 'venezuela', 'crime', 'socialism', 'beautiful women', 'chaos'],
            'quito': ['🏔️', 'equator', 'colonial', 'high altitude', 'volcanoes', 'history', 'architecture', 'culture'],
            'la paz': ['🏔️', 'highest capital', 'bolivia', 'indigenous', 'andes', 'altitude', 'llamas', 'traditional'],
            'montevideo': ['🏖️', 'beach', 'small', 'uruguay', 'peaceful', 'tango', 'soccer', 'relaxed'],
            'asuncion': ['🌴', 'paraguay', 'guarani', 'landlocked', 'traditional', 'calm', 'culture', 'river'],
            'medellin': ['🌺', 'flowers', 'transformation', 'pablo escobar', 'eternal spring', 'innovation', 'culture'],
            'cali': ['💃', 'salsa', 'dancing', 'colombia', 'warm', 'music', 'party', 'sugar cane'],
            'cartagena': ['🏰', 'colonial', 'walled city', 'caribbean', 'romantic', 'pirates', 'colorful', 'historic'],
            'cusco': ['🏔️', 'inca', 'machu picchu gateway', 'altitude', 'llamas', 'indigenous', 'sacred valley'],
            'salvador': ['🥁', 'afro brazilian', 'carnival', 'capoeira', 'music', 'pelourinho', 'colorful', 'culture'],
            'brasilia': ['🏛️', 'planned city', 'modern', 'government', 'architecture', 'artificial', 'political'],
            'fortaleza': ['🏖️', 'beaches', 'northeastern brazil', 'seafood', 'dunes', 'caipirinha', 'tropical'],

            # Oceania (expanded)
            'sydney': ['🏄', 'opera house', 'harbor', 'bridge', 'surfing', 'beach', 'beautiful', 'koalas'],
            'melbourne': ['☕', 'coffee', 'culture', 'sports', 'fashion', 'laneways', 'arts', 'liveable'],
            'brisbane': ['🌞', 'sunshine', 'relaxed', 'river', 'warm', 'friendly', 'subtropical', 'outdoor'],
            'perth': ['🌊', 'isolated', 'mining', 'beaches', 'western', 'sunset', 'expensive', 'remote'],
            'adelaide': ['🍷', 'wine', 'festivals', 'churches', 'planned', 'culture', 'food', 'quiet'],
            'auckland': ['🌋', 'volcanoes', 'harbor', 'sailing', 'maori', 'sheep', 'rugby', 'beautiful'],
            'wellington': ['💨', 'windy', 'capital', 'culture', 'government', 'creative', 'hills', 'coffee'],
            'christchurch': ['🌸', 'garden city', 'earthquake', 'english', 'rebuild', 'flat', 'cathedral', 'peaceful'],
            'gold coast': ['🏄', 'surfing', 'theme parks', 'beaches', 'high rises', 'tourism', 'party', 'sunshine'],
            'canberra': ['🏛️', 'capital', 'politics', 'planned', 'government', 'quiet', 'lake', 'institutions'],
            'hobart': ['🐨', 'tasmania', 'mona', 'devils', 'apple isle', 'mountains', 'cool', 'quirky'],
            'darwin': ['🐊', 'crocodiles', 'tropical', 'northern', 'outback', 'hot', 'multicultural', 'frontier'],
        }

        # Semantic concept groups for better human-feeling matching
        self.semantic_groups = {
            'wealth_prosperity': {
                'keywords': ['rich', 'wealthy', 'expensive', 'luxury', 'premium', 'costly', 'valuable', 'precious'],
                'emoji': '1f4b0'  # money bag
            },
            'freshness_vitality': {
                'keywords': ['fresh', 'new', 'clean', 'pure', 'crisp', 'vibrant', 'lively', 'energetic'],
                'emoji': '1f34e'  # apple (represents freshness)
            },
            'direction_movement': {
                'keywords': ['up', 'rise', 'increase', 'grow', 'ascend', 'climb', 'elevate', 'boost'],
                'emoji': '2b06'  # up arrow
            },
            'speed_urgency': {
                'keywords': ['fast', 'quick', 'rapid', 'swift', 'speedy', 'urgent', 'immediate', 'instant'],
                'emoji': '26a1'  # lightning bolt
            },
            'intelligence_clarity': {
                'keywords': ['smart', 'clever', 'bright', 'clear', 'obvious', 'intelligent', 'brilliant', 'wise'],
                'emoji': '1f4a1'  # light bulb
            },
            'strength_power': {
                'keywords': ['strong', 'powerful', 'mighty', 'robust', 'tough', 'solid', 'firm', 'sturdy'],
                'emoji': '1f4aa'  # flexed bicep
            },
            'beauty_elegance': {
                'keywords': ['beautiful', 'pretty', 'elegant', 'graceful', 'lovely', 'gorgeous', 'stunning',
                             'attractive'],
                'emoji': '1f338'  # cherry blossom
            },
            'danger_warning': {
                'keywords': ['dangerous', 'risky', 'hazardous', 'unsafe', 'threatening', 'perilous', 'harmful',
                             'toxic'],
                'emoji': '26a0'  # warning sign
            }
        }

        # Emotional response mapping - how humans FEEL when reading words
        self.emotional_responses = {
            # Positive feelings
            'achievement': '1f3c6', 'success': '1f3c6', 'victory': '1f3c6', 'win': '1f3c6',
            'celebration': '1f389', 'party': '1f389', 'festive': '1f389', 'joy': '1f389',
            'surprise': '1f381', 'gift': '1f381', 'present': '1f381', 'bonus': '1f381',
            'adventure': '1f5fa', 'explore': '1f5fa', 'journey': '1f5fa', 'discover': '1f5fa',
            'comfort': '1f6cb', 'cozy': '1f6cb', 'relaxing': '1f6cb', 'peaceful': '1f6cb',
            'excitement': '1f3a2', 'thrilling': '1f3a2', 'amazing': '1f3a2', 'awesome': '1f3a2',

            # Negative feelings
            'frustration': '1f624', 'annoying': '1f624', 'irritating': '1f624', 'bothersome': '1f624',
            'sadness': '1f622', 'disappointing': '1f622', 'heartbreaking': '1f622', 'tragic': '1f622',
            'fear': '1f628', 'scary': '1f628', 'frightening': '1f628', 'terrifying': '1f628',
            'confusion': '1f615', 'puzzling': '1f615', 'mysterious': '1f615', 'unclear': '1f615',

            # Neutral but meaningful feelings
            'curiosity': '1f914', 'interesting': '1f914', 'wondering': '1f914', 'questioning': '1f914',
            'determination': '1f4aa', 'motivated': '1f4aa', 'focused': '1f4aa', 'driven': '1f4aa',
            'nostalgia': '1f4f7', 'memories': '1f4f7', 'reminiscing': '1f4f7', 'past': '1f4f7',
            'hope': '1f31f', 'optimistic': '1f31f', 'positive': '1f31f', 'bright future': '1f31f',
        }

        # Comprehensive country flag mapping - HIGHEST PRIORITY for countries

    def debug_log(self, message):
        """Print debug messages if debug mode is enabled"""
        if self.debug_mode:
            print(f"🔍 DEBUG: {message}")

    def reset_quiz_emoji_tracking(self):
        """Reset emoji tracking for new quiz/video"""
        self.used_emojis_in_quiz.clear()
        # Don't clear the entire emoji directory, just reset the tracking
        # This allows reuse of downloaded emojis while ensuring fresh selection
        self.debug_log("Reset emoji tracking for new quiz - cleared used emoji tracking")

    def get_emoji_style_for_question(self, question_index):
        """Randomly select emoji style for a question"""
        # Use question index as seed for consistency within same question
        import random
        random.seed(question_index)
        selected_style = random.choice(self.emoji_styles)
        # Reset random seed to current time for true randomness
        random.seed(None)
        print(f"🎭 Selected {selected_style} emoji style for question {question_index}")
        return selected_style

    def search_by_compound_words(self, text):
        """STEP 0: ULTIMATE RULE - Search for complete compound words first"""
        # Clean the text for comparison
        clean_text = re.sub(r'[^\w\s]', '', text.lower().strip())

        self.debug_log(f"🎯 STEP 0: COMPOUND WORD SEARCH for: '{clean_text}'")

        # Check for exact match of the entire phrase first
        if clean_text in self.emoji_mapping:
            emoji_code = self.emoji_mapping[clean_text]
            if emoji_code not in self.used_emojis_in_quiz:
                self.used_emojis_in_quiz.add(emoji_code)
                self.debug_log(f"✅ EXACT COMPOUND MATCH: '{clean_text}' -> {emoji_code}")
                return emoji_code

        # Check for partial compound matches (e.g., "blue whale" vs "blue whales")
        for compound_key in self.emoji_mapping:
            if len(compound_key.split()) > 1:  # Only check compound words (multiple words)
                # Check if the compound key is contained in the text or vice versa
                if (compound_key in clean_text) or (clean_text in compound_key):
                    emoji_code = self.emoji_mapping[compound_key]
                    if emoji_code not in self.used_emojis_in_quiz:
                        self.used_emojis_in_quiz.add(emoji_code)
                        self.debug_log(f"✅ PARTIAL COMPOUND MATCH: '{clean_text}' -> '{compound_key}' -> {emoji_code}")
                        return emoji_code

        self.debug_log("❌ No compound word matches found")
        return None

    def search_by_country_flags(self, words, original_text):
        """STEP 0.5: HIGHEST PRIORITY - Search for country names and return flag emojis"""
        clean_text = re.sub(r'[^\w\s]', '', original_text.lower().strip())

        self.debug_log(f"🏳️ STEP 0.5: COUNTRY FLAG SEARCH for: '{clean_text}'")

        # Check for exact match of the entire phrase first (for multi-word countries)
        if clean_text in self.country_flags:
            emoji_code = self.country_flags[clean_text]
            if emoji_code not in self.used_emojis_in_quiz:
                self.used_emojis_in_quiz.add(emoji_code)
                self.debug_log(f"✅ EXACT COUNTRY MATCH: '{clean_text}' -> {emoji_code} (flag)")
                return emoji_code

        # Check for partial matches with multi-word country names
        for country_name in self.country_flags:
            if len(country_name.split()) > 1:  # Multi-word countries like "United States"
                if (country_name in clean_text) or (clean_text in country_name):
                    emoji_code = self.country_flags[country_name]
                    if emoji_code not in self.used_emojis_in_quiz:
                        self.used_emojis_in_quiz.add(emoji_code)
                        self.debug_log(
                            f"✅ PARTIAL COUNTRY MATCH: '{clean_text}' -> '{country_name}' -> {emoji_code} (flag)")
                        return emoji_code

        # Check individual words for country names
        for word in words:
            if word in self.country_flags:
                emoji_code = self.country_flags[word]
                if emoji_code not in self.used_emojis_in_quiz:
                    self.used_emojis_in_quiz.add(emoji_code)
                    self.debug_log(f"✅ INDIVIDUAL COUNTRY WORD MATCH: '{word}' -> {emoji_code} (flag)")
                    return emoji_code

        # Check for partial word matches (e.g., "american" contains "america")
        for word in words:
            if len(word) > 3:  # Only for longer words
                for country_name in self.country_flags:
                    if len(country_name.split()) == 1:  # Only single-word countries for partial matching
                        if (country_name in word) or (word in country_name):
                            emoji_code = self.country_flags[country_name]
                            if emoji_code not in self.used_emojis_in_quiz:
                                self.used_emojis_in_quiz.add(emoji_code)
                                self.debug_log(
                                    f"✅ PARTIAL COUNTRY WORD MATCH: '{word}' -> '{country_name}' -> {emoji_code} (flag)")
                                return emoji_code

        self.debug_log("❌ No country flag matches found")
        return None

    def search_by_literal_match(self, words):
        """STEP 1: Try exact literal matches for individual words"""
        self.debug_log(f"🎯 STEP 1: Searching for individual word matches in: {words}")

        # Try exact word matches first
        for word in words:
            if word in self.emoji_mapping:
                emoji_code = self.emoji_mapping[word]
                if emoji_code not in self.used_emojis_in_quiz:
                    self.used_emojis_in_quiz.add(emoji_code)
                    self.debug_log(f"✅ INDIVIDUAL WORD MATCH: '{word}' -> {emoji_code}")
                    return emoji_code

        # Try partial matches for longer words
        for word in words:
            if len(word) > 3:  # Only for longer words
                for key in self.emoji_mapping:
                    if len(key.split()) == 1:  # Only check individual words here
                        if word in key or key in word:
                            emoji_code = self.emoji_mapping[key]
                            if emoji_code not in self.used_emojis_in_quiz:
                                self.used_emojis_in_quiz.add(emoji_code)
                                self.debug_log(f"✅ PARTIAL INDIVIDUAL WORD MATCH: '{word}' -> '{key}' -> {emoji_code}")
                                return emoji_code

        self.debug_log("❌ No individual word matches found")
        return None

    def search_by_city_fame(self, words):
        """STEP 1.5: NEW - For cities, search what they're famous for according to human feelings"""
        self.debug_log(f"🏙️ STEP 1.5: CITY FAME SEARCH - Searching for city associations in: {words}")

        # Join words to check for multi-word city names
        text_combinations = []

        # Single words
        text_combinations.extend(words)

        # Two-word combinations
        for i in range(len(words) - 1):
            two_word = f"{words[i]} {words[i + 1]}"
            text_combinations.append(two_word)

        # Three-word combinations
        for i in range(len(words) - 2):
            three_word = f"{words[i]} {words[i + 1]} {words[i + 2]}"
            text_combinations.append(three_word)

        for text_combo in text_combinations:
            if text_combo in self.city_associations:
                famous_things = self.city_associations[text_combo]
                self.debug_log(f"🏛️ City '{text_combo}' is famous for: {famous_things}")

                # Prioritize emoji symbols in the famous_things list
                for famous_thing in famous_things:
                    if famous_thing.startswith('🎭'):  # Direct emoji symbol
                        # Convert emoji to unicode code
                        emoji_code = format(ord(famous_thing), 'x')
                        if emoji_code not in self.used_emojis_in_quiz:
                            self.used_emojis_in_quiz.add(emoji_code)
                            self.debug_log(
                                f"✅ CITY EMOJI SYMBOL MATCH: '{text_combo}' -> '{famous_thing}' -> {emoji_code}")
                            return emoji_code

                # Try to find emoji for what the city is famous for
                for famous_thing in famous_things:
                    if not famous_thing.startswith('🎭'):  # Skip emoji symbols in this pass
                        if famous_thing in self.emoji_mapping:
                            emoji_code = self.emoji_mapping[famous_thing]
                            if emoji_code not in self.used_emojis_in_quiz:
                                self.used_emojis_in_quiz.add(emoji_code)
                                self.debug_log(
                                    f"✅ CITY FAME MATCH: '{text_combo}' -> famous for '{famous_thing}' -> {emoji_code}")
                                return emoji_code

        self.debug_log("❌ No city fame matches found")
        return None

    def search_by_country_fame(self, words):
        """STEP 2: For countries, search what they're famous for"""
        self.debug_log(f"🌍 STEP 2: Searching for country fame associations in: {words}")

        for word in words:
            if word in self.country_associations:
                famous_things = self.country_associations[word]
                self.debug_log(f"🏛️ Country '{word}' is famous for: {famous_things}")

                # Try to find emoji for what the country is famous for
                for famous_thing in famous_things:
                    if famous_thing in self.emoji_mapping:
                        emoji_code = self.emoji_mapping[famous_thing]
                        if emoji_code not in self.used_emojis_in_quiz:
                            self.used_emojis_in_quiz.add(emoji_code)
                            self.debug_log(
                                f"✅ COUNTRY FAME MATCH: '{word}' -> famous for '{famous_thing}' -> {emoji_code}")
                            return emoji_code

        self.debug_log("❌ No country fame matches found")
        return None

    def search_by_human_feeling(self, words, original_text):
        """STEP 3: How humans FEEL when reading these words"""
        self.debug_log(f"💭 STEP 3: Analyzing human emotional response to: {words}")

        # Check semantic groups first
        for group_name, group_data in self.semantic_groups.items():
            for word in words:
                if word in group_data['keywords']:
                    emoji_code = group_data['emoji']
                    if emoji_code not in self.used_emojis_in_quiz:
                        self.used_emojis_in_quiz.add(emoji_code)
                        self.debug_log(f"✅ SEMANTIC GROUP MATCH: '{word}' -> {group_name} -> {emoji_code}")
                        return emoji_code

        # Check emotional responses
        for word in words:
            if word in self.emotional_responses:
                emoji_code = self.emotional_responses[word]
                if emoji_code not in self.used_emojis_in_quiz:
                    self.used_emojis_in_quiz.add(emoji_code)
                    self.debug_log(f"✅ EMOTIONAL RESPONSE MATCH: '{word}' -> {emoji_code}")
                    return emoji_code

        self.debug_log("❌ No human feeling matches found")
        return None

    def get_emoji_for_text(self, text):
        """Get the most appropriate emoji for given text with CITY-AWARE COMPOUND WORD PRIORITY"""
        # Clean the text
        clean_text = re.sub(r'[^\w\s]', '', text.lower().strip())
        words = clean_text.split()

        self.debug_log(f"🎯 COMPREHENSIVE EMOJI SEARCH for: '{text}' -> clean: '{clean_text}'")
        self.debug_log("=" * 80)

        # STEP 0: ULTIMATE RULE - Try compound word matching first
        result = self.search_by_compound_words(text)
        if result:
            return result

        # STEP 0.5: HIGHEST PRIORITY - Try country flag matching
        result = self.search_by_country_flags(words, text)
        if result:
            return result
        # STEP 1: Try individual word literal matching
        result = self.search_by_literal_match(words)
        if result:
            return result

        # STEP 1.5: NEW - Try city fame associations (HIGH PRIORITY)
        result = self.search_by_city_fame(words)
        if result:
            return result

        # STEP 2: If no literal match, try country fame associations
        result = self.search_by_country_fame(words)
        if result:
            return result

        # STEP 3: If not found, go to how humans FEEL when reading words
        result = self.search_by_human_feeling(words, text)
        if result:
            return result

        # Enhanced fallback emojis with more variety
        fallback_emojis = [
            '1f914',  # thinking face
            '1f4a1',  # light bulb
            '2753',  # question mark
            '1f4dd',  # memo
            '1f44d',  # thumbs up
            '1f3af',  # target
            '1f4cd',  # location pin
            '1f50d',  # magnifying glass
            '2705',  # check mark
            '1f4d6',  # book
        ]

        for emoji_code in fallback_emojis:
            if emoji_code not in self.used_emojis_in_quiz:
                self.used_emojis_in_quiz.add(emoji_code)
                self.debug_log(f"🎯 FALLBACK EMOJI for '{text}': {emoji_code}")
                return emoji_code

        # If all fallbacks used, use a random one
        selected_emoji = random.choice(fallback_emojis)
        self.debug_log(f"⚠️ RANDOM FALLBACK for '{text}': {selected_emoji}")
        return selected_emoji

    def get_standard_emoji_sources(self, emoji_code):
        """Get standard emoji sources"""
        return [
            f"https://cdn.jsdelivr.net/npm/emoji-datasource-apple@15.0.1/img/apple/64/{emoji_code}.png",
            f"https://cdn.jsdelivr.net/npm/emoji-datasource-google@15.0.1/img/google/64/{emoji_code}.png",
            f"https://cdn.jsdelivr.net/npm/emoji-datasource-twitter@15.0.1/img/twitter/64/{emoji_code}.png",
            f"https://twemoji.maxcdn.com/v/latest/72x72/{emoji_code}.png",
            f"https://cdn.jsdelivr.net/gh/twitter/twemoji@latest/assets/72x72/{emoji_code}.png",
            f"https://raw.githubusercontent.com/twitter/twemoji/master/assets/72x72/{emoji_code}.png"
        ]

    def get_playful_emoji_sources(self, emoji_code):
        """Get playful/cartoon emoji sources"""
        return [
            # Noto Color Emoji - More colorful and playful
            f"https://fonts.gstatic.com/s/e/notocoloremoji/latest/{emoji_code}/512.png",
            f"https://raw.githubusercontent.com/googlefonts/noto-emoji/main/png/128/emoji_u{emoji_code}.png",
            f"https://github.com/googlefonts/noto-emoji/raw/main/png/72/emoji_u{emoji_code}.png",
            # JoyPixels (formerly EmojiOne) - Playful style
            f"https://cdn.jsdelivr.net/npm/emoji-datasource-twitter@15.0.1/img/twitter/64/{emoji_code}.png",
            # Fluent Emoji - Microsoft's playful style
            f"https://cdn.jsdelivr.net/gh/microsoft/fluentui-emoji@main/assets/{emoji_code}/Color/{emoji_code}_color.svg",
            # Fallback to animated versions
            f"https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/Symbols/{emoji_code}.png",
            # Additional playful sources
            f"https://cdn.jsdelivr.net/npm/emoji-datasource-facebook@15.0.1/img/facebook/64/{emoji_code}.png",
            f"https://twemoji.maxcdn.com/v/latest/svg/{emoji_code}.svg",
            # JoyPixels as additional playful source
            f"https://cdn.jsdelivr.net/joypixels/assets/6.6/png/unicode/64/{emoji_code}.png",
            # EmojiOne as backup
            f"https://cdn.jsdelivr.net/emojione/assets/4.0/png/64/{emoji_code}.png"
        ]

    def get_emoji_sources(self, emoji_code):
        """Get reliable emoji sources with better error handling (backward compatibility)"""
        return self.get_standard_emoji_sources(emoji_code)

    def download_emoji_image(self, emoji_code, filename, style='standard'):
        """Download emoji image from multiple sources with style selection"""
        if style == 'playful':
            sources = self.get_playful_emoji_sources(emoji_code)
            print(f"🎨 Downloading PLAYFUL emoji: {emoji_code}")
        else:
            sources = self.get_standard_emoji_sources(emoji_code)
            print(f"📱 Downloading STANDARD emoji: {emoji_code}")

        self.debug_log(f"📱 Downloading {style} emoji: {emoji_code}")

        for i, source_url in enumerate(sources, 1):
            try:
                print(f"🔄 Trying to download {style} emoji from: {source_url}")
                response = self.session.get(source_url, timeout=15)

                if response.status_code == 200:
                    with open(filename, 'wb') as f:
                        f.write(response.content)

                    # Verify the image can be opened
                    try:
                        with Image.open(filename) as img:
                            # Convert SVG or other formats to PNG if needed
                            if img.format != 'PNG':
                                img = img.convert('RGBA')

                            # Resize to standard emoji size
                            img = img.resize((EMOJI_CONFIG["size"], EMOJI_CONFIG["size"]), Image.Resampling.LANCZOS)
                            img.save(filename, 'PNG')
                            print(f"✅ Successfully downloaded {style} emoji: {emoji_code}")
                            return filename
                    except Exception as e:
                        print(f"❌ Image verification failed for {style} emoji {emoji_code}: {e}")
                        continue

                else:
                    print(f"❌ HTTP {response.status_code} for {style} emoji from: {source_url}")

            except Exception as e:
                print(f"❌ Error downloading {style} emoji from {source_url}: {e}")
                continue

        print(f"❌ Failed to download {style} emoji: {emoji_code}")
        return None

    def create_fallback_emoji(self, emoji_code, filename, style='standard'):
        """Create a fallback emoji with style-specific appearance"""
        try:
            size = EMOJI_CONFIG["size"]
            self.debug_log(f"🎨 Creating fallback emoji for: {emoji_code}")

            # Enhanced color palette based on emoji code
            color_palettes = [
                [(255, 193, 7), (255, 235, 59)],  # Yellow gradient
                [(76, 175, 80), (129, 199, 132)],  # Green gradient
                [(244, 67, 54), (239, 154, 154)],  # Red gradient
                [(33, 150, 243), (144, 202, 249)],  # Blue gradient
                [(156, 39, 176), (206, 147, 216)],  # Purple gradient
                [(255, 152, 0), (255, 204, 128)],  # Orange gradient
                [(96, 125, 139), (176, 190, 197)],  # Gray gradient
            ]

            # Select color based on emoji code hash
            color_index = sum(ord(c) for c in emoji_code) % len(color_palettes)
            primary_color, secondary_color = color_palettes[color_index]

            # Create image with gradient background
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Draw gradient circle
            margin = 8
            for i in range(margin, size - margin):
                # Calculate gradient
                ratio = (i - margin) / (size - 2 * margin)
                r = int(primary_color[0] * (1 - ratio) + secondary_color[0] * ratio)
                g = int(primary_color[1] * (1 - ratio) + secondary_color[1] * ratio)
                b = int(primary_color[2] * (1 - ratio) + secondary_color[2] * ratio)

                draw.ellipse([i, i, size - i, size - i], fill=(r, g, b))

            # Add emoji code or symbol
            try:
                font_size = size // 4
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                font = ImageFont.load_default()

            # Use first character of emoji code or a symbol
            if emoji_code.startswith('1f'):
                text = "😊"  # Default emoji symbol
            else:
                text = emoji_code[0].upper()

            if font:
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                text_x = (size - text_width) // 2
                text_y = (size - text_height) // 2 - 2

                # Add text shadow for better visibility
                shadow_offset = 2
                draw.text((text_x + shadow_offset, text_y + shadow_offset), text,
                          fill=(0, 0, 0, 128), font=font)
                draw.text((text_x, text_y), text, fill=(255, 255, 255), font=font)

            img.save(filename, 'PNG')
            self.debug_log(f"✅ Created enhanced fallback emoji: {emoji_code}")
            return filename

        except Exception as e:
            self.debug_log(f"❌ Error creating fallback emoji: {e}")
            return None

    def get_emoji_image_path(self, text, option_index, question_index=0, account_index=0, video_index=0):
        """Get emoji image path for given text with style selection"""
        # Get emoji code
        emoji_code = self.get_emoji_for_text(text)

        # Determine style for this question
        style = self.get_emoji_style_for_question(question_index)

        # Create filename with style indicator and unique identifiers
        safe_text = re.sub(r'[^\w\s]', '', text)[:20]  # Limit filename length
        filename = f"{self.emoji_dir}/emoji_acc{account_index}_vid{video_index}_{style}_q{question_index}_opt{option_index}_{emoji_code}_{safe_text}.png"

        # Check if already cached
        if os.path.exists(filename):
            print(f"📦 Using cached {style} emoji: {emoji_code}")
            return filename

        # Try to download with selected style
        print(f"🔄 Downloading {style} emoji for '{text}': {emoji_code}")
        downloaded_path = self.download_emoji_image(emoji_code, filename, style)
        if downloaded_path:
            print(f"✅ Downloaded {style} emoji for '{text}': {emoji_code}")
            return downloaded_path

        # Create fallback with selected style
        print(f"🎨 Creating {style} fallback emoji for '{text}': {emoji_code}")
        fallback_path = self.create_fallback_emoji(emoji_code, filename, style)
        if fallback_path:
            print(f"✅ Created {style} fallback emoji for '{text}': {emoji_code}")
            return fallback_path

        print(f"❌ Failed to get {style} emoji for '{text}'")
        return None


class PhotoAPIManager:
    """Enhanced photo API manager with multiple API key fallback system"""

    def __init__(self):
        self.session = create_session_with_retries()
        self.debug_mode = True
        # Track which Unsplash API keys are still working
        self.working_unsplash_keys = API_KEYS.get("unsplash", []).copy() if isinstance(API_KEYS.get("unsplash"),
                                                                                       list) else [
            API_KEYS.get("unsplash")] if API_KEYS.get("unsplash") else []
        self.current_unsplash_key_index = 0

    def debug_log(self, message):
        """Print debug messages if debug mode is enabled"""
        if self.debug_mode:
            print(f"📸 DEBUG: {message}")

    def get_current_unsplash_key(self):
        """Get the current working Unsplash API key"""
        if not self.working_unsplash_keys:
            return None
        if self.current_unsplash_key_index >= len(self.working_unsplash_keys):
            self.current_unsplash_key_index = 0
        return self.working_unsplash_keys[self.current_unsplash_key_index]

    def mark_unsplash_key_as_exhausted(self, api_key):
        """Mark an Unsplash API key as exhausted and remove it from working keys"""
        if api_key in self.working_unsplash_keys:
            self.working_unsplash_keys.remove(api_key)
            print(
                f"⚠️ Unsplash API key exhausted, removed from working keys. Remaining: {len(self.working_unsplash_keys)}")
            # Reset index to start from beginning of remaining keys
            self.current_unsplash_key_index = 0

    def switch_to_next_unsplash_key(self):
        """Switch to the next available Unsplash API key"""
        if len(self.working_unsplash_keys) > 1:
            self.current_unsplash_key_index = (self.current_unsplash_key_index + 1) % len(self.working_unsplash_keys)
            new_key = self.get_current_unsplash_key()
            print(
                f"🔄 Switched to next Unsplash API key (Index: {self.current_unsplash_key_index + 1}/{len(self.working_unsplash_keys)})")
            return new_key
        return None

    def validate_api_keys(self):
        """Validate all API keys with comprehensive error handling"""
        valid_keys = {}

        # Test Multiple Unsplash Keys
        unsplash_keys = API_KEYS.get("unsplash", [])
        if isinstance(unsplash_keys, str):
            unsplash_keys = [unsplash_keys]

        working_unsplash_keys = []
        for i, key in enumerate(unsplash_keys):
            if key and key != "YOUR_UNSPLASH_ACCESS_KEY":
                try:
                    self.debug_log(f"Testing Unsplash API key {i + 1}/{len(unsplash_keys)}...")
                    response = self.session.get(
                        "https://api.unsplash.com/photos/random",
                        headers={'Authorization': f'Client-ID {key}'},
                        timeout=15
                    )
                    self.debug_log(f"Unsplash key {i + 1} response: {response.status_code}")
                    if response.status_code == 200:
                        working_unsplash_keys.append(key)
                        print(f"✅ Unsplash API key {i + 1}/{len(unsplash_keys)} is valid")
                    else:
                        print(f"❌ Unsplash API key {i + 1}/{len(unsplash_keys)} invalid: {response.status_code}")
                        if response.status_code == 401:
                            print(f"   Key {i + 1}: Check your API key is correct")
                        elif response.status_code == 403:
                            print(f"   Key {i + 1}: API key may be rate limited or suspended")
                except Exception as e:
                    print(f"❌ Unsplash API key {i + 1}/{len(unsplash_keys)} test failed: {e}")

        if working_unsplash_keys:
            valid_keys["unsplash"] = True
            self.working_unsplash_keys = working_unsplash_keys
            print(f"✅ {len(working_unsplash_keys)}/{len(unsplash_keys)} Unsplash API keys are working")
        else:
            print(f"❌ No working Unsplash API keys found out of {len(unsplash_keys)} provided")

        # Test other APIs (unchanged)
        # Test Pixabay
        if API_KEYS.get("pixabay") and API_KEYS["pixabay"] != "YOUR_PIXABAY_API_KEY":
            try:
                self.debug_log("Testing Pixabay API...")
                response = self.session.get(
                    f"https://pixabay.com/api/?key={API_KEYS['pixabay']}&q=nature&per_page=3",
                    timeout=15
                )
                self.debug_log(f"Pixabay response: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('hits'):
                        valid_keys["pixabay"] = True
                        print("✅ Pixabay API key is valid")
                    else:
                        print("❌ Pixabay API key valid but no results")
                else:
                    print(f"❌ Pixabay API key invalid: {response.status_code}")
            except Exception as e:
                print(f"❌ Pixabay API test failed: {e}")

        # Test Pexels
        if API_KEYS.get("pexels") and API_KEYS["pexels"] != "YOUR_PEXELS_API_KEY":
            try:
                self.debug_log("Testing Pexels API...")
                response = self.session.get(
                    "https://api.pexels.com/v1/search?query=nature&per_page=5",
                    headers={'Authorization': API_KEYS["pexels"]},
                    timeout=15
                )
                self.debug_log(f"Pexels response: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('photos'):
                        valid_keys["pexels"] = True
                        print("✅ Pexels API key is valid")
                    else:
                        print("⚠️ Pexels API key valid but no results")
                else:
                    print(f"❌ Pexels API key invalid: {response.status_code}")
                    if response.status_code == 429:
                        print("   Rate limit exceeded")
            except Exception as e:
                print(f"❌ Pexels API test failed: {e}")

        return valid_keys

    def search_unsplash(self, query, per_page=20):
        """Search Unsplash for photos with multiple API key fallback system"""
        if not self.working_unsplash_keys:
            self.debug_log("No working Unsplash API keys available")
            return []

        max_retries_per_key = 2  # Try each key up to 2 times
        keys_tried = 0

        while keys_tried < len(self.working_unsplash_keys) and self.working_unsplash_keys:
            current_key = self.get_current_unsplash_key()
            if not current_key:
                self.debug_log("No current Unsplash API key available")
                break

            for retry in range(max_retries_per_key):
                try:
                    self.debug_log(
                        f"Searching Unsplash for: '{query}' (Key {self.current_unsplash_key_index + 1}/{len(self.working_unsplash_keys)}, Attempt {retry + 1}/{max_retries_per_key})")

                    response = self.session.get(
                        "https://api.unsplash.com/search/photos",
                        headers={'Authorization': f'Client-ID {current_key}'},
                        params={
                            'query': query,
                            'per_page': per_page,
                            'orientation': 'landscape',
                            'content_filter': 'high'
                        },
                        timeout=30
                    )

                    self.debug_log(f"Unsplash response: {response.status_code}")

                    if response.status_code == 200:
                        data = response.json()
                        photos = []
                        for photo in data.get('results', []):
                            photos.append({
                                'id': photo['id'],
                                'url': photo['urls']['regular'],
                                'thumbnail': photo['urls']['thumb'],
                                'photographer': photo['user']['name'],
                                'alt': photo.get('alt_description', 'Photo'),
                                'source': 'Unsplash'
                            })
                        self.debug_log(
                            f"Unsplash found {len(photos)} photos with key {self.current_unsplash_key_index + 1}")
                        return photos

                    elif response.status_code in [403, 429]:
                        # Rate limit or forbidden - this key is exhausted
                        print(
                            f"⚠️ Unsplash API key {self.current_unsplash_key_index + 1} hit rate limit (HTTP {response.status_code})")
                        self.mark_unsplash_key_as_exhausted(current_key)
                        break  # Break out of retry loop for this key

                    elif response.status_code == 401:
                        # Unauthorized - invalid key
                        print(f"❌ Unsplash API key {self.current_unsplash_key_index + 1} is invalid (HTTP 401)")
                        self.mark_unsplash_key_as_exhausted(current_key)
                        break  # Break out of retry loop for this key

                    else:
                        self.debug_log(f"Unsplash API error: {response.status_code}")
                        if retry == max_retries_per_key - 1:
                            # Last retry for this key failed
                            print(
                                f"⚠️ Unsplash API key {self.current_unsplash_key_index + 1} failed after {max_retries_per_key} attempts")
                        else:
                            # Wait before retry
                            time.sleep(1)

                except Exception as e:
                    self.debug_log(f"Unsplash search error with key {self.current_unsplash_key_index + 1}: {e}")
                    if retry == max_retries_per_key - 1:
                        print(f"❌ Unsplash API key {self.current_unsplash_key_index + 1} failed with exception: {e}")

            # Move to next key if current one failed or was exhausted
            keys_tried += 1
            if keys_tried < len(self.working_unsplash_keys):
                next_key = self.switch_to_next_unsplash_key()
                if not next_key:
                    break

        print(f"❌ All Unsplash API keys exhausted or failed. Tried {keys_tried} keys.")
        return []

    def search_pixabay(self, query, per_page=20):
        """Search Pixabay for photos with enhanced error handling"""
        if not API_KEYS.get("pixabay") or API_KEYS["pixabay"] == "YOUR_PIXABAY_API_KEY":
            self.debug_log("Pixabay API key not configured")
            return []

        try:
            self.debug_log(f"Searching Pixabay for: '{query}'")
            response = self.session.get(
                "https://pixabay.com/api/",
                params={
                    'key': API_KEYS["pixabay"],
                    'q': query,
                    'per_page': per_page,
                    'image_type': 'photo',
                    'orientation': 'horizontal',
                    'safesearch': 'true',
                    'category': 'education,business,nature'
                },
                timeout=30
            )

            self.debug_log(f"Pixabay response: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                photos = []
                for photo in data.get('hits', []):
                    photos.append({
                        'id': str(photo['id']),
                        'url': photo['webformatURL'],
                        'thumbnail': photo['previewURL'],
                        'photographer': photo['user'],
                        'alt': photo['tags'],
                        'source': 'Pixabay'
                    })
                self.debug_log(f"Pixabay found {len(photos)} photos")
                return photos
            else:
                self.debug_log(f"Pixabay API error: {response.status_code}")
                return []

        except Exception as e:
            self.debug_log(f"Pixabay search error: {e}")
            return []

    def search_pexels(self, query, per_page=20):
        """Search Pexels for photos with enhanced error handling"""
        if not API_KEYS.get("pexels") or API_KEYS["pexels"] != "YOUR_PEXELS_API_KEY":
            self.debug_log("Pexels API key not configured")
            return []

        try:
            self.debug_log(f"Searching Pexels for: '{query}'")
            response = self.session.get(
                "https://api.pexels.com/v1/search",
                headers={'Authorization': API_KEYS["pexels"]},
                params={
                    'query': query,
                    'per_page': per_page,
                    'orientation': 'landscape'
                },
                timeout=30
            )

            self.debug_log(f"Pexels response: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                photos = []
                for photo in data.get('photos', []):
                    photos.append({
                        'id': str(photo['id']),
                        'url': photo['src']['large'],
                        'thumbnail': photo['src']['medium'],
                        'photographer': photo['photographer'],
                        'alt': photo.get('alt', 'Photo'),
                        'source': 'Pexels'
                    })
                self.debug_log(f"Pexels found {len(photos)} photos")
                return photos
            else:
                self.debug_log(f"Pexels API error: {response.status_code}")
                if response.status_code == 429:
                    print("⏳ Pexels rate limit exceeded, waiting...")
                    time.sleep(2)
                return []

        except Exception as e:
            self.debug_log(f"Pexels search error: {e}")
            return []

    def search_all_apis(self, query, total_needed=10):
        """Search all available APIs for photos with better coordination"""
        all_photos = []

        # Try APIs in order of reliability - Unsplash first with fallback system
        apis = [
            ('unsplash', self.search_unsplash),
            ('pexels', self.search_pexels),
            ('pixabay', self.search_pixabay)
        ]

        for api_name, search_func in apis:
            if len(all_photos) >= total_needed:
                break

            try:
                self.debug_log(f"Trying {api_name} API...")
                photos = search_func(query, per_page=15)
                if photos:
                    # Filter appropriate photos
                    appropriate_photos = [p for p in photos if self.is_photo_appropriate(p)]
                    all_photos.extend(appropriate_photos)
                    self.debug_log(f"{api_name} contributed {len(appropriate_photos)} appropriate photos")

                # Small delay between API calls
                time.sleep(1)

            except Exception as e:
                self.debug_log(f"Error with {api_name}: {e}")
                continue

        # Remove duplicates based on URL
        unique_photos = []
        seen_urls = set()
        for photo in all_photos:
            if photo['url'] not in seen_urls:
                unique_photos.append(photo)
                seen_urls.add(photo['url'])

        self.debug_log(f"Total unique photos found: {len(unique_photos)}")
        return unique_photos[:total_needed]

    def is_photo_appropriate(self, photo_data):
        """Check if photo is appropriate based on alt text and tags"""
        try:
            alt_text = photo_data.get('alt', '').lower()

            # Keywords that might indicate inappropriate content
            inappropriate_keywords = [
                'woman', 'women', 'girl', 'girls', 'female', 'lady', 'ladies',
                'mother', 'mom', 'daughter', 'sister', 'wife', 'girlfriend',
                'bride', 'businesswoman', 'schoolgirl'
            ]

            # Check if any inappropriate keywords are in the alt text
            for keyword in inappropriate_keywords:
                if keyword in alt_text:
                    self.debug_log(f"Filtered out photo with keyword: {keyword}")
                    return False

            return True
        except:
            return True  # If we can't determine, assume it's appropriate


def download_image(url, filename, max_retries=3):
    """Download image from URL with enhanced error handling and debugging"""
    for attempt in range(max_retries):
        try:
            print(f"🔄 Downloading image (attempt {attempt + 1}/{max_retries}): {url}")

            session = create_session_with_retries()
            response = session.get(url, timeout=30, stream=True)
            response.raise_for_status()

            # Check content type
            content_type = response.headers.get('content-type', '')
            print(f"   Content-Type: {content_type}")

            if not content_type.startswith('image/'):
                print(f"   ❌ Invalid content type: {content_type}")
                continue

            os.makedirs(os.path.dirname(filename), exist_ok=True)

            # Download with progress tracking
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0

            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    downloaded_size += len(chunk)

            print(f"   Downloaded {downloaded_size} bytes")

            # Verify and process the image
            with Image.open(filename) as img:
                print(f"   Image format: {img.format}, size: {img.size}, mode: {img.mode}")

                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Resize if too large
                max_size = (800, 600)
                if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)
                    print(f"   Resized to: {img.size}")

                # Save as JPEG
                img.save(filename, 'JPEG', quality=85)

            print(f"✅ Successfully downloaded: {filename}")
            return True

        except requests.exceptions.Timeout:
            print(f"❌ Download attempt {attempt + 1} failed: Timeout")
        except requests.exceptions.RequestException as e:
            print(f"❌ Download attempt {attempt + 1} failed: {e}")
        except Exception as e:
            print(f"❌ Download attempt {attempt + 1} failed: {e}")

        # Clean up failed download
        if os.path.exists(filename):
            try:
                os.remove(filename)
            except:
                pass

        if attempt < max_retries - 1:
            wait_time = 2 ** attempt  # Exponential backoff
            print(f"   Waiting {wait_time} seconds before retry...")
            time.sleep(wait_time)

    print(f"❌ All download attempts failed for: {url}")
    return False


def extract_option_text(option):
    """Extract the text from an option, removing the A), B), C) prefix"""
    if ')' in option:
        return option.split(')', 1)[1].strip()
    return option.strip()


def extract_and_translate_option_text(option, source_language=None):
    """Extract option text and translate it to English for search purposes with enhanced debugging."""
    original_text = extract_option_text(option)

    print(f"🔄 Starting translation process for: '{original_text}'")
    if source_language:
        print(f"   Using detected quiz language: {source_language}")

    translated_text = translate_to_english(original_text, source_language=source_language)

    # Verify if translation actually occurred
    if original_text.lower() != translated_text.lower():
        print(f"✅ Translation successful: '{original_text}' → '{translated_text}'")
    else:
        print(f"⚠️ No translation occurred: '{original_text}' remained as '{translated_text}'")

    return original_text, translated_text


def get_photos_for_options(answer_options, question_index, photo_api_manager, quiz_language=None, num_photos=3):
    """Get photos based on TRANSLATED option text for better search results"""
    import random

    os.makedirs("photos", exist_ok=True)

    print(f"\n📸 TRANSLATED PHOTO SEARCH for question {question_index + 1}...")
    print(f"   Options: {answer_options}")
    print(f"   🌐 Quiz Language: {quiz_language}")
    print("🎯 NEW RULE: Translate option text from quiz language to English, then search for photos!")

    photo_paths = []

    for i, option in enumerate(answer_options[:num_photos]):
        # Extract and translate the option text using quiz language
        original_text, translated_text = extract_and_translate_option_text(option, source_language=quiz_language)

        print(f"\n🔍 Searching for photos using translated text:")
        print(f"   Original: '{original_text}' (Option {chr(65 + i)})")
        print(f"   🌐 Translated: '{translated_text}' (for search)")

        # Search for photos using TRANSLATED text for better results
        photos = photo_api_manager.search_all_apis(translated_text, total_needed=10)

        if photos:
            # Randomly select from available photos
            selected_photo = random.choice(photos)
            selected_index = photos.index(selected_photo) + 1

            photo_url = selected_photo['url']
            photographer = selected_photo.get('photographer', 'Unknown')
            photo_alt = selected_photo.get('alt', 'No description')
            source = selected_photo.get('source', 'Unknown')

            filename = f"photos/question_{question_index}_option_{chr(65 + i)}_{original_text.replace(' ', '_')[:20]}.jpg"

            print(f"🎲 Selected photo #{selected_index} out of {len(photos)} results for translated '{translated_text}'")
            print(f"📥 Downloading photo for original '{original_text}' (searched as '{translated_text}')...")
            print(f"   URL: {photo_url}")
            print(f"   Photographer: {photographer} ({source})")

            if download_image(photo_url, filename):
                print(f"✅ Downloaded photo for '{original_text}' using translation: {filename}")
                photo_paths.append(filename)
            else:
                print(f"❌ Failed to download photo, trying backup...")
                # Backup logic remains the same
                remaining_photos = [p for p in photos if p != selected_photo]
                if remaining_photos:
                    backup_photo = random.choice(remaining_photos)
                    backup_url = backup_photo['url']
                    if download_image(backup_url, filename):
                        print(f"✅ Downloaded backup photo: {filename}")
                        photo_paths.append(filename)
                    else:
                        fallback_path = get_fallback_photo(question_index * 3 + i)
                        photo_paths.append(fallback_path)
                else:
                    fallback_path = get_fallback_photo(question_index * 3 + i)
                    photo_paths.append(fallback_path)
        else:
            print(f"❌ No photos found for translated '{translated_text}', using fallback")
            fallback_path = get_fallback_photo(question_index * 3 + i)
            photo_paths.append(fallback_path)

    # Ensure we have exactly num_photos
    while len(photo_paths) < num_photos:
        fallback_path = get_fallback_photo(question_index * 3 + len(photo_paths))
        photo_paths.append(fallback_path)

    print(f"\n✅ TRANSLATED PHOTO SEARCH COMPLETE!")
    print(f"📊 Result: {len(photo_paths)} photos collected using English translations from {quiz_language}")
    return photo_paths




def select_unique_photos(photos, num_photos):
    """Select multiple photos that haven't been used before with better logic"""
    if not photos:
        return [None] * num_photos

    print(f"🎯 Selecting {num_photos} unique photos from {len(photos)} available")

    # Filter out already used photos
    available_photos = [photo for photo in photos if photo['id'] not in used_photos]
    print(f"   Available unused photos: {len(available_photos)}")

    if len(available_photos) < num_photos:
        # If not enough unused photos, reset and use any photos
        print("🔄 Not enough unused photos, expanding selection")
        available_photos = photos

    if len(available_photos) < num_photos:
        # Still not enough, pad with None
        selected_photos = available_photos + [None] * (num_photos - len(available_photos))
        print(
            f"⚠️ Only {len(available_photos)} photos available, padding with {num_photos - len(available_photos)} fallbacks")
    else:
        selected_photos = random.sample(available_photos, num_photos)
        print(f"✅ Selected {num_photos} photos randomly")

    # Mark selected photos as used
    for photo in selected_photos:
        if photo:
            used_photos.add(photo['id'])

    return selected_photos


def get_fallback_photos(question_index, num_photos):
    """Get multiple fallback photos for a question with better error handling"""
    photo_paths = []
    print(f"🔄 Getting {num_photos} fallback photos for question {question_index + 1}")

    for i in range(num_photos):
        # Create a deterministic but varied selection
        fallback_index = ((question_index * 3) + i) % len(FALLBACK_PHOTOS)
        fallback_url = FALLBACK_PHOTOS[fallback_index]
        filename = f"photos/question_{question_index}_fallback_{i}.jpg"

        print(f"📥 Downloading fallback photo {i + 1}/{num_photos}: {fallback_url}")
        if download_image(fallback_url, filename):
            print(f"✅ Downloaded fallback photo {i + 1}: {filename}")
            photo_paths.append(filename)
        else:
            # If even fallback fails, create a simple colored image
            print(f"🎨 Creating placeholder image {i + 1}")
            placeholder_path = create_placeholder_image(question_index * 3 + i)
            photo_paths.append(placeholder_path)

    return photo_paths


def get_fallback_photo(photo_index):
    """Get a single fallback photo with error handling"""
    fallback_index = photo_index % len(FALLBACK_PHOTOS)
    fallback_url = FALLBACK_PHOTOS[fallback_index]
    filename = f"photos/fallback_{photo_index}.jpg"

    print(f"📥 Getting fallback photo {photo_index}: {fallback_url}")
    if download_image(fallback_url, filename):
        print(f"✅ Downloaded fallback photo: {filename}")
        return filename

    # If even fallback fails, create a simple colored image
    print("🎨 Creating simple placeholder image")
    return create_placeholder_image(photo_index)


def create_placeholder_image(photo_index):
    """Create an enhanced placeholder image if all downloads fail"""
    try:
        # Create a gradient background
        width, height = PHOTO_CONFIG["width"], PHOTO_CONFIG["height"]

        # Enhanced color palettes
        color_palettes = [
            [(70, 130, 180), (135, 206, 235)],  # Steel Blue to Sky Blue
            [(100, 149, 237), (176, 196, 222)],  # Cornflower Blue to Light Steel Blue
            [(72, 61, 139), (147, 112, 219)],  # Dark Slate Blue to Medium Purple
            [(106, 90, 205), (186, 85, 211)],  # Slate Blue to Medium Orchid
            [(123, 104, 238), (221, 160, 221)],  # Medium Slate Blue to Plum
            [(60, 179, 113), (144, 238, 144)],  # Medium Sea Green to Light Green
            [(255, 140, 0), (255, 218, 185)],  # Dark Orange to Peach Puff
        ]

        palette_index = photo_index % len(color_palettes)
        primary_color, secondary_color = color_palettes[palette_index]

        # Create image with gradient effect
        img = Image.new('RGB', (width, height), primary_color)
        draw = ImageDraw.Draw(img)

        # Create gradient effect
        for y in range(height):
            ratio = y / height
            r = int(primary_color[0] * (1 - ratio) + secondary_color[0] * ratio)
            g = int(primary_color[1] * (1 - ratio) + secondary_color[1] * ratio)
            b = int(primary_color[2] * (1 - ratio) + secondary_color[2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))

        # Add geometric shapes for visual interest
        for i in range(3):
            x = (i * width // 3) + width // 6
            y = height // 2
            radius = 20 + (i * 10)
            # Create lighter color for shapes
            lighter_color = tuple(min(255, c + 40) for c in primary_color)
            draw.ellipse([x - radius, y - radius, x + radius, y + radius],
                         fill=lighter_color, outline=None)

        # Add text overlay
        try:
            font = ImageFont.truetype("arial.ttf", 28)
        except:
            font = ImageFont.load_default()

        text = f"Photo {photo_index + 1}"
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            text_x = (width - text_width) // 2
            text_y = (height - text_height) // 2

            # Add text shadow
            draw.text((text_x + 2, text_y + 2), text, fill=(0, 0, 0, 128), font=font)
            draw.text((text_x, text_y), text, fill=(255, 255, 255), font=font)

        filename = f"photos/placeholder_{photo_index}.jpg"
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        img.save(filename, 'JPEG', quality=85)

        print(f"✅ Created enhanced placeholder image: {filename}")
        return filename

    except Exception as e:
        print(f"❌ Error creating placeholder image: {e}")
        return None


def get_emoji_images_for_options(answer_options, question_index, emoji_manager, quiz_language=None, account_index=0, video_index=0):
    """Get emoji images for all options using TRANSLATED text for better emoji matching"""
    emoji_paths = []

    print(f"🎭 Getting emoji images for question {question_index + 1} using translations...")
    print(f"   🌐 Quiz Language: {quiz_language}")

    for i, option in enumerate(answer_options):
        # Extract and translate option text using quiz language
        original_text, translated_text = extract_and_translate_option_text(option, source_language=quiz_language)

        print(f"   Option {chr(65 + i)}: '{original_text}' → 🌐 '{translated_text}'")

        # Use translated text for emoji search
        emoji_path = emoji_manager.get_emoji_image_path(
            translated_text,  # Use translated text for better emoji matching
            f"q{question_index}_opt{i}",
            question_index,
            account_index,
            video_index
        )

        if emoji_path:
            emoji_paths.append(emoji_path)
            print(f"   ✅ Got emoji for '{original_text}' using translation '{translated_text}'")
        else:
            emoji_paths.append(None)
            print(f"   ❌ Failed to get emoji for '{original_text}' (translated: '{translated_text}')")

    print(
        f"📊 Emoji collection summary: {len([p for p in emoji_paths if p])}/{len(emoji_paths)} emojis collected using translations from {quiz_language}")
    return emoji_paths



# Enhanced fallback photos - verified working URLs
FALLBACK_PHOTOS = [
    "https://images.unsplash.com/photo-*************-b7833e8f5570?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-*************-0b793f4b4173?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-*************-7bf3a84b82f8?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1474631245212-32dc3c8310c6?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1497633762265-9d179a990aa6?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1486312338219-ce68e2c6b696?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=800&h=450&fit=crop",
    "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=450&fit=crop"
]

# Track used photos and emojis to avoid repetition
used_photos = set()
used_emojis_global = set()  # Global emoji tracking across entire quiz


# Rest of the code remains the same...
def german_to_phonetic(text):
    """Convert German text to phonetic English approximations."""
    try:
        lang = detect(text)
    except Exception:
        return text

    if lang != 'de':
        return text

    replacements = {
        "die": "dee", "das": "dahs", "und": "oont", "ist": "ist", "ein": "ine",
        "hat": "haht", "ich": "ikh", "Sie": "zee", "wir": "veer",
        "zu": "tsoo", "von": "fon", "nicht": "nikht",
        "was": "vahs", "danke": "dahn-keh", "bitte": "bit-teh",
        "sehen": "zay-en", "schön": "shern", "freund": "froynt",
        "ergebnis": "air-gehp-nis", "viel": "feel", "halbes": "hahl-bes",
        "jahr": "yahr", "woche": "voh-khe", "monat": "moh-naht",
        "stunde": "shtoon-deh", "minute": "mee-noo-teh", "sekunde": "zeh-koon-deh",
        "tage": "tah-geh", "tag": "tahk", "halber": "hahl-ber", "mal": "mahl",
        "geteilt": "geh-tile-t", "plus": "ploos", "minus": "mee-noos", "gleich": "glykh",
        "quadratwurzel": "kvah-draht-voor-tsel", "hälfte": "helf-teh",
        "prozent": "proh-tsent", "ecken": "ek-en", "dreieck": "dry-eck",
        "ergibt": "air-gibt",
        "was ist die": "vahs ist dee", "was ergibt": "vahs air-gibt",
        "was sind": "vahs zint", "wie viele": "vee fee-leh", "wie viel": "vee feel",
        "dreizehn": "dry-tsayn", "vierzehn": "feer-tsayn", "fünfzehn": "foonf-tsayn",
    }

    for german_word, phonetic in replacements.items():
        pattern = re.compile(re.escape(german_word), re.IGNORECASE)
        text = pattern.sub(phonetic, text)

    return text


def get_random_background_video(folder_path):
    """Get a random background video from the specified folder."""
    video_files = [f for f in os.listdir(folder_path) if f.startswith('A (') and f.endswith(').mp4')]
    if not video_files:
        raise ValueError(f"No background videos found in {folder_path}")
    return os.path.join(folder_path, random.choice(video_files))


def delete_background_video(video_path):
    """Delete the background video file if it exists."""
    try:
        if os.path.exists(video_path):
            os.remove(video_path)
            print(f"Successfully deleted background video: {video_path}")
    except Exception as e:
        print(f"Error deleting background video: {e}")


# Constants
FONT_PATH = "./assets/avenir-next-bold.ttf" # Font path
PHOTO_PATH = r"D:\TIKTOK REELS\Clone\quiz_video_maker-1\assets\box.png" # Text box photo path
TIMER_VIDEO = r"D:\XV Material\Assets\Timers\Timer V4.mp4" # Timer video path

# Arabic letters for options
ARABIC_LETTERS = ['أ', 'ب', 'ج']  # Arabic letters for A, B, C


VIDEO_WIDTH = 1920
VIDEO_HEIGHT = 1080

p = inflect.engine()

# Updated Position configuration - moved text boxes to the left and made them bigger
POSITION_CONFIG = {
    "question": {"x": -435, "y": 800, "line_spacing": 100},  # was 950
    "question_part2": {"x": -435, "y": 760},  # was 910
    "options": {"x": 125, "y_base": 860, "option_spacing": 180},
    "photos": {
        "x_start": PHOTO_CONFIG["x_start"],
        "y": PHOTO_CONFIG["y"],
        "width": PHOTO_CONFIG["width"],
        "height": PHOTO_CONFIG["height"],
        "spacing": PHOTO_CONFIG["spacing"]
    },
    "text_boxes": {
        "x": TEXT_BOX_CONFIG["x"],  # Now 150 (moved much further left from 300)
        "y_base": TEXT_BOX_CONFIG["y_base"],
        "spacing": TEXT_BOX_CONFIG["spacing"],  # Now 170 (increased from 160)
        "width": TEXT_BOX_CONFIG["width"],  # Now 750 (increased from 700)
        "height": TEXT_BOX_CONFIG["height"]  # Now 150 (increased from 140)
    },
}

number_of_videos = 1

# UPDATED Account configurations with display_name
ACCOUNTS = [

    {
        "name": "Account1",
        "display_name": "@AGL LA3BAH",  # Display name for the account name text box
        "color_mode": "mode1",  # Color mode
        "csv_file": r"D:\XV Material\Assets\C_Arabic\3\C_Arabic 3.csv", # CSV file path
        "text_box": r"D:\XV Material\text box\T1.png", # Text box path
        "background_folder": r"D:\XV Material\XV Videos\Back Videos",  # Folder containing Background Videos
        "output_folder": r"D:\XV Material\Output\Phone 1",  # Path of the output video
        "photo_x": 1,  # X position for the text box
        "photo_y": 1,  # Y position for the text box
        "photo_width": 1,  # Width of the text box
        "photo_height": 1,  # Height of the text box
        "font_type": "./Assets/NotoSansArabic-Bold.ttf" # Font path
    },

    {
        "name": "Account2",
        "display_name": "@AGL LA3BAH",  # Display name for the account name text box
        "color_mode": "mode2",  # Color mode
        "csv_file": r"D:\XV Material\Assets\C_Arabic\3\C_Arabic 3.csv",  # CSV file path
        "text_box": r"D:\XV Material\text box\T1.png",  # Text box path
        "background_folder": r"D:\XV Material\XV Videos\Back Videos",  # Folder containing Background Videos
        "output_folder": r"D:\XV Material\Output\Phone 1",  # Path of the output video
        "photo_x": 1,  # X position for the text box
        "photo_y": 1,  # Y position for the text box
        "photo_width": 1,  # Width of the text box
        "photo_height": 1,  # Height of the text box
        "font_type": "./Assets/NotoSansArabic-Bold.ttf"  # Font path
    },

    {
        "name": "Account3",
        "display_name": "@AGL LA3BAH",  # Display name for the account name text box
        "color_mode": "mode3",  # Color mode
        "csv_file": r"D:\XV Material\Assets\C_Arabic\3\C_Arabic 3.csv",  # CSV file path
        "text_box": r"D:\XV Material\text box\T1.png",  # Text box path
        "background_folder": r"D:\XV Material\XV Videos\Back Videos",  # Folder containing Background Videos
        "output_folder": r"D:\XV Material\Output\Phone 1",  # Path of the output video
        "photo_x": 1,  # X position for the text box
        "photo_y": 1,  # Y position for the text box
        "photo_width": 1,  # Width of the text box
        "photo_height": 1,  # Height of the text box
        "font_type": "./Assets/NotoSansArabic-Bold.ttf"  # Font path
    },

    {
        "name": "Account4",
        "display_name": "@AGL LA3BAH",  # Display name for the account name text box
        "color_mode": "mode4",  # Color mode
        "csv_file": r"D:\XV Material\Assets\C_Arabic\3\C_Arabic 3.csv",  # CSV file path
        "text_box": r"D:\XV Material\text box\T1.png",  # Text box path
        "background_folder": r"D:\XV Material\XV Videos\Back Videos",  # Folder containing Background Videos
        "output_folder": r"D:\XV Material\Output\Phone 1",  # Path of the output video
        "photo_x": 1,  # X position for the text box
        "photo_y": 1,  # Y position for the text box
        "photo_width": 1,  # Width of the text box
        "photo_height": 1,  # Height of the text box
        "font_type": "./Assets/NotoSansArabic-Bold.ttf"  # Font path
    },





]


def insert_outro(main_video_path, outro_path, output_path, insert_time):
    """
    Insert outro video at a specific time in the main video
    - insert_time: seconds where to insert the outro (e.g., 30 for 30 seconds in)
    """
    try:
        # Get main video resolution
        probe_cmd = [
            'ffprobe', '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height',
            '-of', 'csv=s=x:p=0',
            main_video_path
        ]
        main_res = subprocess.check_output(probe_cmd).decode().strip()
        main_width, main_height = map(int, main_res.split('x'))

        # Get duration of outro
        probe_command = [
            'ffprobe', '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            outro_path
        ]
        outro_duration = float(subprocess.check_output(probe_command).strip())

        # Build FFmpeg command with scaling for outro video
        filter_complex = (
            f"[0:v]split=2[v1][v2];"
            f"[0:a]asplit=2[a1][a2];"
            # Start time --> insert time Video
            f"[v1]trim=0:{insert_time},setpts=PTS-STARTPTS[part1v];"
            # Start time --> insert time Auido
            f"[a1]atrim=0:{insert_time},asetpts=PTS-STARTPTS[part1a];"
            # Insert time --> end time Video
            f"[v2]trim=start={insert_time},setpts=PTS-STARTPTS[part2v];"
            # Insert time --> end time Audio
            f"[a2]atrim=start={insert_time},asetpts=PTS-STARTPTS[part2a];"
            f"[1:v]scale={main_width}:{main_height}[outro_v];"
            f"[part1v][part1a][outro_v][1:a][part2v][part2a]concat=n=3:v=1:a=1[outv][outa]"
        )

        command = [
            'ffmpeg',
            '-hide_banner', '-loglevel', 'error',
            '-y',
            '-i', main_video_path,
            '-i', outro_path,
            '-filter_complex', filter_complex,
            '-map', '[outv]',
            '-map', '[outa]',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-crf', '28',
            "-threads", "8",
            '-c:a', 'aac',
            '-b:a', '320k',
            output_path
        ]

        subprocess.run(command, check=True)
        return True

    except Exception as e:
        print(f"Failed to insert outro: {str(e)}")
        return False


def generate_random_filename(prefix="final_quiz_video", extension=".mp4"):
    """Generate a random filename with a given prefix and extension."""
    random_string = "".join(random.choices(string.ascii_letters + string.digits, k=8))
    return f"{prefix}_{random_string}{extension}"


# Semaphore to limit TTS requests
tts_semaphore = Semaphore(1)


def text_to_speech_file(args):
    """Convert text to speech and save as an audio file."""
    text, file_name = args

    # Convert numbers and math operations in the text to words
    text = replace_math_operations_multilingual(text)
    print(f"Processed Text: {text}")  # Debugging: Print the processed text

    with elevenlabs_semaphore:  # Acquire semaphore to limit concurrent requests
        try:
            response = client.text_to_speech.convert(
                voice_id="pNInz6obpgDQGcFmaJgB",
                optimize_streaming_latency="0",
                output_format="mp3_44100_128",
                text=text,
                model_id="eleven_multilingual_v2",
                voice_settings=VoiceSettings(
                    stability=0.7,
                    similarity_boost=1.0,
                    style=0.0,
                    use_speaker_boost=True,
                ),
            )

            os.makedirs("generated_audio", exist_ok=True)
            save_file_path = f"generated_audio/{file_name}.mp3"

            with open(save_file_path, "wb") as f:
                for chunk in response:
                    if chunk:
                        f.write(chunk)



            return save_file_path
        except Exception as e:
            print(f"Error generating audio for {file_name}: {e}")
            return None


def generate_audio_files(questions, correct_answers, cta_texts=None):
    """Generate all audio files for questions and answers, with special handling for last question."""
    audio_files = {}
    for i, (q, a) in enumerate(zip(questions, correct_answers)):
        # Generate audio for the question
        question_audio = text_to_speech_file((q, f"question_{i + 1}"))
        if question_audio:
            audio_files[f"question_{i + 1}"] = question_audio

        # Generate audio for the correct answer
        # NEW: Special handling for the last question
        if i == len(questions) - 1:  # Last question
            # Instead of revealing the answer, ask users to comment
            comment_text = "شارك اجابتك في التعليقات"
            answer_audio = text_to_speech_file((comment_text, f"answer_{i + 1}"))
        else:
            answer_audio = text_to_speech_file((a, f"answer_{i + 1}"))

        if answer_audio:
            audio_files[f"answer_{i + 1}"] = answer_audio

    # Return a dictionary mapping file names to paths
    return audio_files


def get_output_video_path(input_video_path, output_video_name='background_video_stable'):
    """Returns the path where the video will be saved (before creating it)"""
    # Ensure .mp4 extension
    if not output_video_name.endswith('.mp4'):
        output_video_name += '.mp4'
    return os.path.abspath(output_video_name)


def create_stable_background_video(input_video_path, output_video_name='background_video_stable'):
    """Actually generates the video at the pre-determined path"""
    output_path = get_output_video_path(input_video_path, output_video_name)

    cap = cv2.VideoCapture(input_video_path)
    if not cap.isOpened():
        print("Error: Could not open video file")
        return None

    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    # Go to 3-second mark
    cap.set(cv2.CAP_PROP_POS_FRAMES, int(6 * fps))
    ret, frame = cap.read()
    cap.release()

    if not ret:
        print("Error: Couldn't read frame at 6 seconds")
        return None

    # Generate 11-second stable video
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    for _ in range(int(11 * fps)):
        out.write(frame)
    out.release()

    print(f"Stable background video created: {output_path}")
    return output_path


def remove_green_screen_and_overlay(input_video_path, background_video_path, output_video_path):
    # FFmpeg command to remove green screen and overlay on the background
    command = [
        "ffmpeg",
        "-hide_banner", "-loglevel", "error",
        "-i", input_video_path,
        "-i", background_video_path,
        "-filter_complex",
        # Explicitly set TIMER_VIDEO audio volume to 1.0 to preserve its level
        "[0:v]chromakey=0x00FF00:0.1:0.2[fg];"
        "[1:v][fg]overlay=0:0[outv]",
        "-map", "[outv]",
        "-map", "0:a",  # Use TIMER_VIDEO audio as-is, no volume filter
        "-c:v", "libx264",
        "-preset", "ultrafast",
        "-crf", "28",
        "-threads", "8",
        "-c:a", "aac",
        "-y",
        output_video_path,
    ]
    subprocess.run(command, check=True)
    return output_video_path


def run_ffmpeg_command(command):
    """Run FFmpeg command using subprocess."""
    # Flatten any nested lists in the command
    flattened_command = []
    for item in command:
        if isinstance(item, list):
            flattened_command.extend(item)
        else:
            flattened_command.append(str(item))  # Ensure all items are strings

    print("Running FFmpeg command:")
    print(" ".join(flattened_command))

    # Ensure -hide_banner, -loglevel error, and -threads 8 are present
    if "-hide_banner" not in flattened_command:
        flattened_command.insert(1, "-hide_banner")
    if "-loglevel" not in flattened_command:
        flattened_command.insert(2, "-loglevel")
        flattened_command.insert(3, "error")
    if "-threads" in flattened_command:
        idx = flattened_command.index("-threads")
        flattened_command[idx + 1] = "8"
    else:
        flattened_command.insert(-1, "-threads")
        flattened_command.insert(-1, "8")

    subprocess.run(flattened_command, check=True)


def center_align_text(text, font, image_width):
    """Center-align text both horizontally per line and vertically as a block"""
    lines = text.split("\n")
    centered_lines = []

    for line in lines:
        text_width = font.getlength(line)
        x = (image_width - text_width) // 2
        centered_lines.append((x, line))

    return centered_lines


text_properties = {
    "question": {"fontsize": 80, "borderw": 0},
    "option": {"fontsize": 105, "borderw": 6},  # Increased from 95 to 105
    "answer": {"fontsize": 105, "borderw": 6},  # Increased from 95 to 105
}
text_properties1 = {
    "question": {"fontsize": 90, "borderw": 4},
    "option": {"fontsize": 130, "borderw": 6},  # Increased from 120 to 130
    "answer": {"fontsize": 130, "borderw": 6},  # Increased from 120 to 130
}


# UPDATED: create_final_video_with_dynamic_positioning function with account name text box overlay
def create_final_video_with_dynamic_positioning(output_video_path, questions, correct_answers, account_name,
                                                text_box_path,
                                                photo_x, photo_y, photo_width, photo_height, output_folder, font_type,
                                                all_question_photos, all_emoji_paths, all_text_box_paths,
                                                all_green_text_box_paths,
                                                all_question_text_box_paths, account_display_name, account_index, color_mode="mode1"):

    import tempfile

    # Constants for timing
    QUESTION_DURATION = 11  # Each question gets 11 seconds
    ANSWER_DISPLAY_TIME = 2.5  # Last 3 seconds show answer
    LAST_ANSWER_TIME = 8.5  # Changed to 8 seconds

    # Use actual number of questions - DYNAMIC based on random selection
    NUMBER_OF_QUESTIONS = len(questions)  # Dynamic number of questions

    TOTAL_DURATION = NUMBER_OF_QUESTIONS * QUESTION_DURATION  # 11 seconds for 1 question

    # Generate all audio files (no CTA)
    audio_files = generate_audio_files(questions, correct_answers)

    # Get dynamic configuration for the first question (representative)
    main_question = questions[0] if questions else ""
    dynamic_config = get_question_config(main_question)

    print(f"🎬 Using dynamic configuration based on question structure:")
    print(f"   Question newlines: {main_question.count('\\n')}")
    print(f"   Configuration type: {'short_question' if main_question.count('\\n') <= 1 else 'long_question'}")

    # Use the account-specific text box and positioning
    PHOTO_PATH = text_box_path
    PHOTO_X = photo_x
    PHOTO_Y = photo_y
    PHOTO_WIDTH = photo_width
    PHOTO_HEIGHT = photo_height
    FONT_PATH = font_type

    if not os.path.exists(FONT_PATH):
        abs_path = os.path.abspath(FONT_PATH)
        raise FileNotFoundError(
            f"Font file not found: {FONT_PATH}\n"
            f"Absolute path: {abs_path}\n"
            f"Current working directory: {os.getcwd()}"
        )

    try:
        font = ImageFont.truetype(FONT_PATH, text_properties["question"]["fontsize"])
        option_font = ImageFont.truetype(FONT_PATH, text_properties["option"]["fontsize"])
    except IOError as e:
        print(f"Failed to load font: {FONT_PATH}")
        raise

    # Generate a unique filename for the final video
    FINAL_OUTPUT = generate_random_filename()
    FINAL_OUTPUT_PATH = os.path.join(output_folder, FINAL_OUTPUT)

    # NEW: Create account name text box
    account_name_text_box_path = create_account_name_text_box(account_display_name, account_index)

    # Build final command
    command = [
        "ffmpeg", "-y", "-hide_banner", "-loglevel", "error",
        "-stream_loop", "-1", "-i", merged_video_path,  # Loop video if needed
    ]

    # Add question/answer audio inputs
    for i in range(1, NUMBER_OF_QUESTIONS + 1):
        command.extend(["-i", audio_files[f"question_{i}"]])
        command.extend(["-i", audio_files[f"answer_{i}"]])

    # Add text box input
    command.extend(["-i", PHOTO_PATH])

    # NEW: Add account name text box input
    command.extend(["-i", account_name_text_box_path])

    # Add all question text box inputs
    for question_text_box_path in all_question_text_box_paths:
        if question_text_box_path:
            command.extend(["-i", question_text_box_path])

    # Add all question photo inputs (3 photos per question)
    for question_photos in all_question_photos:
        for photo_path in question_photos:
            command.extend(["-i", photo_path])

    # Add all emoji image inputs (3 emojis per question)
    for emoji_paths in all_emoji_paths:
        for emoji_path in emoji_paths:
            if emoji_path:  # Only add if emoji path exists
                command.extend(["-i", emoji_path])

    # Add all white text box inputs (3 text boxes per question)
    for text_box_paths in all_text_box_paths:
        for text_box_path in text_box_paths:
            if text_box_path:  # Only add if text box path exists
                command.extend(["-i", text_box_path])

    # Add all green text box inputs
    for green_text_box_paths_for_question in all_green_text_box_paths:
        for green_text_box_path in green_text_box_paths_for_question:
            if green_text_box_path:  # Only add if green text box path exists
                command.extend(["-i", green_text_box_path])

    # Calculate indices dynamically
    text_box_idx = 1 + 2 * NUMBER_OF_QUESTIONS
    account_name_text_box_idx = text_box_idx + 1  # NEW: Account name text box index
    first_question_text_box_idx = account_name_text_box_idx + 1  # Updated to account for account name text box
    first_photo_idx = first_question_text_box_idx + len([p for p in all_question_text_box_paths if p])
    first_emoji_idx = first_photo_idx + (NUMBER_OF_QUESTIONS * 3)  # After all photos
    first_white_text_box_idx = first_emoji_idx + sum(
        len([p for p in paths if p]) for paths in all_emoji_paths)  # After all emojis
    first_green_text_box_idx = first_white_text_box_idx + sum(
        len([p for p in paths if p]) for paths in all_text_box_paths)  # After all white text boxes

    # Build filter complex with dynamic positioning
    filter_parts = [
        f"[0:v]trim=duration={TOTAL_DURATION},setpts=PTS-STARTPTS[quiz_vid];",
        f"[0:a]atrim=duration={TOTAL_DURATION},asetpts=PTS-STARTPTS[merged_audio];"
    ]

    audio_inputs = []
    question_delays = []
    current_time = 0
    for i in range(NUMBER_OF_QUESTIONS):
        question_delays.append(current_time)
        question_delays.append(current_time + QUESTION_DURATION - ANSWER_DISPLAY_TIME)
        current_time += QUESTION_DURATION

    for i in range(1, NUMBER_OF_QUESTIONS + 1):
        q_idx = 1 + (i - 1) * 2
        a_idx = q_idx + 1

        filter_parts.append(
            f"[{q_idx}:a]adelay={question_delays[(i - 1) * 2] * 1000}|{question_delays[(i - 1) * 2] * 1000},volume=1.0[question_{i}_a];")

        if i == NUMBER_OF_QUESTIONS:
            last_answer_delay = question_delays[(i - 1) * 2] + LAST_ANSWER_TIME
            filter_parts.append(
                f"[{a_idx}:a]adelay={int(last_answer_delay * 1000)}|{int(last_answer_delay * 1000)},volume=1.0[answer_{i}_a];")
        else:
            filter_parts.append(
                f"[{a_idx}:a]adelay={question_delays[(i - 1) * 2 + 1] * 1000}|{question_delays[(i - 1) * 2 + 1] * 1000},volume=1.0[answer_{i}_a];")

        audio_inputs.append(f"[question_{i}_a]")
        audio_inputs.append(f"[answer_{i}_a]")

    # Add text box overlay
    filter_parts.append(
        f"[{text_box_idx}:v]scale={PHOTO_WIDTH}:{PHOTO_HEIGHT}[scaled_text_box];"
        f"[quiz_vid][scaled_text_box]overlay={PHOTO_X}:{PHOTO_Y}:enable='between(t,0,{TOTAL_DURATION})'[v_with_textbox];"
    )

    # NEW: Add account name text box overlay (always visible throughout the video)
    account_name_config = ACCOUNT_NAME_TEXT_BOX_CONFIG
    filter_parts.append(
        f"[{account_name_text_box_idx}:v]scale={account_name_config['width']}:{account_name_config['height']}[scaled_account_name];"
        f"[v_with_textbox][scaled_account_name]overlay={account_name_config['x']}:{account_name_config['y']}:enable='between(t,0,{TOTAL_DURATION})'[v_with_account_name];"
    )

    # Add question text box overlays with dynamic positioning (updated current_stream)
    current_stream = "v_with_account_name"  # Updated to use the stream with account name
    current_time = 0
    question_text_box_input_idx = first_question_text_box_idx

    for i in range(NUMBER_OF_QUESTIONS):
        if i < len(all_question_text_box_paths) and all_question_text_box_paths[i]:
            # Get dynamic configuration for this specific question
            question_text = questions[i] if i < len(questions) else ""
            question_config = get_dynamic_question_text_box_config(question_text)

            question_box_width = question_config["width"]
            question_box_height = question_config["height"]
            question_box_x = question_config["x"]
            question_box_y = question_config["y"]

            next_stream = f"{current_stream}_qbox_{i}"

            filter_parts.append(
                f"[{question_text_box_input_idx}:v]scale={question_box_width}:{question_box_height}[scaled_question_box_{i}];"
                f"[{current_stream}][scaled_question_box_{i}]overlay={question_box_x}:{question_box_y}:enable='between(t,{current_time},{current_time + QUESTION_DURATION})'[{next_stream}];"
            )
            current_stream = next_stream
            question_text_box_input_idx += 1

        current_time += QUESTION_DURATION

    # Add 3-photo overlays with dynamic positioning
    current_time = 0

    for i in range(NUMBER_OF_QUESTIONS):
        # Get dynamic photo configuration for this specific question
        question_text = questions[i] if i < len(questions) else ""
        photo_config = get_dynamic_photo_config(question_text)

        # Calculate positions for 3 photos using dynamic config
        photo_width = photo_config["width"]
        photo_height = photo_config["height"]
        spacing = photo_config["spacing"]
        start_x = photo_config["x_start"]
        y_pos = photo_config["y"]

        print(f"📸 Question {i + 1} photos: {photo_width}x{photo_height} at y={y_pos} with {spacing}px spacing")

        # Add 3 photos for this question
        for photo_num in range(3):
            photo_idx = first_photo_idx + (i * 3) + photo_num
            x_pos = start_x + (photo_num * (photo_width + spacing))
            next_stream = f"v_with_q{i}_photo{photo_num}"

            filter_parts.append(
                f"[{photo_idx}:v]scale={photo_width}:{photo_height}[scaled_q{i}_photo{photo_num}];"
                f"[{current_stream}][scaled_q{i}_photo{photo_num}]overlay={x_pos}:{y_pos}:enable='between(t,{current_time},{current_time + QUESTION_DURATION})'[{next_stream}];"
            )
            current_stream = next_stream

        current_time += QUESTION_DURATION

    # Add white text box overlays with dynamic positioning
    current_time = 0
    white_text_box_input_idx = first_white_text_box_idx
    green_text_box_input_idx = first_green_text_box_idx

    for i in range(NUMBER_OF_QUESTIONS):
        # Get dynamic text box configuration for this specific question
        question_text = questions[i] if i < len(questions) else ""
        text_box_config = get_dynamic_text_box_config(question_text)

        text_box_paths = all_text_box_paths[i]
        green_text_box_paths_for_question = all_green_text_box_paths[i]

        print(
            f"📦 Question {i + 1} text boxes: {text_box_config['width']}x{text_box_config['height']} at base_y={text_box_config['y_base']} with {text_box_config['spacing']}px spacing")

        # Add 3 white text box overlays for this question
        for text_box_num in range(3):
            if text_box_paths[text_box_num]:  # Only add if text box exists
                # UPDATED: Account for complete circle space on RIGHT side with dynamic sizing
                circle_radius = 60  # Keep consistent circle radius
                extra_space = circle_radius * 2 + 20
                text_box_width = text_box_config["width"] + extra_space
                text_box_height = text_box_config["height"]
                text_box_x = text_box_config["x"]  # No left offset needed since circle is on right
                text_box_y = text_box_config["y_base"] + (text_box_num * text_box_config["spacing"])

                next_stream = f"{current_stream}_textbox_q{i}_{text_box_num}"

                filter_parts.append(
                    f"[{white_text_box_input_idx}:v]scale={text_box_width}:{text_box_height}[scaled_textbox_q{i}_{text_box_num}];"
                    f"[{current_stream}][scaled_textbox_q{i}_{text_box_num}]overlay={text_box_x}:{text_box_y}:enable='between(t,{current_time},{current_time + QUESTION_DURATION})'[{next_stream}];"
                )
                current_stream = next_stream
                white_text_box_input_idx += 1

        # Add green text box overlay for correct answer with dynamic positioning
        # NEW: Skip green reveal for the last question
        if i < NUMBER_OF_QUESTIONS - 1:  # Not the last question
            correct_letter = extract_correct_option_letter(correct_answers[i])
            if correct_letter in ['A', 'B', 'C']:
                correct_option_index = ord(correct_letter) - ord('A')
                green_text_box_path = green_text_box_paths_for_question[correct_option_index]

                if green_text_box_path:
                    circle_radius = 60
                    extra_space = circle_radius * 2 + 20
                    text_box_width = text_box_config["width"] + extra_space
                    text_box_height = text_box_config["height"]
                    text_box_x = text_box_config["x"]  # No left offset needed since circle is on right
                    text_box_y = text_box_config["y_base"] + (correct_option_index * text_box_config["spacing"])

                    green_reveal_start = current_time + GREEN_REVEAL_TIME
                    green_reveal_end = current_time + QUESTION_DURATION

                    next_stream = f"{current_stream}_green_textbox_q{i}"

                    filter_parts.append(
                        f"[{green_text_box_input_idx}:v]scale={text_box_width}:{text_box_height}[scaled_green_textbox_q{i}];"
                        f"[{current_stream}][scaled_green_textbox_q{i}]overlay={text_box_x}:{text_box_y}:enable='between(t,{green_reveal_start},{green_reveal_end})'[{next_stream}];"
                    )
                    current_stream = next_stream
                    green_text_box_input_idx += 1
        else:
            print(f"🚫 Skipping green reveal for last question {i + 1} - asking for comments instead")

        current_time += QUESTION_DURATION

    # Add emoji overlays positioned dynamically at the end of Arabic text
    current_time = 0
    emoji_input_idx = first_emoji_idx

    for i in range(NUMBER_OF_QUESTIONS):
        # Get dynamic configuration for this specific question
        question_text = questions[i] if i < len(questions) else ""
        emoji_config = get_dynamic_emoji_config(question_text)
        text_box_config = get_dynamic_text_box_config(question_text)

        emoji_paths = all_emoji_paths[i]
        answer_options = answers[i] if i < len(answers) else []

        print(f"🎭 Question {i + 1} emojis: size={emoji_config['size']}px positioned dynamically at end of Arabic text")

        # Add 3 emoji overlays for this question
        for emoji_num in range(3):
            if emoji_paths[emoji_num] and emoji_num < len(answer_options):  # Only add if emoji exists and option exists
                emoji_size = emoji_config["size"]

                # Get the option text to calculate its width
                option_text = extract_option_text(answer_options[emoji_num])
                processed_option_text = process_arabic_text(option_text)

                # Load font to calculate accurate text width
                try:
                    text_font = ImageFont.truetype("./Assets/NotoSansArabic-Bold.ttf",
                                                   text_box_config.get("option_text_size", 80))
                except:
                    try:
                        text_font = ImageFont.truetype("arial.ttf", text_box_config.get("option_text_size", 80))
                    except:
                        text_font = ImageFont.load_default()

                # UPDATED: Use helper function for accurate Arabic text measurement
                text_width = measure_arabic_text_width(
                    option_text,
                    "./Assets/NotoSansArabic-Bold.ttf",
                    text_box_config.get("option_text_size", 80)
                )

                # UPDATED: Dynamic positioning calculation for Arabic RTL text
                text_x_offset = text_box_config.get("text_x_offset", 60)

                # For Arabic RTL text, calculate the LEFT edge of the text (where it starts visually)
                text_right_edge_x = text_box_config["x"] + text_box_config["width"] - text_x_offset
                text_left_edge_x = text_right_edge_x - text_width

                # Position emoji at the left edge of the text (end of Arabic RTL text)
                emoji_x = text_left_edge_x + emoji_config["x_offset"] -30
                emoji_y = text_box_config["y_base"] + (emoji_num * text_box_config["spacing"]) + (
                        text_box_config["height"] // 2) - (emoji_size // 2) - 10

                print(
                    f"   🎯 Emoji {emoji_num + 1}: text_right_edge={text_right_edge_x}, text_left_edge={text_left_edge_x}, emoji_x={emoji_x}")

                next_stream = f"{current_stream}_emoji_q{i}_{emoji_num}"

                filter_parts.append(
                    f"[{emoji_input_idx}:v]scale={emoji_size}:{emoji_size}[scaled_emoji_q{i}_{emoji_num}];"
                    f"[{current_stream}][scaled_emoji_q{i}_{emoji_num}]overlay={emoji_x}:{emoji_y}:enable='between(t,{current_time},{current_time + QUESTION_DURATION})'[{next_stream}];"
                )
                current_stream = next_stream
                emoji_input_idx += 1

        current_time += QUESTION_DURATION

    # Add question text displays with dynamic timing and positioning
    current_time = 0
    for q_num in range(1, NUMBER_OF_QUESTIONS + 1):
        # Question text with dynamic positioning
        question = questions[q_num - 1]
        question_config = get_question_config(question)

        lines = question.split('\n')
        base_y = question_config["question_text"]["base_y"]
        line_spacing = question_config["question_text"]["line_spacing"]
        x_offset = question_config["question_text"]["x_offset"]

        print(f"📝 Question {q_num} text: base_y={base_y}, line_spacing={line_spacing}, lines={len(lines)}")

        for line_num, line in enumerate(lines[:3]):
            next_stream = f"{current_stream}q{q_num}{line_num}"
            x_pos = x_offset + (VIDEO_WIDTH - font.getlength(line)) // 2
            y_pos = base_y + line_num * line_spacing

            # Get color configuration based on mode
            color_config = COLOR_MODES.get(color_mode, COLOR_MODES["mode1"])["question_text_box"]

            # Convert RGB tuple to color name or hex
            text_color_rgb = color_config["text_color"]
            if text_color_rgb == (0, 0, 0):
                text_color = "black"
                border_color = "white"
            else:
                text_color = "white"
                border_color = "black"

            filter_parts.append(
                f"[{current_stream}]drawtext=fontfile={FONT_PATH}:"
                f"text='{ffmpeg_safe_text(line)}':"
                f"fontsize={text_properties['question']['fontsize']}:"
                f"fontcolor={text_color}:borderw={text_properties['question']['borderw']}:bordercolor={border_color}:"
                f"x={x_pos}:y={y_pos}:"
                f"enable='between(t,{current_time},{current_time + QUESTION_DURATION})'"
                f"[{next_stream}];"
            )
            current_stream = next_stream

        current_time += QUESTION_DURATION

    # ADD THIS NEW SECTION HERE - Follow me flicking text in last 3 seconds
    follow_me_start_time = TOTAL_DURATION - 2.6
    follow_me_end_time = TOTAL_DURATION
    flicker_enable = f"if(mod(floor((t-{follow_me_start_time})*{FOLLOW_ME_CONFIG['flicker_speed']}),2),1,0)*between(t,{follow_me_start_time},{follow_me_end_time})"

    next_stream = f"{current_stream}_follow_me"
    filter_parts.append(
        f"[{current_stream}]drawtext=fontfile={FONT_PATH}:"
        f"text='{FOLLOW_ME_CONFIG['text']}':"
        f"fontsize={FOLLOW_ME_CONFIG['fontsize']}:"
        f"fontcolor={FOLLOW_ME_CONFIG['fontcolor']}:"
        f"borderw=3:bordercolor=white:"
        f"x={FOLLOW_ME_CONFIG['x'] - len(FOLLOW_ME_CONFIG['text']) * FOLLOW_ME_CONFIG['fontsize'] // 4}:"  # Center the text
        f"y={FOLLOW_ME_CONFIG['y']}:"
        f"enable='{flicker_enable}'"
        f"[{next_stream}];"
    )
    current_stream = next_stream

    # Final audio mix and output
    filter_parts.append(
        f"[merged_audio]{''.join(audio_inputs)}amix=inputs={1 + len(audio_inputs)}:normalize=0:dropout_transition=0[a];")
    filter_parts.append(f"[{current_stream}]null[outv];")

    # Write filter complex to temporary file to avoid command line length issues
    filter_complex_content = "".join(filter_parts)

    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(filter_complex_content)
        filter_file_path = f.name

    try:
        command.extend([
            "-filter_complex_script", filter_file_path,
            "-map", "[outv]",
            "-map", "[a]",
            "-c:v", "libx264",
            "-preset", "ultrafast",
            "-crf", "28",
            "-threads", "4",
            "-c:a", "aac",
            "-b:a", "320k",
            "-t", str(TOTAL_DURATION),
            "-strict", "experimental",
            FINAL_OUTPUT_PATH
        ])

        subprocess.run(command, check=True)

        # Clean up account name text box after video creation
        if os.path.exists(account_name_text_box_path):
            os.remove(account_name_text_box_path)
            print(f"✅ Cleaned up account name text box: {account_name_text_box_path}")

        return FINAL_OUTPUT_PATH

    finally:
        # Clean up filter file
        try:
            os.unlink(filter_file_path)
        except:
            pass


def remove_background_video(file_path):
    """Removes the background video file if it exists."""
    try:
        os.remove(file_path)
        # print(f"Successfully removed {file_path}")
    except FileNotFoundError:
        print(f"{file_path} not found, nothing to remove.")
    except Exception as e:
        print(f"Error removing {file_path}: {e}")


def has_background_videos(folder_path):
    """Check if the folder contains any background videos."""
    try:
        video_files = [f for f in os.listdir(folder_path) if f.startswith('A (') and f.endswith(').mp4')]
        return len(video_files) > 0
    except Exception as e:
        print(f"ERROR CHECKING FOLDER: {str(e)}")
        return False


def ffmpeg_safe_text(text):
    """Sanitize text for FFmpeg drawtext filter."""
    # Remove newlines and carriage returns
    text = text.replace('\n', ' ').replace('\r', ' ')
    # Remove extra spaces
    text = ' '.join(text.split())
    # Escape single quotes (FFmpeg expects a single backslash)
    text = text.replace("'", r"\'")
    # Remove or escape double backslashes (avoid over-escaping)
    text = text.replace("\\", "/")
    return text


def get_video_duration(video_path):
    """Return duration of video in seconds."""
    try:
        result = subprocess.run(
            [
                'ffprobe', '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                video_path
            ],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )
        return float(result.stdout.strip())
    except Exception as e:
        print(f"Could not get duration for {video_path}: {e}")
        return None


def insert_video_at_time(main_video_path, insert_path, output_path, insert_time):
    """
    Insert a video at a specific time in the main video.
    """
    try:
        # Get main video resolution and SAR
        probe_cmd = [
            'ffprobe', '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height,sample_aspect_ratio',
            '-of', 'csv=p=0',
            main_video_path
        ]
        main_res = subprocess.check_output(probe_cmd).decode().strip()
        # main_res: width,height,sar (e.g., 1088,1920,135:136)
        parts = main_res.split(',')
        main_width, main_height = map(int, parts[:2])
        if len(parts) > 2 and parts[2] and parts[2] != 'N/A':
            main_sar = parts[2]
        else:
            main_sar = "1:1"

        # Build FFmpeg command with scaling and SAR for insert video
        filter_complex = (
            f"[0:v]split=2[v1][v2];"
            f"[0:a]asplit=2[a1][a2];"
            f"[v1]trim=0:{insert_time},setpts=PTS-STARTPTS[part1v];"
            f"[a1]atrim=0:{insert_time},asetpts=PTS-STARTPTS[part1a];"
            f"[v2]trim=start={insert_time},setpts=PTS-STARTPTS[part2v];"
            f"[a2]atrim=start={insert_time},asetpts=PTS-STARTPTS[part2a];"
            f"[1:v]scale={main_width}:{main_height},setsar={main_sar}[insert_v];"
            f"[part1v][part1a][insert_v][1:a][part2v][part2a]concat=n=3:v=1:a=1[outv][outa]"
        )

        command = [
            'ffmpeg',
            '-hide_banner', '-loglevel', 'error',
            '-y',
            '-i', main_video_path,
            '-i', insert_path,
            '-filter_complex', filter_complex,
            '-map', '[outv]',
            '-map', '[outa]',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-crf', '28',
            '-c:a', 'aac',
            "-threads", "8",
            '-b:a', '320k',
            output_path
        ]

        subprocess.run(command, check=True)
        return True

    except Exception as e:
        print(f"Failed to insert video: {str(e)}")
        return False


# Timing for green answer reveal (in seconds from start of question)
GREEN_REVEAL_TIME = 8.0  # Show green at 8 seconds

if __name__ == "__main__":
    freeze_support()

    print("🚀 STARTING DIRECT OPTION PHOTO SEARCH QUIZ VIDEO MAKER WITH ACCOUNT NAME TEXT BOX...")
    print("=" * 120)
    print("🎯 NEW FEATURE: ACCOUNT NAME TEXT BOX ON TOP OF EVERY VIDEO")
    print("   📺 Account name will appear in a gradient text box at the top of every video")
    print("   🎨 Beautiful gradient background with white text and shadow effects")
    print("   ⏱️ Visible throughout the entire video duration")
    print("=" * 120)
    print("🎯 ULTIMATE RULE: DIRECT OPTION TEXT PHOTO SEARCH")
    print("   📸 Photos are searched using EXACT option text only")
    print("   🚫 NO keyword enhancement or modification")
    print("   🚫 NO search term changes or substitutions")
    print("   ✅ If option A) Dog → search for 'Dog' and choose one photo")
    print("   ✅ If option B) Cat → search for 'Cat' and choose one photo")
    print("   ✅ If option C) Bird → search for 'Bird' and choose one photo")
    print("   🎯 Each photo corresponds EXACTLY to what the option says")
    print("=" * 120)

    # Initialize photo API manager once
    photo_api_manager = PhotoAPIManager()

    # Validate API keys
    print("🔧 VALIDATING API KEYS...")
    valid_keys = photo_api_manager.validate_api_keys()

    if not valid_keys:
        print("⚠️ No valid API keys found. Please update your API keys in the script.")
        print("📋 Get free API keys from:")
        print("   - Unsplash: https://unsplash.com/developers")
        print("   - Pixabay: https://pixabay.com/api/docs/")
        print("   - Pexels: https://www.pexels.com/api/")
        print("🔄 Continuing with fallback photos only...")
    else:
        print(f"✅ Found {len(valid_keys)} valid API key(s): {list(valid_keys.keys())}")

    # Validate DeepL API
    print("\n🌐 VALIDATING DEEPL TRANSLATION API...")
    deepl_valid = validate_deepl_connection()
    if not deepl_valid:
        print("⚠️ DeepL API validation failed. Translation may not work properly.")
        print("   Get a free DeepL API key from: https://www.deepl.com/api")
    print("=" * 50)


    print("=" * 120)

    # Track per-phone-folder account index in memory (reset every run)
    phone_folder_account_indices = {}

    for account_index, account in enumerate(ACCOUNTS, 1):
        account_name = account["name"]
        account_display_name = account.get("display_name",
                                           account_name)  # NEW: Get display name or fallback to account name

        print(f"\n📺 PROCESSING ACCOUNT: {account_name}")
        print(f"   🎨 Display Name: '{account_display_name}'")
        print(f"   📺 Account name text box will show: '{account_display_name}'")

        # IMPORTANT: Create a NEW emoji manager for each account
        print(f"🎨 Creating fresh emoji manager for {account_name}")
        emoji_manager = EmojiImageManager()

        # Use new CSV logic with preferred keys
        csv_q1 = account.get("csv_fileQ1") or account.get("csv_q1") or account.get("csv_file")
        csv_q2 = account.get("csv_fileQ2") or account.get("csv_q2") or account.get("csv_file")
        csv_q3_5 = account.get("csv_fileQ3_Q5") or account.get("csv_q3_5") or account.get("csv_file")
        csv_q6 = account.get("csv_fileQ6") or account.get("csv_q6") or account.get("csv_file")

        # Check if we're using a single CSV file for all questions
        csv_files = [csv_q1, csv_q2, csv_q3_5, csv_q6]
        unique_files = list(set(csv_files))
        using_single_file = len(unique_files) == 1

        print(f"\n📋 CSV CONFIGURATION DETECTED:")
        if using_single_file:
            print(f"   🗂️ SINGLE FILE MODE: Using 1 CSV file for all 6 questions")
            print(f"   📄 File: {unique_files[0]}")
        else:
            print(f"   🗂️ MULTIPLE FILE MODE: Using {len(unique_files)} different CSV files")
            print(f"   📄 Q1 File: {csv_q1}")
            print(f"   📄 Q2 File: {csv_q2}")
            print(f"   📄 Q3-5 File: {csv_q3_5}")
            print(f"   📄 Q6 File: {csv_q6}")

        text_box_path = account["text_box"]
        background_folder = account["background_folder"]
        output_folder = account["output_folder"]
        photo_x = account["photo_x"]
        photo_y = account["photo_y"]
        photo_width = account["photo_width"]
        photo_height = account["photo_height"]
        font_type = account["font_type"]

        if not has_background_videos(background_folder):
            print(f"ACCOUNT NUMBER {account_index} DOES NOT HAVE BACKGROUND VIDEOS IN ITS FOLDER")
            continue

        print(f"Processing account: {account_name}")
        os.makedirs(output_folder, exist_ok=True)

        # Rest of the existing code for phone folder setup...
        phone_folder = output_folder
        cta_videos_folder = os.path.join(phone_folder, "CTA Videos")
        os.makedirs(cta_videos_folder, exist_ok=True)

        if phone_folder not in phone_folder_account_indices:
            phone_folder_account_indices[phone_folder] = 0
        phone_folder_account_indices[phone_folder] += 1
        phone_account_index = phone_folder_account_indices[phone_folder]

        LIKE_VIDEO_PATH = os.path.join(cta_videos_folder, f"{phone_account_index}_Like.mp4")
        RADER_VIDEO_PATH = os.path.join(cta_videos_folder, f"{phone_account_index}_Rader.mp4")
        SAVE_VIDEO_PATH = os.path.join(cta_videos_folder, f"{phone_account_index}_Save.mp4")

        for i in range(number_of_videos):
            print(
                f"\n🎬 Creating video {i + 1} of {number_of_videos} for {account_name} with account name '{account_display_name}'...")

            # CRITICAL FIX: Reset emoji tracking for EACH video within the account
            emoji_manager.reset_quiz_emoji_tracking()
            print(f"🔄 Reset emoji tracking for video {i + 1} of {account_name}")

            output_video_path = f"merged_video_{i}.mp4"
            final_output_path = os.path.join(output_folder, f"final_video_{i}.mp4")
            BACKGROUND_VIDEO = get_random_background_video(background_folder)
            print(f"Using background video: {BACKGROUND_VIDEO}")
            background_video_path = BACKGROUND_VIDEO
            background_video_stable = create_stable_background_video(BACKGROUND_VIDEO)

            questions = []
            answers = []
            correct_answers = []

            # Load questions logic - UPDATED TO HANDLE BOTH SCENARIOS
            used_rows = set()


            def load_random_row(csv_path, used_rows):
                with open(csv_path, mode='r', encoding='utf-8') as file:
                    csv_reader = csv.reader(file)
                    rows = [tuple(row) for row in csv_reader if row and any(cell.strip() for cell in row)]
                    # Skip header if present
                    if rows and rows[0][0].lower().startswith("question"):
                        rows = rows[1:]
                    available_rows = [row for row in rows if row not in used_rows]
                    if not available_rows:
                        raise ValueError(f"No unused data rows in {csv_path}")
                    chosen = random.choice(available_rows)
                    used_rows.add(chosen)
                    return chosen


            def load_multiple_random_rows(csv_path, num_rows, used_rows):
                """Load multiple random rows from a single CSV file"""
                with open(csv_path, mode='r', encoding='utf-8') as file:
                    csv_reader = csv.reader(file)
                    rows = [tuple(row) for row in csv_reader if row and any(cell.strip() for cell in row)]
                    # Skip header if present
                    if rows and rows[0][0].lower().startswith("question"):
                        rows = rows[1:]
                    available_rows = [row for row in rows if row not in used_rows]
                    if len(available_rows) < num_rows:
                        raise ValueError(
                            f"Not enough unused data rows in {csv_path}. Need {num_rows}, have {len(available_rows)}")
                    chosen_rows = random.sample(available_rows, num_rows)
                    for row in chosen_rows:
                        used_rows.add(row)
                    return chosen_rows


            try:

                selected_rows = []

                # NEW: Randomly select number of questions between 1 and 6
                num_questions = 6
                print(f"🎲 Randomly selected {num_questions} questions for this video")

                if using_single_file:
                    # SINGLE FILE MODE: Load random number of questions from single CSV...
                    print(f"📋 SINGLE FILE MODE: Loading {num_questions} random questions from single CSV...")
                    single_csv_path = unique_files[0]

                    # Load random number of questions from the single file
                    all_questions = load_multiple_random_rows(single_csv_path, num_questions, used_rows)
                    selected_rows.extend(all_questions)

                    print(f"✅ Single File Mode: {num_questions} questions loaded successfully")
                    print(f"   📊 {num_questions} questions loaded from: {single_csv_path}")

                else:
                    # MULTIPLE FILE MODE: Load questions from appropriate CSV files
                    print(f"📋 MULTIPLE FILE MODE: Loading {num_questions} questions from CSV files...")

                    questions_loaded = 0

                    # Load questions based on the random number selected
                    if questions_loaded < num_questions and csv_q1:
                        q1_row = load_random_row(csv_q1, used_rows)
                        selected_rows.append(q1_row)
                        questions_loaded += 1
                        print("✅ Question 1 loaded from csv_q1")

                    if questions_loaded < num_questions and csv_q2:
                        q2_row = load_random_row(csv_q2, used_rows)
                        selected_rows.append(q2_row)
                        questions_loaded += 1
                        print("✅ Question 2 loaded from csv_q2")

                    # Load remaining questions from csv_q3_5
                    while questions_loaded < num_questions and csv_q3_5:
                        if questions_loaded < 5:  # Questions 3-5 come from csv_q3_5
                            q_row = load_random_row(csv_q3_5, used_rows)
                            selected_rows.append(q_row)
                            questions_loaded += 1
                            print(f"✅ Question {questions_loaded} loaded from csv_q3_5")
                        else:  # Question 6 comes from csv_q6
                            if csv_q6:
                                q6_row = load_random_row(csv_q6, used_rows)
                                selected_rows.append(q6_row)
                                questions_loaded += 1
                                print("✅ Question 6 loaded from csv_q6")
                            break

                print(f"🎯 Total questions loaded: {len(selected_rows)}")

                # Validation check
                if len(selected_rows) != num_questions:
                    raise ValueError(f"Expected {num_questions} questions, but loaded {len(selected_rows)}")



            except Exception as e:
                print(f"❌ Error loading questions: {e}")
                if using_single_file:
                    print(f"   Check that {unique_files[0]} has at least 6 valid question rows")
                else:
                    print(f"   Check that all CSV files have sufficient question rows")
                continue

            # Process the loaded questions (this part remains the same)
            for row in selected_rows:
                question = row[0].replace("\\n", "\n")
                option_a = row[1] if row[1].startswith("A)") else f"A) {row[1]}"
                option_b = row[2] if row[2].startswith("B)") else f"B) {row[2]}"
                option_c = row[3] if row[3].startswith("C)") else f"C) {row[3]}"
                correct_answer = f"Answer {row[4]}"
                questions.append(question)
                answers.append([option_a, option_b, option_c])
                correct_answers.append(correct_answer)

            # Debug: Print questions, answers, and correct_answers
            print(f"\n🎯 QUIZ SUMMARY - {len(questions)} QUESTIONS LOADED:")
            # NEW: Detect language of first question for consistent translation
            quiz_language = 'en'  # Default to English
            if questions:
                quiz_language = detect_language_of_first_question(questions[0])
                print(f"🌐 QUIZ LANGUAGE DETECTED: {quiz_language}")
                print(f"   All options will be translated FROM {quiz_language} TO English for search")
            else:
                print("⚠️ No questions found, defaulting to English")

            print(f"Number of questions: {len(questions)}")
            print(f"Loading Method: {'SINGLE FILE' if using_single_file else 'MULTIPLE FILES'}")
            print("=" * 80)

            for i, (q, a, ca) in enumerate(zip(questions, answers, correct_answers), 1):
                print(f"Question {i}: {q[:50]}...")
                print(f"Options {i}: {[opt[:20] + '...' if len(opt) > 20 else opt for opt in a]}")
                print(f"Correct Answer {i}: {ca}")
                print("-" * 40)

            print("=" * 80)
            print(f"✅ {len(questions)} QUESTIONS LOADED AND READY FOR PROCESSING!")
            print(f"🎲 Random selection: {len(questions)} questions (min: 1, max: 6)")
            print(f"🚫 Last question will NOT reveal answer - asking for comments instead!")

            # Get account's color mode
            account_color_mode = account.get("color_mode", "mode1")
            print(f"🎨 Using color mode: {account_color_mode} for {account_name}")

            all_question_text_box_paths = []
            video_letter_circle_color = generate_random_letter_circle_color()
            print(f"🎨 Generated letter circle color for video {i + 1}: RGB{video_letter_circle_color}")
            for idx, question in enumerate(questions):
                question_text_box_path = create_question_text_box_dynamic(question, idx,
                                                                          account_color_mode)  # Add color mode
                all_question_text_box_paths.append(question_text_box_path)
                print(f"✅ Question {idx + 1} text box created with random stroke color and {account_color_mode}")

            print(f"\n🌐 TRANSLATION STATUS CHECK...")
            print(f"   🎯 FIRST QUESTION LANGUAGE: {quiz_language}")
            print(f"   🔧 ALL OPTIONS will be translated FROM {quiz_language} TO English")
            print(f"   ✅ Consistent translation approach across entire quiz")
            print("=" * 50)

            # Get 3 photos for each question using DIRECT OPTION TEXT SEARCH
            print(f"\n🎬 STARTING DIRECT OPTION TEXT PHOTO SEARCH FOR VIDEO {i + 1}...")
            print("=" * 50)
            all_question_photos = []
            for idx, answer_options in enumerate(answers):
                photos_for_this_question = get_photos_for_options(answer_options, idx, photo_api_manager, quiz_language=quiz_language, num_photos=3)
                all_question_photos.append(photos_for_this_question)
                print(
                    f"✅ Question {idx + 1} photos: {len(photos_for_this_question)} photos collected (DIRECT option text search)")

            print("=" * 50)
            print(f"✅ DIRECT OPTION TEXT PHOTO SEARCH COMPLETE FOR VIDEO {i + 1}!")

            # Create white text boxes and green text boxes for each question's options
            print(f"\n📦 STARTING WHITE AND GREEN TEXT BOX CREATION FOR VIDEO {i + 1}...")
            print("=" * 50)
            all_text_box_paths = []
            all_green_text_box_paths = []
            for idx, (answer_options, correct_answer) in enumerate(zip(answers, correct_answers)):
                text_box_paths_for_question, green_text_box_paths_for_question = create_white_text_boxes_with_dynamic_positioning(
                    idx, answer_options, correct_answer, video_letter_circle_color, questions[idx],
                    account_color_mode)  # Add color mode
                all_text_box_paths.append(text_box_paths_for_question)
                all_green_text_box_paths.append(green_text_box_paths_for_question)

                print(
                    f"✅ Question {idx + 1} text boxes: {len(text_box_paths_for_question)} white + {len([p for p in green_text_box_paths_for_question if p])} green text boxes created with random strokes and letter circle color RGB{video_letter_circle_color}")
            print("=" * 50)
            print(f"✅ WHITE AND GREEN TEXT BOX CREATION COMPLETE FOR VIDEO {i + 1}!")

            # Get emoji images for each question's options with COMPREHENSIVE EMOJI SYSTEM
            print(f"\n🎨 STARTING COMPREHENSIVE EMOJI INTELLIGENCE SYSTEM FOR {account_name} VIDEO {i + 1}...")
            print("=" * 80)
            all_emoji_paths = []
            for idx, answer_options in enumerate(answers):
                # Create unique identifier for this video
                video_identifier = f"acc{account_index}_vid{i + 1}"
                emoji_paths_for_question = get_emoji_images_for_options(answer_options, idx, emoji_manager
                                                                        , account_index, i,)

                all_emoji_paths.append(emoji_paths_for_question)
                print(
                    f"🎭 Question {idx + 1} emojis: {len([p for p in emoji_paths_for_question if p])}/3 emojis collected with comprehensive intelligence")

            print("=" * 80)
            print(f"🎨 COMPREHENSIVE EMOJI INTELLIGENCE SYSTEM COMPLETE FOR {account_name} VIDEO {i + 1}!")
            print(f"📊 Total unique semantic emojis used in this video: {len(emoji_manager.used_emojis_in_quiz)}")
            print(
                f"🎯 Emoji intelligence success rate: {len([p for paths in all_emoji_paths for p in paths if p])}/{len(answers) * 3}")

            # Language detection for TTS
            if questions:
                first_question = questions[0]
                try:
                    detected_language = detect(first_question)
                    print(f"Detected language: {detected_language}")
                except:
                    detected_language = "en"
                    print("Language detection failed. Defaulting to English.")
            else:
                detected_language = "en"
                print("No questions found. Defaulting to English.")


            def replace_math_operations_multilingual(text, language=None):
                """
                Replaces mathematical operations with their word equivalents in the specified language.
                If no language is provided, it defaults to the detected language.
                """
                if language is None:
                    language = detected_language

                math_to_words = {
                    "en": {  # English
                        '+': ' plus ',
                        ' + ': ' plus ',
                        '-': ' minus ',
                        ' - ': ' minus ',
                        '×': ' times ',
                        ' × ': ' times ',
                        '*': ' times ',
                        '/': ' out of ',
                        "let us go": "Let's go",
                        "you are": "you're",
                        "You are": "you're",
                        '÷': ' divided by ',
                        ' ÷ ': ' divided by ',
                        '^': ' to the power of ',
                        'Answer': ' Answer ',
                        '(AS)': '(عليه السلام)',
                        'WHICH': 'Which',
                    },
                    "de": {  # German
                        '+': ' plus ',
                        ' + ': ' plus ',
                        '-': ' minus ',
                        ' - ': ' minus ',
                        '×': ' mal ',
                        ' × ': ' mal ',
                        '*': ' mal ',
                        '/': ' geteilt durch ',
                        '÷': ' geteilt durch ',
                        ' ÷ ': ' geteilt durch ',
                        '=': ' gleich ',
                        '^': ' hoch ',
                        'Answer': ' Antwort ',
                        '√': 'die Quadratwurzel von',
                        '√ ': 'die Quadratwurzel von',
                    },
                    "es": {  # Spanish
                        '+': ' más ',
                        ' + ': ' más ',
                        '-': ' menos ',
                        ' - ': ' menos ',
                        '×': ' por ',
                        ' × ': ' por ',
                        '*': ' por ',
                        '/': ' dividido por ',
                        '÷': ' dividido por ',
                        ' ÷ ': ' dividido por ',
                        '=': ' igual a ',
                        '^': ' elevado a la ',
                        'Answer': ' Respuesta ',
                        '√': 'la raíz cuadrada de',
                        '√ ': 'la raíz cuadrada de',
                    },
                    "fr": {  # Spanish
                        '+': ' más ',
                        ' + ': ' más ',
                        '-': ' menos ',
                        ' - ': ' menos ',
                        '×': ' por ',
                        ' × ': ' por ',
                        '*': ' por ',
                        '/': ' dividido por ',
                        '÷': ' dividido por ',
                        ' ÷ ': ' dividido por ',
                        '=': ' igual a ',
                        '^': ' elevado a la ',
                        'Answer': ' Respuesta ',
                        '√': 'la raíz cuadrada de',
                        '√ ': 'la raíz cuadrada de',
                    },
                    "ar": {
                        '+': ' زائد ',
                        ' + ': ' زائد ',
                        '-': ' ناقص ',
                        ' - ': ' ناقص ',
                        '×': ' ضرب ',
                        ' × ': ' ضرب ',
                        '*': ' ضرب ',
                        '/': ' مقسوم على ',
                        '÷': ' مقسوم على ',
                        ' ÷ ': ' مقسوم على ',
                        '=': ' يساوي ',
                        '^': ' مرفوع إلى ',
                        'Answer': 'الإجابة',
                        '√': ' الجذر التربيعي لـ',
                        '√ ': ' الجذر التربيعي لـ',
                    }

                }

                # Select the appropriate dictionary based on the detected language
                if language in math_to_words:
                    math_to_words_dict = math_to_words[language]
                else:
                    # Default to English if the language is not supported
                    math_to_words_dict = math_to_words["en"]

                # Replace each math operation with its word equivalent
                for operation, word in math_to_words_dict.items():
                    text = text.replace(f" {operation} ", f" {word} ")
                    text = text.replace(f"{operation} ", f"{word} ")
                    text = text.replace(f" {operation}", f" {word}")
                    text = text.replace(operation, word)

                # Clean up extra spaces
                text = ' '.join(text.split())

                return text


            # Merge the background video and timer video for this iteration
            merged_video_path = generate_random_filename(prefix="merged_video", extension=".mp4")
            print(f"Merging background and timer video: {merged_video_path}")
            merged_video_path = remove_green_screen_and_overlay(
                input_video_path=TIMER_VIDEO,
                background_video_path=background_video_path,
                output_video_path=merged_video_path
            )

            # Only process the first question for overlays
            question_lines = questions[0].split('\n')
            num_lines = len(question_lines)
            if questions[0].count('\n') <= 1:
                y_position = POSITION_CONFIG['question_part2']['y']
            else:
                y_position = POSITION_CONFIG['question']['y']

            # Generate a unique filename for each iteration
            FINAL_OUTPUT = generate_random_filename()

            actual_final_output_path = create_final_video_with_dynamic_positioning(
                output_video_path,
                questions,
                correct_answers,
                account_name,
                text_box_path,
                photo_x,
                photo_y,
                photo_width,
                photo_height,
                output_folder,
                font_type,
                all_question_photos,  # Pass the 3D array of photos
                all_emoji_paths,  # Pass the COMPREHENSIVE emoji image paths
                all_text_box_paths,  # Pass the white text box paths with random strokes
                all_green_text_box_paths,  # Pass the green text box paths
                all_question_text_box_paths,  # Pass the question text box paths with random strokes
                account_display_name,  # NEW: Pass the account display name
                account_index,  # NEW: Pass the account index
                account_color_mode  # NEW: Pass the color mode
            )

            try:
                # If we reach here, it means the video was generated successfully
                print(
                    f"✅ Video {i + 1} generated successfully with ACCOUNT NAME TEXT BOX, DIRECT OPTION TEXT PHOTO SEARCH AND EMOJIS!")

                # Clean up the generated audio files
                shutil.rmtree("generated_audio", ignore_errors=True)

                # Clean up temporary video files
                if os.path.exists(output_video_path):
                    os.remove(output_video_path)

                # Delete the background video after successful generation
                delete_background_video(BACKGROUND_VIDEO)

                # Clean up the merged video
                if os.path.exists(merged_video_path):
                    os.remove(merged_video_path)

                # Clean up question text boxes
                for question_text_box_path in all_question_text_box_paths:
                    if question_text_box_path and os.path.exists(question_text_box_path):
                        os.remove(question_text_box_path)

                # Clean up question photos (all 3 photos per question)
                for question_photos in all_question_photos:
                    for photo_path in question_photos:
                        if os.path.exists(photo_path):
                            os.remove(photo_path)

                # Clean up emoji images for this video (but keep downloaded emojis for reuse)
                for emoji_paths in all_emoji_paths:
                    for emoji_path in emoji_paths:
                        if emoji_path and os.path.exists(emoji_path):
                            os.remove(emoji_path)

                # Clean up white text box images
                for text_box_paths in all_text_box_paths:
                    for text_box_path in text_box_paths:
                        if text_box_path and os.path.exists(text_box_path):
                            os.remove(text_box_path)

                # Clean up green text box images
                for green_text_box_paths_for_question in all_green_text_box_paths:
                    for green_text_box_path in green_text_box_paths_for_question:
                        if green_text_box_path and os.path.exists(green_text_box_path):
                            os.remove(green_text_box_path)

            except Exception as e:
                print(f"Error processing video {i + 1}: {str(e)}")
                # Don't delete the background video if there was an error
                continue

            print(
                f"✅ Completed video {i + 1} for {account_name} with account name text box '{account_display_name}' and emojis!")

            # Clean up the generated audio files after each iteration
            shutil.rmtree("generated_audio", ignore_errors=True)

            # Clean up temporary video files
            if os.path.exists(output_video_path):
                os.remove(output_video_path)

            # Clean up the background video file after all iterations are done
            input_video_path = "BACKGROUND_VIDEO.mp4"
            remove_background_video(input_video_path)
            remove_background_video(merged_video_path)

            merged_output_path = os.path.join(
                output_folder, f"with_outro_{os.path.basename(actual_final_output_path)}"
            )

            # Verify the main video exists before merging
            if os.path.exists(actual_final_output_path):
                # Check if all CTA videos exist before attempting to insert
                cta_missing = False
                for cta_path, label in [
                    (SAVE_VIDEO_PATH, "Save"),
                    (LIKE_VIDEO_PATH, "Like"),
                    (RADER_VIDEO_PATH, "Rader"),
                ]:
                    if not os.path.exists(cta_path):
                        print(
                            f"WARNING: {label}.mp4 not found at {cta_path}. Skipping CTA inserts. Saving original video only.")
                        cta_missing = True
                        break

                if cta_missing:
                    # Do NOT save the with_outro_... version if any CTA is missing
                    print("No CTA videos found, skipping with_outro_... save.")
                else:
                    # Always use the same CTA flow/order for all accounts:
                    # 11s = Rader, 33s = Like, 55s = Save
                    # Merge in reverse order to avoid shifting insert points
                    insert_points = [
                        (55, SAVE_VIDEO_PATH),
                        (33, LIKE_VIDEO_PATH),
                        (11, RADER_VIDEO_PATH),
                    ]
                    temp_path = actual_final_output_path
                    for idx, (insert_time, insert_path) in enumerate(insert_points):
                        next_temp_path = temp_path.replace(".mp4", f"_insert{idx}.mp4")
                        print(f"Inserting {insert_path} at {insert_time:.2f} seconds into {temp_path}...")
                        if insert_video_at_time(temp_path, insert_path, next_temp_path, insert_time):
                            if temp_path != actual_final_output_path:
                                os.remove(temp_path)
                            temp_path = next_temp_path
                            print(f"Inserted {insert_path} at {insert_time:.2f}s.")
                        else:
                            print(f"Insertion failed for {insert_path} at {insert_time:.2f}s - keeping previous video")
                    # Move final temp_path to merged_output_path if needed
                    if temp_path != merged_output_path:
                        shutil.move(temp_path, merged_output_path)
                    # Remove the original final video if the merged video was created
                    if os.path.exists(merged_output_path) and os.path.exists(actual_final_output_path):
                        try:
                            os.remove(actual_final_output_path)
                        except Exception as e:
                            print(f"Warning: Could not remove original video: {e}")
                    print(f"All inserts complete. Final video: {merged_output_path}")
            else:
                print(f"Error: Generated video not found at {actual_final_output_path}")

            # Clean up photos folder after processing each video
            if os.path.exists("photos"):
                shutil.rmtree("photos")

            # Clean up text boxes folder after processing each video
            if os.path.exists("text_boxes"):
                shutil.rmtree("text_boxes")

        # Clean up emoji images folder after processing all videos for this account
        if os.path.exists("emoji_images"):
            shutil.rmtree("emoji_images")

        print(f"🎉 ALL 1-QUESTION VIDEOS FOR {account_name} COMPLETED SUCCESSFULLY WITH ACCOUNT NAME TEXT BOX AND EMOJIS!")


    print("\n🎉 ALL ACCOUNTS PROCESSED SUCCESSFULLY WITH TRANSLATION-ENHANCED SEARCH!")
    print("=" * 120)
    print("🌐 NEW TRANSLATION-ENHANCED SEARCH SUMMARY:")
    print("   ✅ OPTIONS AUTOMATICALLY TRANSLATED TO ENGLISH FOR SEARCH")
    print("   🔍 Photos searched using English translations for better results")
    print("   🎭 Emojis matched using English translations for accuracy")
    print("   📺 Original language preserved for quiz display")
    print("   🌍 Supports multiple languages (German, French, Spanish, etc.)")
    print("   🎯 Improved search accuracy through DeepL translation")
    print("=" * 120)
    print("🎯 SEARCH ENHANCEMENT BENEFITS:")
    print("   📸 Better photo matches regardless of original language")
    print("   🎭 More accurate emoji selection")
    print("   🔄 Automatic language detection and translation")
    print("   📊 Higher success rate in finding relevant content")
    print("=" * 120)
    print("🏆 ACHIEVEMENT UNLOCKED: MULTILINGUAL 1-QUESTION QUIZ WITH TRANSLATION-ENHANCED SEARCH!")

