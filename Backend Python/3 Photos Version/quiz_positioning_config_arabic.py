# =============================================================================
# DYNAMIC POSITIONING CONFIGURATION BASED ON QUESTION NEWLINES
# =============================================================================
#
# SIMPLE EDITING GUIDE:
# - If question has 1 \n or less: use "short_question" settings
# - If question has 2 \n or more: use "long_question" settings
#
# You can easily modify positions, sizes, and other parameters below
# =============================================================================



# Enhanced configuration with newline-based positioning
DYNAMIC_POSITION_CONFIG = {
    # Configuration for questions with 1 \n or less (short questions)
    "short_question": {
        "question_text": {
            "base_y": 305,  # Y position for question text (higher up)
            "line_spacing": 90,  # Tighter line spacing
            "x_offset": -285  # X offset from center
        },
        "photos": {
            "x_start": 70,  # Starting X position for photos
            "y": 490,  # Y position for photos (higher up)
            "width": 295,  # Smaller width for photos
            "height": 360,  # Smaller height for photos
            "spacing": 25  # Tighter spacing between photos
        },
        "text_boxes": {
            "x": 110,  # X position for text boxes
            "y_base": 960,  # Base Y position for first text box
            "spacing": 205,  # Vertical spacing between text boxes
            "width": 900,  # Width of each text box
            "height": 190  # Height of each text box
        },
        "question_box": {
            "x": 30,  # X position for question text box
            "y": 290,  # Y position for question text box
            "width": 1030,  # Width of question text box
            "height": 610  # Height of question text box
        },
        "emojis": {
            "size": 150,  # Size of emoji images
            "x_offset": -160,  # Distance from end of option text
            "y_adjustment": -20  # Additional Y adjustment for emojis
        }
    },

    # Configuration for questions with 2 \n or more (long questions)
    "long_question": {
        "question_text": {
            "base_y": 305,  # Y position for question text (higher up)
            "line_spacing": 90,  # Tighter line spacing
            "x_offset": -340  # X offset from center
        },
        "photos": {
            "x_start": 70,  # Starting X position for photos
            "y": 570,  # Y position for photos (higher up)
            "width": 295,  # Smaller width for photos
            "height": 290,  # Smaller height for photos
            "spacing": 25  # Tighter spacing between photos
        },
        "text_boxes": {
            "x": 110,  # X position for text boxes
            "y_base": 960,  # Base Y position for first text box
            "spacing": 205,  # Vertical spacing between text boxes
            "width": 900,  # Width of each text box
            "height": 190  # Height of each text box
        },
        "question_box": {
            "x": 30,  # X position for question text box
            "y": 290,  # Y position for question text box
            "width": 1030,  # Width of question text box
            "height": 610  # Height of question text box
        },
        "emojis": {
            "size": 150,  # Size of emoji images
            "x_offset": -160,  # Distance from end of option text
            "y_adjustment": -25  # Additional Y adjustment for emojis
        }
    }
}



def get_question_config(question_text):
    """
    Determine which configuration to use based on newline count
    Returns: configuration dict for the question type
    """
    newline_count = question_text.count('\n')

    if newline_count <= 1:
        config_type = "short_question"
    else:
        config_type = "long_question"

    print(f"📏 Question has {newline_count} newlines → using '{config_type}' configuration")
    return DYNAMIC_POSITION_CONFIG[config_type]


# Update the original PHOTO_CONFIG to be dynamic
def get_dynamic_photo_config(question_text):
    """Get photo configuration based on question length"""
    config = get_question_config(question_text)
    photos = config["photos"]

    return {
        "width": photos["width"],
        "height": photos["height"],
        "spacing": photos["spacing"],
        "total_width": photos["width"] * 3 + photos["spacing"] * 2,
        "x_start": photos["x_start"],
        "y": photos["y"]
    }


# Update the original TEXT_BOX_CONFIG to be dynamic
def get_dynamic_text_box_config(question_text):
    """Get text box configuration based on question length"""
    config = get_question_config(question_text)
    text_boxes = config["text_boxes"]

    return {
        "width": text_boxes["width"],
        "height": text_boxes["height"],
        "x": text_boxes["x"],
        "y_base": text_boxes["y_base"],
        "spacing": text_boxes["spacing"],
        "corner_radius": 30,  # Rounded corner radius (bigger radius)
        "border_width": 7,  # Increased border width for more prominent stroke
        "fill_color": (255, 255, 255),  # White fill
        "text_color": (0, 0, 0),  # Black text
        "shadow_color": (0, 0, 0, 180),  # Black shadow with transparency
        "shadow_offset": (3, 3),  # Shadow offset (x, y)
        "letter_circle_radius": 80,  # UPDATED: Increased radius for bigger circles (was 60)
        "letter_circle_color": (138, 43, 226),  # Purple color for letter circles
        "letter_text_color": (255, 255, 255),  # White text for letters
        "letter_offset_x": -370,  # X offset for letter circle from text box center (adjusted for bigger box)
        # Green answer reveal colors
        "correct_fill_color": (34, 197, 94),  # Green fill for correct answer
        "correct_border_color": (22, 163, 74),  # Darker green border for correct answer
        "correct_letter_circle_color": (21, 128, 61),  # Dark green for letter circle
        "option_text_size": 100,  # Larger font size for option text
        "letter_text_size": 105,  # Larger font size for letter text
        # Add these inside the dictionary returned by get_dynamic_text_box_config
        "letter_offsets": {
            "أ": {"x": 0, "y": -40},
            "ب": {"x": 0, "y": -80},
            "ج": {"x": 0, "y": -75}
        },
        # NEW: Control options text position inside text box
        "text_x_offset": 100,  # Distance from left edge of text box (for left alignment)
        "text_y_offset": -45,  # Vertical offset from center
    }


# Update the original QUESTION_TEXT_BOX_CONFIG to be dynamic
def get_dynamic_question_text_box_config(question_text):
    """Get question text box configuration based on question length"""
    config = get_question_config(question_text)
    question_box = config["question_box"]

    return {
        "width": question_box["width"],
        "height": question_box["height"],
        "x": question_box["x"],
        "y": question_box["y"],
        "corner_radius": 50,
        "border_width": 0,
        "fill_color": (255, 255, 255),
        "shadow_color": (0, 0, 0, 225),
        "shadow_offset": (18, 18),
    }


# Update the original EMOJI_CONFIG to be dynamic
def get_dynamic_emoji_config(question_text):
    """Get emoji configuration based on question length"""
    config = get_question_config(question_text)
    text_boxes = config["text_boxes"]
    emojis = config["emojis"]

    return {
        "size": emojis["size"],
        "x_offset": emojis["x_offset"],
        "y_base": text_boxes["y_base"] + emojis["y_adjustment"],
        "spacing": text_boxes["spacing"],
    }