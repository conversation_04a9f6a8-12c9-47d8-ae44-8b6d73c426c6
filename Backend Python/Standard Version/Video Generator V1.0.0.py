import os
import subprocess
import json
import shutil
import random
import string
import cv2
import csv
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import inflect
import re
from langdetect import detect, DetectorFactory
from multiprocessing import Pool, Semaphore, freeze_support
from kokoro import KPipeline
import soundfile as sf

# Make language detection deterministic
DetectorFactory.seed = 0

# Initialize Kokoro TTS pipeline (American English)
kokoro_pipeline = KPipeline(lang_code='a')


def german_to_phonetic(text):
    """Convert German text to phonetic English approximations."""
    try:
        lang = detect(text)
    except Exception:
        return text

    if lang != 'de':
        return text

    replacements = {
        "die": "dee", "das": "dahs", "und": "oont", "ist": "ist", "ein": "ine",
        "hat": "haht", "ich": "ikh", "Sie": "zee", "wir": "veer",
        "zu": "tsoo", "von": "fon", "nicht": "nikht",
        "was": "vahs", "danke": "dahn-keh", "bitte": "bit-teh",
        "sehen": "zay-en", "schön": "shern", "freund": "froynt",
        "ergebnis": "air-gehp-nis", "viel": "feel", "halbes": "hahl-bes",
        "jahr": "yahr", "woche": "voh-khe", "monat": "moh-naht",
        "stunde": "shtoon-deh", "minute": "mee-noo-teh", "sekunde": "zeh-koon-deh",
        "tage": "tah-geh", "tag": "tahk", "halber": "hahl-ber", "mal": "mahl",
        "geteilt": "geh-tile-t", "plus": "ploos", "minus": "mee-noos", "gleich": "glykh",
        "quadratwurzel": "kvah-draht-voor-tsel", "hälfte": "helf-teh",
        "prozent": "proh-tsent", "ecken": "ek-en", "dreieck": "dry-eck",
        "ergibt": "air-gibt",
        "was ist die": "vahs ist dee", "was ergibt": "vahs air-gibt",
        "was sind": "vahs zint", "wie viele": "vee fee-leh", "wie viel": "vee feel",
        # "0": "null", "1": "ains", "eins": "ains", "2": "tsvai", "zwei": "tsvai",
        # "3": "dry", "drei": "dry", "4": "feer", "vier": "feer",
        # "5": "foonf", "fünf": "foonf", "6": "zeks", "sechs": "zeks",
        # "7": "zee-ben", "sieben": "zee-ben", "8": "ahkt", "acht": "ahkt",
        # "9": "noyn", "neun": "noyn", "10": "tsayn", "zehn": "tsayn",
        # "11": "elf", "12": "tsvelf", "zwölf": "tsverlf",
        "dreizehn": "dry-tsayn", "vierzehn": "feer-tsayn", "fünfzehn": "foonf-tsayn",
        # "20": "tsvahn-tsikh", "22": "tsvahn-tsikh-tsvai", "24": "tsvahn-tsikh-feer",
        # "25": "tsvahn-tsikh-foonf", "26": "tsvahn-tsikh-zeks", "30": "dry-sikh",
        # "33": "dry-sikh-dry", "40": "feer-sikh", "45": "feer-sikh-foonf",
        # "50": "foonf-sikh", "60": "zekh-sikh", "65": "zekh-sikh-foonf",
        # "66": "zekh-sikh-zeks", "70": "zee-ben-sikh", "90": "noyn-sikh",
        # "100": "hoon-dert", "120": "hoon-dert-tsvahn-tsikh",
        # "130": "hoon-dert-dry-sikh", "140": "hoon-dert-feer-sikh",
        # "200": "tsvai-hoon-dert", "360": "dry-hoon-dert-zekh-sikh",
        # "365": "dry-hoon-dert-zekh-sikh-foonf", "366": "dry-hoon-dert-zekh-sikh-zeks",
        # "500": "foonf-hoon-dert", "600": "zeks-hoon-dert", "1000": "tohz-ent",
        # "-5": "mee-noos foonf", "1.5": "ains komma foonf", "2.5": "tsvai komma foonf",
        # "3.5": "dry komma foonf", "4.5": "feer komma foonf", "5.5": "foonf komma foonf",
        # "7.5": "zee-ben komma foonf", "8.2": "ahkt komma tsvai",
        # "10.1": "tsayn komma ains", "12.5": "tsvelf komma foonf",
        # "14.5": "foonf-tsayn komma foonf", "24.5": "tsvahn-tsikh-feer komma foonf",
        # "40.5": "feer-sikh komma foonf", "70.5": "zee-ben-sikh komma foonf",
        # "75.5": "zee-ben-sikh-foonf komma foonf",
        # "√": "square root", "+": "plus", "−": "minus", "×": "times",
        # "÷": "divided by", "%": "percent", ",": "komma", "?": "",
        # "\n": " [pause] ", " , ": " [pause] ",
        # "a\)": "ah)", "b\)": "bay)", "c\)": "tsay)",
        # "correct": "koh-rekt", "option": "op-tsee-ohn"
    }

    for german_word, phonetic in replacements.items():
        pattern = re.compile(re.escape(german_word), re.IGNORECASE)
        text = pattern.sub(phonetic, text)

    return text


def get_random_background_video(folder_path):
    """Get a random background video from the specified folder."""
    video_files = [f for f in os.listdir(
        folder_path) if f.startswith('A (') and f.endswith(').mp4')]
    if not video_files:
        raise ValueError(f"No background videos found in {folder_path}")
    return os.path.join(folder_path, random.choice(video_files))


def delete_background_video(video_path):
    """Delete the background video file if it exists."""
    try:
        if os.path.exists(video_path):
            os.remove(video_path)
            print(f"Successfully deleted background video: {video_path}")
    except Exception as e:
        print(f"Error deleting background video: {e}")


# Constants
FONT_PATH = "./assets/avenir-next-bold.ttf" # Font path
PHOTO_PATH = r"D:\TIKTOK REELS\Clone\quiz_video_maker-1\assets\box.png" # Text box photo path
TIMER_VIDEO = r"D:\XV Material\Assets\Timers\TIMER 1.mp4" # Timer Video Path

PHOTO_WIDTH = 1020
PHOTO_HEIGHT = 510
PHOTO_X = -40
PHOTO_Y = 280
input_video_path = TIMER_VIDEO
VIDEO_WIDTH = 1920
VIDEO_HEIGHT = 1080

p = inflect.engine()

# Position configuration
POSITION_CONFIG = {
    "question": {"x": -420, "y": 320, "line_spacing": 110},
    "question_part2": {"x": -420, "y": 360},
    "options": {"x": 160, "y_base": 850, "option_spacing": 160},
}

number_of_videos = 1
# Account configurations

ACCOUNTS = [

    {
        "name": "Account1",
        "csv_fileQ1": r"D:\XV Material\Assets\C_Math ( 4 Files )\Q1.csv", # First CSV file path
        "csv_fileQ2": r"D:\XV Material\Assets\C_Math ( 4 Files )\Q2.csv", # Second CSV file path
        "csv_fileQ3_Q5": r"D:\XV Material\Assets\C_Math ( 4 Files )\Q3-Q5.csv", # Third CSV file path
        "csv_fileQ6": r"D:\XV Material\Assets\C_Math ( 4 Files )\Q6.csv", # Forth CSV file path
        "text_box": r"D:\XV Material\text box\Text Box 2\1.png", # Text box path
        "background_folder": r"D:\XV Material\XV Videos\Testing_folder", # Folder containing Background Videos
        "output_folder": r"D:\XV Material\Output\Testing_folder", # Path of the output video
        "photo_x": 70,  # X position for the text box
        "photo_y": 255,  # Y position for the text box
        "photo_width": 930,  # Width of the text box
        "photo_height": 440,  # Height of the text box
        "font_type": "./Assets/DE Font.ttf" # Font path
    },

    {
        "name": "Account2",
        "csv_fileQ1": r"D:\XV Material\Assets\C_Math ( 4 Files )\Q1.csv",  # First CSV file path
        "csv_fileQ2": r"D:\XV Material\Assets\C_Math ( 4 Files )\Q2.csv",  # Second CSV file path
        "csv_fileQ3_Q5": r"D:\XV Material\Assets\C_Math ( 4 Files )\Q3-Q5.csv",  # Third CSV file path
        "csv_fileQ6": r"D:\XV Material\Assets\C_Math ( 4 Files )\Q6.csv",  # Forth CSV file path
        "text_box": r"D:\XV Material\text box\Text Box 2\1.png",  # Text box path
        "background_folder": r"D:\XV Material\XV Videos\Testing_folder",  # Folder containing Background Videos
        "output_folder": r"D:\XV Material\Output\Testing_folder",  # Path of the output video
        "photo_x": 70,  # X position for the text box
        "photo_y": 255,  # Y position for the text box
        "photo_width": 930,  # Width of the text box
        "photo_height": 440,  # Height of the text box
        "font_type": "./Assets/DE Font.ttf"  # Font path
    },



]


def insert_outro(main_video_path, outro_path, output_path, insert_time):
    """
    Insert outro video at a specific time in the main video
    - insert_time: seconds where to insert the outro (e.g., 30 for 30 seconds in)
    """
    try:
        # Get main video resolution
        probe_cmd = [
            'ffprobe', '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height',
            '-of', 'csv=s=x:p=0',
            main_video_path
        ]
        main_res = subprocess.check_output(probe_cmd).decode().strip()
        main_width, main_height = map(int, main_res.split('x'))

        # Get duration of outro
        probe_command = [
            'ffprobe', '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            outro_path
        ]
        outro_duration = float(subprocess.check_output(probe_command).strip())

        # Build FFmpeg command with scaling for outro video
        filter_complex = (
            f"[0:v]split=2[v1][v2];"
            f"[0:a]asplit=2[a1][a2];"
            # Start time --> insert time Video
            f"[v1]trim=0:{insert_time},setpts=PTS-STARTPTS[part1v];"
            # Start time --> insert time Auido
            f"[a1]atrim=0:{insert_time},asetpts=PTS-STARTPTS[part1a];"
            # Insert time --> end time Video
            f"[v2]trim=start={insert_time},setpts=PTS-STARTPTS[part2v];"
            # Insert time --> end time Audio
            f"[a2]atrim=start={insert_time},asetpts=PTS-STARTPTS[part2a];"
            f"[1:v]scale={main_width}:{main_height}[outro_v];"
            f"[part1v][part1a][outro_v][1:a][part2v][part2a]concat=n=3:v=1:a=1[outv][outa]"
        )

        command = [
            'ffmpeg',
            '-hide_banner', '-loglevel', 'error',
            '-y',
            '-i', main_video_path,
            '-i', outro_path,
            '-filter_complex', filter_complex,
            '-map', '[outv]',
            '-map', '[outa]',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-crf', '28',
            '-c:a', 'aac',
            "-threads", "8",
            '-b:a', '320k',
            output_path
        ]

        subprocess.run(command, check=True)
        return True

    except Exception as e:
        print(f"Failed to insert outro: {str(e)}")
        return False


def generate_random_filename(prefix="final_quiz_video", extension=".mp4"):
    """Generate a random filename with a given prefix and extension."""
    random_string = "".join(random.choices(
        string.ascii_letters + string.digits, k=8))
    return f"{prefix}_{random_string}{extension}"


# Semaphore to limit TTS requests
tts_semaphore = Semaphore(1)


def text_to_speech_file(args):
    """Convert text to speech using Kokoro TTS and save as an audio file."""
    text, file_name = args

    # Apply German-to-phonetic conversion if needed
    processed_text = german_to_phonetic(text)

    # Convert numbers and math operations
    processed_text = replace_math_operations_multilingual(processed_text)
    print(f"Processed Text: {processed_text}")

    with tts_semaphore:
        try:
            os.makedirs("generated_audio", exist_ok=True)
            save_file_path = f"generated_audio/{file_name}.wav"

            # Generate speech with Kokoro
            generator = kokoro_pipeline(
                processed_text,
                voice='am_adam',
                speed=1,
                split_pattern=r'\n+'
            )

            # Save the audio with increased volume
            for idx, (gs, ps, audio) in enumerate(generator):
                # Increase volume by multiplying the waveform
                # Adjust gain as needed (e.g., 3.0 = 3x louder)
                audio = audio * 3.0
                # Clip to avoid overflow
                audio = np.clip(audio, -1.0, 1.0)
                sf.write(save_file_path, audio, 24000)

            return save_file_path
        except Exception as e:
            print(f"Error generating audio for {file_name}: {e}")
            return None


def generate_audio_files(questions, correct_answers, hook_texts=None):
    """Generate all audio files for questions and answers only."""
    audio_files = {}

    # Use each question's own text for the voice-over
    for i, (q, a) in enumerate(zip(questions, correct_answers)):
        question_audio = text_to_speech_file((q, f"question_{i + 1}"))
        answer_audio = text_to_speech_file((a, f"answer_{i + 1}"))

        if question_audio:
            audio_files[f"question_{i + 1}"] = question_audio
        if answer_audio:
            audio_files[f"answer_{i + 1}"] = answer_audio

    return audio_files


def get_output_video_path(input_video_path, output_video_name='background_video_stable'):
    """Returns the path where the video will be saved (before creating it)"""
    # Ensure .mp4 extension
    if not output_video_name.endswith('.mp4'):
        output_video_name += '.mp4'
    return os.path.abspath(output_video_name)


def create_stable_background_video(input_video_path, output_video_name='background_video_stable'):
    """Actually generates the video at the pre-determined path"""
    output_path = get_output_video_path(input_video_path, output_video_name)

    cap = cv2.VideoCapture(input_video_path)
    if not cap.isOpened():
        print("Error: Could not open video file")
        return None

    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    # Go to 3-second mark
    cap.set(cv2.CAP_PROP_POS_FRAMES, int(6 * fps))
    ret, frame = cap.read()
    cap.release()

    if not ret:
        print("Error: Couldn't read frame at 6 seconds")
        return None

    # Generate 11-second stable video
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    for _ in range(int(11 * fps)):
        out.write(frame)
    out.release()

    print(f"Stable background video created: {output_path}")
    return output_path


# Usage:
# Get path BEFORE creation
# Now generate the video

def remove_green_screen_and_overlay(input_video_path, background_video_path, output_video_path):
    # FFmpeg command to remove green screen and overlay on the background
    command = [
        "ffmpeg",
        "-hide_banner", "-loglevel", "error",
        "-i", input_video_path,
        "-i", background_video_path,
        "-filter_complex",
        # Explicitly set TIMER_VIDEO audio volume to 1.0 to preserve its level
        "[0:v]chromakey=0x00FF00:0.1:0.2[fg];"
        "[1:v][fg]overlay=0:0[outv]",
        "-map", "[outv]",
        "-map", "0:a",  # Use TIMER_VIDEO audio as-is, no volume filter
        "-c:v", "libx264",
        "-preset", "ultrafast",
        "-crf", "28",
        "-threads", "8",
        "-c:a", "aac",
        "-y",
        output_video_path,
    ]
    subprocess.run(command, check=True)
    return output_video_path


def run_ffmpeg_command(command):
    """Run FFmpeg command using subprocess."""
    print("Running FFmpeg command:")
    print(" ".join(command))
    # Ensure -hide_banner, -loglevel error, and -threads 8 are present
    if "-hide_banner" not in command:
        command.insert(1, "-hide_banner")
    if "-loglevel" not in command:
        command.insert(2, "-loglevel")
        command.insert(3, "error")
    if "-threads" in command:
        idx = command.index("-threads")
        command[idx + 1] = "8"
    else:
        command.insert(-1, "-threads")
        command.insert(-1, "8")
    subprocess.run(command, check=True)
    # Remove or comment out this line to avoid NameError:
    # print(f"Final video saved: {FINAL_OUTPUT}")


def center_align_text(text, font, image_width):
    """Center-align text both horizontally per line and vertically as a block"""
    lines = text.split("\n")
    centered_lines = []

    for line in lines:
        text_width = font.getlength(line)
        x = (image_width - text_width) // 2
        centered_lines.append((x, line))

    return centered_lines


text_properties = {
    "question": {"fontsize": 90, "borderw": 4},
    "option": {"fontsize": 110, "borderw": 6},
    "answer": {"fontsize": 110, "borderw": 6},
}
text_properties1 = {
    "question": {"fontsize": 110, "borderw": 4},
    "option": {"fontsize": 110, "borderw": 6},
    "answer": {"fontsize": 110, "borderw": 6},
}


def create_final_video(output_video_path, questions, correct_answers, account_name, text_box_path,
                       photo_x, photo_y, photo_width, photo_height, output_folder, font_type):
    # Constants for timing
    QUESTION_DURATION = 11  # Each question gets 11 seconds
    ANSWER_DISPLAY_TIME = 3.0  # Last 3 seconds show answer
    LAST_ANSWER_TIME = 8.5

    NUMBER_OF_QUESTIONS = 6
    TOTAL_DURATION = NUMBER_OF_QUESTIONS * QUESTION_DURATION  # 66 seconds

    # Generate all audio files (no HOOK)
    audio_files = generate_audio_files(questions, correct_answers)

    # Use the account-specific text box and positioning
    PHOTO_PATH = text_box_path
    PHOTO_X = photo_x
    PHOTO_Y = photo_y
    PHOTO_WIDTH = photo_width
    PHOTO_HEIGHT = photo_height
    FONT_PATH = font_type

    if not os.path.exists(FONT_PATH):
        abs_path = os.path.abspath(FONT_PATH)
        raise FileNotFoundError(
            f"Font file not found: {FONT_PATH}\n"
            f"Absolute path: {abs_path}\n"
            f"Current working directory: {os.getcwd()}"
        )

    try:
        font = ImageFont.truetype(
            FONT_PATH,
            text_properties["question"]["fontsize"]
        )
    except IOError as e:
        print(f"Failed to load font: {FONT_PATH}")
        print(f"Possible causes:")
        print(f"1. File exists but is corrupt")
        print(
            f"2. Font size {text_properties['question']['fontsize']} is invalid")
        print(f"3. File permissions issue")
        print(f"Absolute path: {os.path.abspath(FONT_PATH)}")
        raise

    # Generate a unique filename for the final video
    FINAL_OUTPUT = generate_random_filename()
    FINAL_OUTPUT_PATH = os.path.join(output_folder, FINAL_OUTPUT)

    # Build final command
    command = [
        "ffmpeg", "-y",
        "-stream_loop", "-1", "-i", merged_video_path,  # Loop video if needed
    ]

    # Add question/answer audio inputs
    for i in range(1, NUMBER_OF_QUESTIONS+1):
        command.extend(["-i", audio_files[f"question_{i}"]])
        command.extend(["-i", audio_files[f"answer_{i}"]])

    # Add photo input
    command.extend(["-i", PHOTO_PATH])

    # Build filter complex with only merged_video_path as video
    filter_parts = [
        # Trim the (looped) merged_video_path to exactly TOTAL_DURATION seconds
        f"[0:v]trim=duration={TOTAL_DURATION},setpts=PTS-STARTPTS[quiz_vid];",
        # --- DO NOT use any volume filter on [0:a] or [merged_audio] ---
        f"[0:a]atrim=duration={TOTAL_DURATION},asetpts=PTS-STARTPTS[merged_audio];"
    ]

    audio_inputs = []
    question_delays = []
    current_time = 0
    for i in range(NUMBER_OF_QUESTIONS):
        question_delays.append(current_time)
        question_delays.append(
            current_time + QUESTION_DURATION - ANSWER_DISPLAY_TIME)
        current_time += QUESTION_DURATION

    for i in range(1, NUMBER_OF_QUESTIONS+1):
        q_idx = 1 + (i - 1) * 2
        a_idx = q_idx + 1

        filter_parts.append(
            f"[{q_idx}:a]adelay={question_delays[(i - 1) * 2] * 1000}|{question_delays[(i - 1) * 2] * 1000},volume=1.0[question_{i}_a];")

        # Always pronounce/display the correct answer, even for the last question
        if i == NUMBER_OF_QUESTIONS:
            last_answer_delay = question_delays[(i - 1) * 2] + LAST_ANSWER_TIME
            filter_parts.append(
                f"[{a_idx}:a]adelay={int(last_answer_delay * 1000)}|{int(last_answer_delay * 1000)},volume=1.0[answer_{i}_a];")
        else:
            filter_parts.append(
                f"[{a_idx}:a]adelay={question_delays[(i - 1) * 2 + 1] * 1000}|{question_delays[(i - 1) * 2 + 1] * 1000},volume=1.0[answer_{i}_a];")

        audio_inputs.append(f"[question_{i}_a]")
        audio_inputs.append(f"[answer_{i}_a]")

    # Add photo overlay
    photo_idx = 13  # merged_video_path + 12 audio inputs
    filter_parts.append(
        f"[{photo_idx}:v]scale={PHOTO_WIDTH}:{PHOTO_HEIGHT}[scaled_photo];"
        f"[quiz_vid][scaled_photo]overlay={PHOTO_X}:{PHOTO_Y}:enable='between(t,0,{TOTAL_DURATION})'[v0];"
    )

    current_stream = "v0"

    # No HOOK text displays

    # Add question/answer text displays with dynamic timing
    current_time = 0
    for q_num in range(1, NUMBER_OF_QUESTIONS+1):
        # Question text
        question = questions[q_num - 1]
        lines = question.split('\n')
        num_lines = len(lines)
        # Determine y position based on number of \n
        if question.count('\n') <= 1:
            base_y = 360
        elif question.count('\n') == 0:
            base_y = 310
        else:
            base_y = 320

        for line_num, line in enumerate(lines[:3]):
            next_stream = f"{current_stream}q{q_num}{line_num}"
            x_pos = POSITION_CONFIG['question']['x'] + \
                (VIDEO_WIDTH - font.getlength(line)) // 2
            y_pos = base_y + line_num * \
                POSITION_CONFIG['question']['line_spacing']

            filter_parts.append(
                f"[{current_stream}]drawtext=fontfile={FONT_PATH}:"
                f"text='{ffmpeg_safe_text(line)}':"
                f"fontsize={text_properties['question']['fontsize']}:"
                f"fontcolor=white:borderw={text_properties['question']['borderw']}:bordercolor=black:"
                f"x={x_pos}:y={y_pos}:"
                f"enable='between(t,{current_time},{current_time + QUESTION_DURATION})'"
                f"[{next_stream}];"
            )
            current_stream = next_stream

        # Answer options
        answer_options = answers[q_num - 1]
        for opt_num in range(3):
            next_stream = f"{current_stream}a{q_num}{opt_num}"
            y_opt = POSITION_CONFIG['options']['y_base'] + \
                opt_num * POSITION_CONFIG['options']['option_spacing']

            filter_parts.append(
                f"[{current_stream}]drawtext=fontfile={FONT_PATH}:"
                f"text='{ffmpeg_safe_text(answer_options[opt_num])}':"
                f"fontsize={text_properties['option']['fontsize']}:"
                f"fontcolor=white:borderw={text_properties['option']['borderw']}:bordercolor=black:"
                f"x={POSITION_CONFIG['options']['x']}:y={y_opt}:"
                f"enable='between(t,{current_time},{current_time + QUESTION_DURATION})'"
                f"[{next_stream}];"
            )
            current_stream = next_stream

        # Correct answer highlight (now also for last question)
        if q_num <= len(correct_answers):
            correct_ans = correct_answers[q_num - 1]
            next_stream = f"{current_stream}correct{q_num}"
            letter = correct_ans.split()[1][0]
            opt_num = ord(letter) - ord('A')
            y_correct = POSITION_CONFIG['options']['y_base'] + \
                opt_num * POSITION_CONFIG['options']['option_spacing']

            filter_parts.append(
                f"[{current_stream}]drawtext=fontfile={FONT_PATH}:"
                f"text='{ffmpeg_safe_text(correct_ans.replace('Answer ', ''))}':"
                f"fontsize={text_properties['answer']['fontsize']}:"
                f"fontcolor=lime:borderw={text_properties['answer']['borderw']}:bordercolor=black:"
                f"x={POSITION_CONFIG['options']['x']}:y={y_correct}:"
                f"enable='between(t,{current_time + QUESTION_DURATION - ANSWER_DISPLAY_TIME},{current_time + QUESTION_DURATION})'"
                f"[{next_stream}];"
            )
            current_stream = next_stream

        current_time += QUESTION_DURATION

    # Final audio mix and output
    filter_parts.append(
        f"[merged_audio]{''.join(audio_inputs)}amix=inputs={1 + len(audio_inputs)}:normalize=0:dropout_transition=0[a];")
    filter_parts.append(f"[{current_stream}]null[outv];")

    command.extend([
        "-filter_complex", "".join(filter_parts),
        "-map", "[outv]",
        "-map", "[a]",
        "-c:v", "libx264",
        "-preset", "ultrafast",
        "-crf", "28",
        "-threads", "4",
        "-c:a", "aac",
        "-b:a", "320k",
        "-t", str(TOTAL_DURATION),
        "-strict", "experimental",
        FINAL_OUTPUT_PATH
    ])

    run_ffmpeg_command(command)
    return FINAL_OUTPUT_PATH  # <-- Return the actual output path


def remove_background_video(file_path):
    """Removes the background video file if it exists."""
    try:
        os.remove(file_path)
        # print(f"Successfully removed {file_path}")
    except FileNotFoundError:
        print(f"{file_path} not found, nothing to remove.")
    except Exception as e:
        print(f"Error removing {file_path}: {e}")


def has_background_videos(folder_path):
    """Check if the folder contains any background videos."""
    try:
        video_files = [f for f in os.listdir(
            folder_path) if f.startswith('A (') and f.endswith(').mp4')]
        return len(video_files) > 0
    except Exception as e:
        print(f"ERROR CHECKING FOLDER: {str(e)}")
        return False


def ffmpeg_safe_text(text):
    """Sanitize text for FFmpeg drawtext filter."""
    # Remove newlines and carriage returns
    text = text.replace('\n', ' ').replace('\r', ' ')
    # Remove extra spaces
    text = ' '.join(text.split())
    # Escape single quotes (FFmpeg expects a single backslash)
    text = text.replace("'", r"\'")
    # Remove or escape double backslashes (avoid over-escaping)
    text = text.replace("\\", "/")
    return text


def get_video_duration(video_path):
    """Return duration of video in seconds."""
    try:
        result = subprocess.run(
            [
                'ffprobe', '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                video_path
            ],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )
        return float(result.stdout.strip())
    except Exception as e:
        print(f"Could not get duration for {video_path}: {e}")
        return None


def insert_video_at_time(main_video_path, insert_path, output_path, insert_time):
    """
    Insert a video at a specific time in the main video.
    """
    try:
        # Get main video resolution and SAR
        probe_cmd = [
            'ffprobe', '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height,sample_aspect_ratio',
            '-of', 'csv=p=0',
            main_video_path
        ]
        main_res = subprocess.check_output(probe_cmd).decode().strip()
        # main_res: width,height,sar (e.g., 1088,1920,135:136)
        parts = main_res.split(',')
        main_width, main_height = map(int, parts[:2])
        if len(parts) > 2 and parts[2] and parts[2] != 'N/A':
            main_sar = parts[2]
        else:
            main_sar = "1:1"

        # Build FFmpeg command with scaling and SAR for insert video
        filter_complex = (
            f"[0:v]split=2[v1][v2];"
            f"[0:a]asplit=2[a1][a2];"
            f"[v1]trim=0:{insert_time},setpts=PTS-STARTPTS[part1v];"
            f"[a1]atrim=0:{insert_time},asetpts=PTS-STARTPTS[part1a];"
            f"[v2]trim=start={insert_time},setpts=PTS-STARTPTS[part2v];"
            f"[a2]atrim=start={insert_time},asetpts=PTS-STARTPTS[part2a];"
            f"[1:v]scale={main_width}:{main_height},setsar={main_sar}[insert_v];"
            f"[part1v][part1a][insert_v][1:a][part2v][part2a]concat=n=3:v=1:a=1[outv][outa]"
        )

        command = [
            'ffmpeg',
            '-hide_banner', '-loglevel', 'error',
            '-y',
            '-i', main_video_path,
            '-i', insert_path,
            '-filter_complex', filter_complex,
            '-map', '[outv]',
            '-map', '[outa]',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-crf', '28',
            '-c:a', 'aac',
            "-threads", "8",
            '-b:a', '320k',
            output_path
        ]

        subprocess.run(command, check=True)
        return True

    except Exception as e:
        print(f"Failed to insert video: {str(e)}")
        return False


if __name__ == "__main__":
    freeze_support()

    # Track per-phone-folder account index in memory (reset every run)
    phone_folder_account_indices = {}

    for account_index, account in enumerate(ACCOUNTS, 1):
        account_name = account["name"]
        # --- Use new CSV logic with preferred keys ---
        csv_q1 = account.get("csv_fileQ1") or account.get(
            "csv_q1") or account.get("csv_file")
        csv_q2 = account.get("csv_fileQ2") or account.get(
            "csv_q2") or account.get("csv_file")
        csv_q3_5 = account.get("csv_fileQ3_Q5") or account.get(
            "csv_q3_5") or account.get("csv_file")
        csv_q6 = account.get("csv_fileQ6") or account.get(
            "csv_q6") or account.get("csv_file")
        text_box_path = account["text_box"]
        background_folder = account["background_folder"]
        output_folder = account["output_folder"]
        photo_x = account["photo_x"]
        photo_y = account["photo_y"]
        photo_width = account["photo_width"]
        photo_height = account["photo_height"]
        font_type = account["font_type"]

        if not has_background_videos(background_folder):
            print(
                f"ACCOUNT NUMBER {account_index} DOES NOT HAVE BACKGROUND VIDEOS IN ITS FOLDER")
            continue

        print(f"Processing account: {account_name}")
        os.makedirs(output_folder, exist_ok=True)

        # --- Determine Phone folder and assign a unique index per Phone folder ---
        phone_folder = output_folder
        hook_videos_folder = os.path.join(phone_folder, "HOOK Videos")
        os.makedirs(hook_videos_folder, exist_ok=True)

        # Use an in-memory counter for this Phone folder (reset every run)
        if phone_folder not in phone_folder_account_indices:
            phone_folder_account_indices[phone_folder] = 0
        phone_folder_account_indices[phone_folder] += 1
        phone_account_index = phone_folder_account_indices[phone_folder]


        HOOK_VIDEO_PATH = os.path.join(
            hook_videos_folder, f"Hook.mp4"
        )

        for i in range(number_of_videos):
            print(f"Creating video {i + 1} of 5 for {account_name}...")

            # Remove HOOK loading
            # hook_texts = load_hooks_from_csv('hooks.csv')
            # Optionally print for debugging
            # for idx, part in enumerate(hook_texts, 1):
            #     print(f"Processed Text part {idx}: {repr(part)}")

            output_video_path = f"merged_video_{i}.mp4"
            final_output_path = os.path.join(
                output_folder, f"final_video_{i}.mp4")
            BACKGROUND_VIDEO = get_random_background_video(background_folder)
            print(f"Using background video: {BACKGROUND_VIDEO}")
            background_video_path = BACKGROUND_VIDEO
            background_video_stable = create_stable_background_video(
                BACKGROUND_VIDEO)

            questions = []
            answers = []
            correct_answers = []

            # --- New logic: load 1 from Q1, 1 from Q2, 3 from Q3-5, 1 from Q6 ---
            def load_random_row(csv_path):
                with open(csv_path, mode='r', encoding='utf-8') as file:
                    csv_reader = csv.reader(file)
                    rows = [row for row in csv_reader if row and any(
                        cell.strip() for cell in row)]
                    # Skip header if present
                    if rows and rows[0][0].lower().startswith("question"):
                        rows = rows[1:]
                    if not rows:
                        raise ValueError(f"No data rows in {csv_path}")
                    return random.choice(rows)

            def load_random_rows(csv_path, n):
                with open(csv_path, mode='r', encoding='utf-8') as file:
                    csv_reader = csv.reader(file)
                    rows = [row for row in csv_reader if row and any(
                        cell.strip() for cell in row)]
                    # Skip header if present
                    if rows and rows[0][0].lower().startswith("question"):
                        rows = rows[1:]
                    if len(rows) < n:
                        raise ValueError(
                            f"Not enough rows in {csv_path} for {n} picks")
                    return random.sample(rows, n)

            try:
                q1_row = load_random_row(csv_q1)
                q2_row = load_random_row(csv_q2)
                q3_5_rows = load_random_rows(csv_q3_5, 3)
                q6_row = load_random_row(csv_q6)
                selected_rows = [q1_row, q2_row] + q3_5_rows + [q6_row]
            except Exception as e:
                print(f"Error loading questions: {e}")
                continue

            for row in selected_rows:
                question = row[0].replace("\\n", "\n")
                option_a = row[1] if row[1].startswith(
                    "A)") else f"A) {row[1]}"
                option_b = row[2] if row[2].startswith(
                    "B)") else f"B) {row[2]}"
                option_c = row[3] if row[3].startswith(
                    "C)") else f"C) {row[3]}"
                correct_answer = f"Answer {row[4]}"
                questions.append(question)
                answers.append([option_a, option_b, option_c])
                correct_answers.append(correct_answer)

            # --- Debug: Print questions, answers, and correct_answers ---
            print(f"\nNumber of questions: {len(questions)}")
            print("questions = [")
            for q in questions:
                print(f'    "{q}",')
            print("]")

            print("\nanswers = [")
            for a in answers:
                print(f'    {a},')
            print("]")

            print("\ncorrect_answers = [")
            for ca in correct_answers:
                print(f'    "{ca}",')
            print("]")

            # After reading the CSV file and populating the lists
            if questions:
                first_question = questions[0]  # Get the first question
                try:
                    detected_language = detect(
                        first_question)  # Detect the language
                    print(f"Detected language: {detected_language}")
                except:
                    detected_language = "en"  # Default to English if detection fails
                    print("Language detection failed. Defaulting to English.")
            else:
                detected_language = "en"  # Default to English if no questions are available
                print("No questions found. Defaulting to English.")

            # Use the detected language in the replace_math_operations_multilingual function

            def replace_math_operations_multilingual(text, language=None):
                """
                Replaces mathematical operations with their word equivalents in the specified language.
                If no language is provided, it defaults to the detected language.
                """
                if language is None:
                    language = detected_language  # Use the detected language

                math_to_words = {
                    "en": {  # English
                        '+': ' plus ',
                        ' + ': ' plus ',
                        '-': ' minus ',
                        ' - ': ' minus ',
                        '×': ' times ',
                        ' × ': ' times ',
                        '*': ' times ',
                        '/': ' out of ',
                        "let us go": "Let's go",
                        "you are": "you're",
                        "You are": "you're",
                        '÷': ' divided by ',
                        ' ÷ ': ' divided by ',
                        '^': ' to the power of ',
                        'Answer': ' Answer ',
                    },
                    "de": {  # German
                        '+': ' plus ',
                        ' + ': ' plus ',
                        '-': ' minus ',
                        ' - ': ' minus ',
                        '×': ' mal ',
                        ' × ': ' mal ',
                        '*': ' mal ',
                        '/': ' geteilt durch ',
                        '÷': ' geteilt durch ',
                        ' ÷ ': ' geteilt durch ',
                        '=': ' gleich ',
                        '^': ' hoch ',
                        'Answer': ' Antwort ',
                        '√': 'die Quadratwurzel von',
                        '√ ': 'die Quadratwurzel von',

                    },

                    # Add more languages as needed
                }

                # Select the appropriate dictionary based on the detected language
                if language in math_to_words:
                    math_to_words_dict = math_to_words[language]
                else:
                    # Default to English if the language is not supported
                    math_to_words_dict = math_to_words["en"]

                # Replace each math operation with its word equivalent
                for operation, word in math_to_words_dict.items():
                    text = text.replace(f" {operation} ", f" {word} ")
                    text = text.replace(f"{operation} ", f"{word} ")
                    text = text.replace(f" {operation}", f" {word}")
                    text = text.replace(operation, word)

                # Clean up extra spaces
                text = ' '.join(text.split())

                return text

            # Merge the background video and timer video for this iteration
            merged_video_path = generate_random_filename(
                prefix="merged_video", extension=".mp4")
            print(f"Merging background and timer video: {merged_video_path}")
            merged_video_path = remove_green_screen_and_overlay(
                input_video_path=TIMER_VIDEO,
                background_video_path=background_video_path,
                output_video_path=merged_video_path
            )

            question_lines = questions[0].split('\n')
            num_lines = len(question_lines)
            # Use y=320 if 0 or 1 \n, else y=360
            if questions[0].count('\n') <= 1:
                y_position = POSITION_CONFIG['question_part2']['y']
            else:
                y_position = POSITION_CONFIG['question']['y']

            # REPEAT THE PROSSES FOR ALL QUESTIONS

            question_lines1 = questions[1].split('\n')
            num_lines1 = len(question_lines1)
            if questions[1].count('\n') <= 1:
                y_position1 = POSITION_CONFIG['question_part2']['y']
            else:
                y_position1 = POSITION_CONFIG['question']['y']

            question_lines2 = questions[2].split('\n')
            num_lines2 = len(question_lines2)
            if questions[2].count('\n') <= 1:
                y_position2 = POSITION_CONFIG['question_part2']['y']
            else:
                y_position2 = POSITION_CONFIG['question']['y']

            question_lines3 = questions[3].split('\n')
            num_lines3 = len(question_lines3)
            if questions[3].count('\n') <= 1:
                y_position3 = POSITION_CONFIG['question_part2']['y']
            else:
                y_position3 = POSITION_CONFIG['question']['y']

            question_lines4 = questions[4].split('\n')
            num_lines4 = len(question_lines4)
            if questions[4].count('\n') <= 1:
                y_position4 = POSITION_CONFIG['question_part2']['y']
            else:
                y_position4 = POSITION_CONFIG['question']['y']

            question_lines5 = questions[5].split('\n')
            num_lines5 = len(question_lines5)
            if questions[5].count('\n') <= 1:
                y_position5 = POSITION_CONFIG['question_part2']['y']
            else:
                y_position5 = POSITION_CONFIG['question']['y']

            # Generate a unique filename for each iteration
            FINAL_OUTPUT = generate_random_filename()

            # Create the final video for this iteration (no HOOK_texts argument)
            actual_final_output_path = create_final_video(
                output_video_path,
                questions,
                correct_answers,
                account_name,
                text_box_path,
                photo_x,
                photo_y,
                photo_width,
                photo_height,
                output_folder,
                font_type,
            )

            try:
                # If we reach here, it means the video was generated successfully
                print(f"Video {i + 1} generated successfully")

                # Clean up the generated audio files
                shutil.rmtree("generated_audio", ignore_errors=True)

                # Clean up temporary video files
                if os.path.exists(output_video_path):
                    os.remove(output_video_path)

                # Delete the background video after successful generation
                delete_background_video(BACKGROUND_VIDEO)

                # Clean up the merged video
                if os.path.exists(merged_video_path):
                    os.remove(merged_video_path)

            except Exception as e:
                print(f"Error processing video {i + 1}: {str(e)}")
                # Don't delete the background video if there was an error
                continue

            print(f"Completed video {i + 1} for {account_name}")

            # Clean up the generated audio files after each iteration
            shutil.rmtree("generated_audio", ignore_errors=True)

            # Clean up temporary video files
            if os.path.exists(output_video_path):
                os.remove(output_video_path)

            # Clean up the background video file after all iterations are done
            input_video_path = "BACKGROUND_VIDEO.mp4"  # Path to the background video
            remove_background_video(input_video_path)
            remove_background_video(merged_video_path)

            merged_output_path = os.path.join(
                output_folder, f"with_outro_{os.path.basename(actual_final_output_path)}"
            )

            # Verify the main video exists before merging
            if os.path.exists(actual_final_output_path):
                # Check if all HOOK videos exist before attempting to insert
                hook_missing = False
                for hook_path, label in [
                    (HOOK_VIDEO_PATH, "Hook"),
                ]:
                    if not os.path.exists(hook_path):
                        print(
                            f"WARNING: {label}.mp4 not found at {hook_path} Skipping HOOK inserts. Saving original video only.")
                        hook_missing = True
                        break

                if hook_missing:
                    # Do NOT save the with_outro_... version if any HOOK is missing
                    print("No HOOK videos found, skipping with_outro_... save.")
                else:
                    # Always use the same HOOK flow/order for all accounts:
                    # 11s = Rader, 33s = Like, 55s = Save
                    # Merge in reverse order to avoid shifting insert points
                    insert_points = [
                        (0, HOOK_VIDEO_PATH),
                    ]
                    temp_path = actual_final_output_path
                    for idx, (insert_time, insert_path) in enumerate(insert_points):
                        next_temp_path = temp_path.replace(
                            ".mp4", f"_insert{idx}.mp4")
                        print(
                            f"Inserting {insert_path} at {insert_time:.2f} seconds into {temp_path}...")
                        if insert_video_at_time(temp_path, insert_path, next_temp_path, insert_time):
                            if temp_path != actual_final_output_path:
                                os.remove(temp_path)
                            temp_path = next_temp_path
                            print(
                                f"Inserted {insert_path} at {insert_time:.2f}s.")
                        else:
                            print(
                                f"Insertion failed for {insert_path} at {insert_time:.2f}s - keeping previous video")
                    # Move final temp_path to merged_output_path if needed
                    if temp_path != merged_output_path:
                        shutil.move(temp_path, merged_output_path)
                    # Remove the original final video if the merged video was created
                    if os.path.exists(merged_output_path) and os.path.exists(actual_final_output_path):
                        try:
                            os.remove(actual_final_output_path)
                        except Exception as e:
                            print(
                                f"Warning: Could not remove original video: {e}")
                    print(
                        f"All inserts complete. Final video: {merged_output_path}")
            else:
                print(
                    f"Error: Generated video not found at {actual_final_output_path}")
