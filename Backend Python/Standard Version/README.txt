# A code that creates a 6 questions quiz videos along with zooming background and 3 call to actions,
# Like CTA in the 33rd second of the original video, and a Rader CTA in the 11th second of the original video,
# And a Save CTA in the 55th second of the original video.
# And this code accepts 1 CSV file, Or 4 CSV files, if one CSV file is provided it will randomly choose
# 6 questions from this file, if 4 CSV files is provided it will randomly choose 1 question from the Q1 CSV file,
# 1 random question from Q2 csv file, 3 random questions ( without replications ) from Q3-Q5 Csv file, and 1 random question from Q6 CSV file
# IT USES KOKORO FOR VOICEOVER
# Dynamically displays the questions text on the video and Dynamically generate the voiceover
# ( depends on the number of questions you ask it to make )
# Contains the missmatch audio trick, use the voiceover for question 4 for both question 3 and question 4,
# ( only the voice of the questions, the answers are correct )
# No Hook