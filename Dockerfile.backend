# Multi-stage Docker build for Python backend
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libmagic1 \
    libpq-dev \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs uploads output assets alembic/versions && \
    chown -R appuser:appuser /app

# Make migration script executable
RUN chmod +x migrate.py

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Production stage
FROM base as production

# Set production environment
ENV PYTHON_ENV=production

# Run with gunicorn
CMD ["gunicorn", "api_server_production:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8001", "--access-logfile", "-", "--error-logfile", "-"]

# Development stage
FROM base as development

# Set development environment
ENV PYTHON_ENV=development

# Install development dependencies
RUN pip install --no-cache-dir pytest pytest-asyncio pytest-cov httpx

# Run with uvicorn for development
CMD ["uvicorn", "api_server_production:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]
