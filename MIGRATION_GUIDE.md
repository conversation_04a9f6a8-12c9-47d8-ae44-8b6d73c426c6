# 🔄 Database Migration Guide - Xvion Project

This guide covers database migration management using Alembic for the Xvion Arabic Quiz Video Generator.

## 📋 Overview

The project uses **Alembic** for database schema version control and migrations. This ensures:
- **Version Control**: Track all database schema changes
- **Rollback Capability**: Safely revert problematic changes
- **Team Collaboration**: Consistent database state across environments
- **Production Safety**: Controlled deployment of schema changes

## 🛠️ Migration Management Script

The project includes a comprehensive migration management script: `migrate.py`

### Available Commands

```bash
# Check database connection
python migrate.py check

# Create initial migration (first time setup)
python migrate.py initial

# Create a new migration
python migrate.py create "Add user preferences table"

# Apply migrations (upgrade to latest)
python migrate.py upgrade

# Apply migrations to specific revision
python migrate.py upgrade abc123

# Rollback to previous revision
python migrate.py downgrade -1

# Rollback to specific revision
python migrate.py downgrade abc123

# Show current database revision
python migrate.py current

# Show migration history
python migrate.py history

# Show details of specific revision
python migrate.py show abc123

# Stamp database with revision (without running migration)
python migrate.py stamp head

# Reset database (⚠️ DESTRUCTIVE - drops all tables)
python migrate.py reset
```

## 🚀 Quick Start

### 1. First Time Setup

```bash
# Check database connection
python migrate.py check

# Create initial migration with all current models
python migrate.py initial

# Apply the initial migration
python migrate.py upgrade
```

### 2. Creating New Migrations

When you modify database models:

```bash
# Create migration with auto-generated changes
python migrate.py create "Add video quality field to jobs table"

# Review the generated migration file in alembic/versions/
# Edit if necessary, then apply:
python migrate.py upgrade
```

### 3. Production Deployment

```bash
# Always backup database first!
# Then run migrations:
python migrate.py upgrade
```

## 🐳 Docker Integration

### Automatic Migrations in Docker

The Docker setup includes automatic migration handling:

```bash
# Deploy with automatic migrations
./deploy.sh production
```

The deployment script will:
1. Check if migrations exist
2. Create initial migration if first deployment
3. Run pending migrations automatically

### Manual Migration Commands in Docker

```bash
# Run migrations in Docker
docker-compose run --rm migrate python migrate.py upgrade

# Create new migration in Docker
docker-compose run --rm migrate python migrate.py create "Migration message"

# Check migration status
docker-compose run --rm migrate python migrate.py current
```

## 📁 File Structure

```
xvion/
├── alembic.ini                 # Alembic configuration
├── migrate.py                  # Migration management script
├── alembic/
│   ├── env.py                 # Alembic environment setup
│   ├── script.py.mako         # Migration template
│   └── versions/              # Migration files
│       ├── 001_initial_migration.py
│       ├── 002_add_user_preferences.py
│       └── ...
└── database.py                # SQLAlchemy models
```

## 🔧 Configuration

### Environment Variables

```bash
# Database URL for migrations
DATABASE_URL=postgresql://user:pass@localhost:5432/xvion_db

# Or use individual components
DB_HOST=localhost
DB_PORT=5432
DB_NAME=xvion_db
DB_USER=xvion_user
DB_PASSWORD=your_password
```

### Alembic Configuration (alembic.ini)

Key settings:
- **script_location**: `alembic` (migration files location)
- **file_template**: Includes timestamp in migration filenames
- **sqlalchemy.url**: Database connection URL

## 📝 Migration Best Practices

### 1. Always Review Generated Migrations

```bash
# After creating migration, review the file:
python migrate.py create "Add new column"
# Check: alembic/versions/TIMESTAMP_add_new_column.py
```

### 2. Test Migrations

```bash
# Test upgrade
python migrate.py upgrade

# Test downgrade
python migrate.py downgrade -1

# Re-upgrade
python migrate.py upgrade
```

### 3. Backup Before Production Migrations

```bash
# Backup database
pg_dump xvion_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Run migration
python migrate.py upgrade

# If issues occur, restore:
# psql xvion_db < backup_TIMESTAMP.sql
```

### 4. Handle Data Migrations

For complex data transformations, create custom migrations:

```python
# In migration file
def upgrade():
    # Schema changes
    op.add_column('users', sa.Column('full_name', sa.String(200)))
    
    # Data migration
    connection = op.get_bind()
    connection.execute(
        "UPDATE users SET full_name = first_name || ' ' || last_name"
    )
    
    # Remove old columns
    op.drop_column('users', 'first_name')
    op.drop_column('users', 'last_name')
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Migration Conflicts
```bash
# If multiple developers create migrations simultaneously
python migrate.py history
# Resolve conflicts by merging or recreating migrations
```

#### 2. Database Out of Sync
```bash
# Check current state
python migrate.py current

# If database has manual changes, stamp with current revision
python migrate.py stamp head
```

#### 3. Failed Migration
```bash
# Check what went wrong
python migrate.py current
python migrate.py history

# Fix the issue and retry
python migrate.py upgrade
```

#### 4. Rollback Issues
```bash
# If downgrade fails, check the downgrade() function
python migrate.py show REVISION_ID

# May need to manually fix database state
```

### Recovery Procedures

#### Reset and Recreate (Development Only)
```bash
# ⚠️ DESTRUCTIVE - Only for development
python migrate.py reset
```

#### Production Recovery
```bash
# 1. Stop application
docker-compose stop backend

# 2. Restore from backup
psql xvion_db < backup_file.sql

# 3. Stamp with known good revision
python migrate.py stamp KNOWN_GOOD_REVISION

# 4. Restart application
docker-compose start backend
```

## 📊 Migration Monitoring

### Check Migration Status

```bash
# Current revision
python migrate.py current

# Pending migrations
python migrate.py history --verbose

# Show specific migration
python migrate.py show REVISION_ID
```

### Production Monitoring

```bash
# In production, log migration status
python migrate.py current >> /var/log/xvion/migrations.log

# Monitor for migration failures
tail -f /var/log/xvion/migrations.log
```

## 🔄 CI/CD Integration

### GitHub Actions Integration

The CI/CD pipeline includes migration testing:

```yaml
# In .github/workflows/ci-cd.yml
- name: Test Migrations
  run: |
    python migrate.py upgrade
    python migrate.py downgrade -1
    python migrate.py upgrade
```

### Deployment Pipeline

```bash
# Automated deployment with migrations
./deploy.sh production
# This includes:
# 1. Database backup
# 2. Migration execution
# 3. Health checks
# 4. Rollback on failure
```

## 📚 Additional Resources

- [Alembic Documentation](https://alembic.sqlalchemy.org/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 🆘 Support

For migration issues:
1. Check this guide first
2. Review migration files in `alembic/versions/`
3. Check application logs
4. Use `python migrate.py check` to verify database connection
5. Create GitHub issue with migration details

---

**Remember**: Always backup your database before running migrations in production! 🛡️
