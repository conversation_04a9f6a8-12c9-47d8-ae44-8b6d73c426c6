"""
Database configuration and models for Xvion
"""

import os
from datetime import datetime
from typing import List, Optional
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Float, text, Index
from sqlalchemy.orm import declarative_base, sessionmaker, relationship, Session
import uuid

# Database URL - can be configured via environment variable
# Using PostgreSQL with psycopg3 driver
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql+psycopg://mohamedshady@localhost:5432/xvion_db"
)

# Create engine
engine = create_engine(DATABASE_URL, echo=True)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Database Models

class Quiz(Base):
    __tablename__ = "quizzes"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(255), nullable=False)
    description = Column(Text)
    language = Column(String(10), default="ar")
    created_by = Column(String, ForeignKey("users.id"), nullable=True)  # nullable for existing data
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    questions = relationship("Question", back_populates="quiz", cascade="all, delete-orphan")
    video_jobs = relationship("VideoJob", back_populates="quiz")
    created_by_user = relationship("User", back_populates="quizzes")

class Question(Base):
    __tablename__ = "questions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    quiz_id = Column(String, ForeignKey("quizzes.id"), nullable=False)
    text = Column(Text, nullable=False)
    options = Column(JSON)  # Store as JSON array
    correct_answer = Column(String(500), nullable=False)
    explanation = Column(Text)
    order_index = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationship to quiz
    quiz = relationship("Quiz", back_populates="questions")

class VideoJob(Base):
    __tablename__ = "video_jobs"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    quiz_id = Column(String, ForeignKey("quizzes.id"), nullable=False)
    created_by = Column(String, ForeignKey("users.id"), nullable=True)  # nullable for existing data
    status = Column(String(50), default="pending")  # pending, processing, completed, failed, cancelled
    progress = Column(Integer, default=0)
    config = Column(JSON)  # Store video generation config as JSON
    output_path = Column(String(500))
    error_message = Column(Text)
    logs = Column(JSON, default=list)  # Store logs as JSON array
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    quiz = relationship("Quiz", back_populates="video_jobs")
    created_by_user = relationship("User", back_populates="video_jobs")

class APIKey(Base):
    __tablename__ = "api_keys"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    service_name = Column(String(100), nullable=False, unique=True)
    api_key = Column(Text, nullable=False)  # Encrypted in production
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class AccountTemplate(Base):
    __tablename__ = "account_templates"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False)
    display_name = Column(String(200), nullable=False)
    color_mode = Column(String(50), default="mode1")
    settings = Column(JSON)  # Additional settings as JSON
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class AppSettings(Base):
    __tablename__ = "app_settings"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    key = Column(String(100), nullable=False, unique=True)
    value = Column(JSON)  # Store any type of value as JSON
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(255), nullable=False, unique=True)
    username = Column(String(100), nullable=False, unique=True)
    full_name = Column(String(255), nullable=False)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    role = Column(String(50), default="user")  # user, admin, moderator
    avatar_url = Column(String(500))
    last_login = Column(DateTime)
    email_verification_token = Column(String(255))
    password_reset_token = Column(String(255))
    password_reset_expires = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    quizzes = relationship("Quiz", back_populates="created_by_user")
    video_jobs = relationship("VideoJob", back_populates="created_by_user")

    # Indexes for performance
    __table_args__ = (
        Index('idx_user_email', 'email'),
        Index('idx_user_username', 'username'),
        Index('idx_user_role', 'role'),
    )

# Database utility functions

def get_db() -> Session:
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """Create all tables"""
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created successfully!")

def drop_tables():
    """Drop all tables (use with caution!)"""
    Base.metadata.drop_all(bind=engine)
    print("⚠️ All database tables dropped!")

def init_default_data():
    """Initialize database with default data"""
    db = SessionLocal()
    try:
        # Create default account templates
        default_accounts = [
            {
                "name": "Account 1",
                "display_name": "@AGL LA3BAH",
                "color_mode": "mode1",
                "settings": {"font_size": 120, "shadow_enabled": True}
            },
            {
                "name": "Account 2", 
                "display_name": "@XVION_QUIZ",
                "color_mode": "mode2",
                "settings": {"font_size": 120, "shadow_enabled": True}
            }
        ]
        
        for account_data in default_accounts:
            existing = db.query(AccountTemplate).filter_by(name=account_data["name"]).first()
            if not existing:
                account = AccountTemplate(**account_data)
                db.add(account)
        
        # Create default app settings
        default_settings = [
            {"key": "default_video_duration", "value": 60, "description": "Default video duration in seconds"},
            {"key": "default_question_count", "value": 6, "description": "Default number of questions per video"},
            {"key": "output_quality", "value": "high", "description": "Default video output quality"},
            {"key": "default_language", "value": "ar", "description": "Default quiz language"},
        ]
        
        for setting_data in default_settings:
            existing = db.query(AppSettings).filter_by(key=setting_data["key"]).first()
            if not existing:
                setting = AppSettings(**setting_data)
                db.add(setting)
        
        db.commit()
        print("✅ Default data initialized successfully!")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Error initializing default data: {e}")
    finally:
        db.close()

def get_database_stats():
    """Get database statistics"""
    db = SessionLocal()
    try:
        stats = {
            "quizzes": db.query(Quiz).count(),
            "questions": db.query(Question).count(),
            "video_jobs": db.query(VideoJob).count(),
            "api_keys": db.query(APIKey).count(),
            "account_templates": db.query(AccountTemplate).count(),
            "app_settings": db.query(AppSettings).count(),
        }
        return stats
    finally:
        db.close()

# Database connection test
def test_connection():
    """Test database connection"""
    try:
        db = SessionLocal()
        result = db.execute(text("SELECT 1")).fetchone()
        db.close()
        print("✅ Database connection successful!")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Setting up Xvion database...")
    
    # Test connection
    if test_connection():
        # Create tables
        create_tables()
        
        # Initialize default data
        init_default_data()
        
        # Show stats
        stats = get_database_stats()
        print(f"📊 Database Statistics: {stats}")
        
        print("🎉 Database setup complete!")
    else:
        print("❌ Database setup failed!")
