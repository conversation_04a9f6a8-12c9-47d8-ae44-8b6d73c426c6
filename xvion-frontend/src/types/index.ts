export interface Question {
  id: string
  text: string
  options: string[]
  correctAnswer: string
  explanation?: string
}

export interface Quiz {
  id: string
  title: string
  description?: string
  questions: Question[]
  language: 'ar' | 'en'
  createdAt: Date
  updatedAt: Date
}

export interface VideoGenerationConfig {
  accountName: string
  displayName: string
  colorMode: 'mode1' | 'mode2' | 'mode3' | 'mode4'
  outputFolder: string
  fontType: string
  backgroundFolder: string
  numberOfQuestions: number
  questionDuration: number
  totalDuration: number
}

export interface APIKeys {
  deepl: string
  elevenlabs: string
  unsplash: string[]
  pixabay: string
  pexels: string
  openai: string
}

export interface VideoGenerationJob {
  id: string
  quizId: string
  config: VideoGenerationConfig
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  startedAt?: Date
  completedAt?: Date
  outputPath?: string
  error?: string
  logs: string[]
}

export interface VideoGenerationProgress {
  jobId: string
  status: VideoGenerationJob['status']
  progress: number
  currentStep: string
  message: string
  timestamp: Date
}

export interface Account {
  id: string
  name: string
  displayName: string
  colorMode: VideoGenerationConfig['colorMode']
  settings: {
    fontType: string
    backgroundFolder: string
    outputFolder: string
  }
}

export interface AppSettings {
  apiKeys: Partial<APIKeys>
  defaultAccount: string
  videoSettings: {
    defaultDuration: number
    defaultQuestionCount: number
    outputQuality: 'high' | 'medium' | 'low'
  }
  uiSettings: {
    theme: 'light' | 'dark' | 'system'
    language: 'en' | 'ar'
  }
}

export interface GenerationStats {
  totalVideos: number
  totalQuizzes: number
  successRate: number
  averageGenerationTime: number
  lastGenerated?: Date
}
