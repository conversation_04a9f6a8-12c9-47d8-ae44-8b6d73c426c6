'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'

// Types
interface User {
  id: string
  email: string
  username?: string
  name?: string
  full_name?: string
  is_active?: boolean
  is_verified?: boolean
  role?: string
  avatar_url?: string
  last_login?: string
  created_at?: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (token: string, user: User) => void
  logout: () => void
  updateUser: (user: Partial<User>) => void
  refreshUser: () => Promise<void>
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth provider component
interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedToken = localStorage.getItem('auth_token')
        const storedUser = localStorage.getItem('user')

        console.log('🔧 Initializing auth:', {
          hasToken: !!storedToken,
          hasUser: !!storedUser,
          tokenPreview: storedToken ? storedToken.substring(0, 20) + '...' : null,
          userPreview: storedUser ? storedUser.substring(0, 50) + '...' : null
        })

        if (storedToken && storedUser) {
          try {
            const parsedUser = JSON.parse(storedUser)
            console.log('✅ Restoring user from localStorage:', parsedUser)
            setToken(storedToken)
            setUser(parsedUser)
            console.log('✅ Auth state restored successfully')
          } catch (error) {
            console.error('❌ Error parsing stored user:', error)
            localStorage.removeItem('auth_token')
            localStorage.removeItem('user')
          }

          // For MVP, skip token verification
          // await refreshUserData(storedToken)
        } else {
          console.log('ℹ️ No stored auth data found')
        }
      } catch (error) {
        console.error('❌ Error initializing auth:', error)
        // Clear invalid data
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user')
      } finally {
        console.log('🏁 Auth initialization complete, setting isLoading to false')
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  // Refresh user data from server
  const refreshUserData = async (authToken?: string) => {
    const tokenToUse = authToken || token
    if (!tokenToUse) return

    try {
      console.log('🔄 Refreshing user data with token:', tokenToUse.substring(0, 20) + '...')

      const response = await fetch('http://localhost:8001/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${tokenToUse}`,
          'Content-Type': 'application/json',
        },
      })

      console.log('📡 User data refresh response:', response.status)

      if (response.ok) {
        const userData = await response.json()
        console.log('✅ User data refreshed:', userData)
        setUser(userData)
        localStorage.setItem('user', JSON.stringify(userData))
      } else if (response.status === 401) {
        // Token is invalid, logout user
        console.log('❌ Token invalid (401), logging out')
        logout()
      } else {
        console.log('⚠️ User data refresh failed with status:', response.status)
      }
    } catch (error) {
      console.error('❌ Error refreshing user data:', error)
    }
  }

  // Login function
  const login = (newToken: string, newUser: User) => {
    console.log('🔐 Login called with:', {
      tokenPreview: newToken.substring(0, 20) + '...',
      user: newUser
    })

    // Test localStorage availability
    try {
      localStorage.setItem('test', 'test')
      localStorage.removeItem('test')
      console.log('✅ localStorage is available')
    } catch (error) {
      console.error('❌ localStorage is not available:', error)
    }

    setToken(newToken)
    setUser(newUser)

    try {
      localStorage.setItem('auth_token', newToken)
      localStorage.setItem('user', JSON.stringify(newUser))
      console.log('✅ Login data saved to localStorage')

      // Verify it was saved
      const savedToken = localStorage.getItem('auth_token')
      const savedUser = localStorage.getItem('user')
      console.log('🔍 Verification - saved token:', savedToken?.substring(0, 20) + '...')
      console.log('🔍 Verification - saved user:', savedUser)
    } catch (error) {
      console.error('❌ Error saving to localStorage:', error)
    }
  }

  // Logout function
  const logout = () => {
    console.log('🚪 Logout called')
    console.trace('🔍 Logout call stack')

    setToken(null)
    setUser(null)
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user')

    console.log('✅ Logout completed, localStorage cleared')

    // Optional: Call logout endpoint to invalidate token on server
    // This is not strictly necessary for JWT tokens but good for security
    if (token) {
      fetch('http://localhost:8001/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }).catch(error => {
        console.error('Error during logout:', error)
      })
    }
  }

  // Update user function
  const updateUser = (updatedUser: Partial<User>) => {
    if (user) {
      const newUser = { ...user, ...updatedUser }
      setUser(newUser)
      localStorage.setItem('user', JSON.stringify(newUser))
    }
  }

  // Refresh user function
  const refreshUser = async () => {
    await refreshUserData()
  }

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    isAuthenticated: !!user && !!token,
    login,
    logout,
    updateUser,
    refreshUser,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// HOC for protected components
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth()

    if (isLoading) {
      return (
        <div className="d-flex justify-content-center align-items-center min-vh-100">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      )
    }

    if (!isAuthenticated) {
      return (
        <div className="d-flex justify-content-center align-items-center min-vh-100">
          <div className="text-center">
            <h3>Authentication Required</h3>
            <p className="text-muted">Please log in to access this page.</p>
            <button className="btn btn-primary" onClick={() => window.location.href = '/login'}>
              Go to Login
            </button>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}

// Protected Route component
interface ProtectedRouteProps {
  children: ReactNode
  fallback?: ReactNode
  requireVerified?: boolean
  requiredRole?: string
}

export function ProtectedRoute({ 
  children, 
  fallback,
  requireVerified = false,
  requiredRole 
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return fallback || (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <h3>Authentication Required</h3>
          <p className="text-muted">Please log in to access this page.</p>
          <button className="btn btn-primary" onClick={() => window.location.href = '/login'}>
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  // Check if email verification is required
  if (requireVerified && user && !user.is_verified) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <h3>Email Verification Required</h3>
          <p className="text-muted">Please verify your email address to access this page.</p>
          <button className="btn btn-primary" onClick={() => window.location.href = '/verify-email'}>
            Verify Email
          </button>
        </div>
      </div>
    )
  }

  // Check if specific role is required
  if (requiredRole && user && user.role !== requiredRole) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <h3>Access Denied</h3>
          <p className="text-muted">You don't have permission to access this page.</p>
          <button className="btn btn-secondary" onClick={() => window.history.back()}>
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// Hook for API calls with authentication
export function useAuthenticatedFetch() {
  const { token, logout } = useAuth()

  const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {}),
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    // If unauthorized, logout user
    if (response.status === 401) {
      logout()
      throw new Error('Authentication expired. Please log in again.')
    }

    return response
  }

  return authenticatedFetch
}

// Export types for use in other components
export type { User, AuthContextType }
