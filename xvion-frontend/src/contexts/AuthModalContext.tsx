'use client'

import { createContext, useContext, useState, ReactNode } from 'react'

type AuthModalMode = 'login' | 'register' | 'forgot-password'

interface AuthModalContextType {
  isOpen: boolean
  mode: AuthModalMode
  showModal: (mode: AuthModalMode) => void
  hideModal: () => void
}

const AuthModalContext = createContext<AuthModalContextType | undefined>(undefined)

interface AuthModalProviderProps {
  children: ReactNode
}

export function AuthModalProvider({ children }: AuthModalProviderProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [mode, setMode] = useState<AuthModalMode>('login')

  const showModal = (modalMode: AuthModalMode) => {
    console.log('🔓 AuthModalContext: showModal called with mode:', modalMode)
    console.log('🔓 AuthModalContext: Current state - isOpen:', isOpen, 'mode:', mode)
    setMode(modalMode)
    setIsOpen(true)
    console.log('🔓 AuthModalContext: Modal should now be open')
  }

  const hideModal = () => {
    console.log('🔒 AuthModal: Closing modal')
    setIsOpen(false)
  }

  const value: AuthModalContextType = {
    isOpen,
    mode,
    showModal,
    hideModal,
  }

  return (
    <AuthModalContext.Provider value={value}>
      {children}
    </AuthModalContext.Provider>
  )
}

// Custom hook to use auth modal context
export function useAuthModal() {
  const context = useContext(AuthModalContext)
  if (context === undefined) {
    throw new Error('useAuthModal must be used within an AuthModalProvider')
  }
  return context
}
