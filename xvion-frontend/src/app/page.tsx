'use client'

import { useState } from 'react'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { Dashboard } from '@/components/dashboard/Dashboard'
import { QuizManager } from '@/components/quiz/QuizManager'
import { VideoGenerator } from '@/components/video/VideoGenerator'
import { Settings } from '@/components/settings/Settings'
import { VideoJobs } from '@/components/video/VideoJobs'
import { UserProfile } from '@/components/profile/UserProfile'
import { GlobalAuthModal } from '@/components/auth/GlobalAuthModal'
import { useAuthModal } from '@/contexts/AuthModalContext'
// Bootstrap CSS is imported in globals.css

type ActiveView = 'dashboard' | 'quizzes' | 'generate' | 'jobs' | 'settings' | 'profile'

export default function Home() {
  const [activeView, setActiveView] = useState<ActiveView>('dashboard')
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { showModal } = useAuthModal()

  const handleNavigate = (view: string) => {
    setActiveView(view as ActiveView)
  }

  const renderContent = () => {
    switch (activeView) {
      case 'dashboard':
        return <Dashboard onNavigate={handleNavigate} />
      case 'quizzes':
        return <QuizManager />
      case 'generate':
        return <VideoGenerator />
      case 'jobs':
        return <VideoJobs />
      case 'settings':
        return <Settings />
      case 'profile':
        return <UserProfile />
      default:
        return <Dashboard onNavigate={handleNavigate} />
    }
  }

  return (
    <div className="d-flex vh-100">
        {/* Sidebar */}
        <Sidebar
          activeView={activeView}
          setActiveView={setActiveView}
          isOpen={sidebarOpen}
          setIsOpen={setSidebarOpen}
        />

        {/* Main content */}
        <div className="flex-fill d-flex flex-column overflow-hidden" style={{ marginLeft: '280px' }}>
          <Header
            onMenuClick={() => setSidebarOpen(!sidebarOpen)}
            title={getPageTitle(activeView)}
            onShowAuthModal={showModal}
          />

          <main className="flex-fill overflow-auto p-4 p-lg-5">
            <div className="container-fluid animate-fade-in">
              {renderContent()}
            </div>
          </main>
        </div>



        {/* Global Auth Modal - Managed by AuthModalContext */}
        <GlobalAuthModal />
    </div>
  )
}

function getPageTitle(view: ActiveView): string {
  switch (view) {
    case 'dashboard':
      return 'Dashboard'
    case 'quizzes':
      return 'Quiz Management'
    case 'generate':
      return 'Video Generator'
    case 'jobs':
      return 'Video Jobs'
    case 'settings':
      return 'Settings'
    case 'profile':
      return 'Profile'
    default:
      return 'Xvion'
  }
}
