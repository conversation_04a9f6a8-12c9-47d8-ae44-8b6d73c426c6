import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { AuthModalProvider } from "@/contexts/AuthModalContext";
import Script from "next/script";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Xvion - Arabic Quiz Video Generator",
  description: "Professional Arabic quiz video generation platform with AI-powered features",
  keywords: ["Arabic", "Quiz", "Video", "Generator", "AI", "Education"],
  authors: [{ name: "Xvion Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-100">
      <body className={`${inter.className} h-100`}>
        <AuthProvider>
          <AuthModalProvider>
            <div className="min-vh-100">
              {children}
            </div>
          </AuthModalProvider>
        </AuthProvider>

        {/* Bootstrap JavaScript */}
        <Script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
          strategy="afterInteractive"
        />
      </body>
    </html>
  );
}
