/* Import Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom CSS Variables for theming */
:root {
  --bs-primary: #dc3545;
  --bs-primary-rgb: 220, 53, 69;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* Global Styles */
body {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  font-family: var(--bs-font-sans-serif);
}

/* Custom Bootstrap Component Overrides */
.btn {
  border-radius: 12px;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease-in-out;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: linear-gradient(135deg, #dc3545 0%, #e91e63 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #c82333 0%, #d81b60 100%);
}

.btn-outline-primary {
  border: 2px solid #dc3545;
  color: #dc3545;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

.card {
  border-radius: 16px;
  border: 1px solid rgba(0,0,0,0.08);
  box-shadow: 0 4px 6px rgba(0,0,0,0.05);
  backdrop-filter: blur(10px);
  background: rgba(255,255,255,0.95);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.card-header {
  border-bottom: 1px solid rgba(0,0,0,0.08);
  background: rgba(248,249,250,0.5);
  border-radius: 16px 16px 0 0;
}

.form-control {
  border-radius: 12px;
  border: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  background: rgba(255,255,255,0.8);
}

.form-control:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220,53,69,0.25);
  background: white;
}

.badge {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #dee2e6, #adb5bd);
  border-radius: 4px;
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #dc3545, #e91e63);
}

/* Arabic text support */
.rtl {
  direction: rtl;
  text-align: right;
  font-family: 'Noto Sans Arabic', 'Cairo', 'Amiri', sans-serif;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* Animation utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.4s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.4s ease-out;
}

/* Utility classes */
.hover-lift {
  transition: all 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-primary {
  background: linear-gradient(135deg, #dc3545 0%, #e91e63 100%);
}

/* Additional Bootstrap utilities */
.hover-bg-light:hover {
  background-color: #f8f9fa !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.bg-success-subtle {
  background-color: #d1e7dd !important;
}

.bg-danger-subtle {
  background-color: #f8d7da !important;
}

.bg-warning-subtle {
  background-color: #fff3cd !important;
}

.bg-primary-subtle {
  background-color: #cfe2ff !important;
}

.bg-info-subtle {
  background-color: #d1ecf1 !important;
}

.text-success {
  color: #198754 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-primary {
  color: #0d6efd !important;
}

.text-info {
  color: #0dcaf0 !important;
}

/* Fix for responsive layout */
@media (max-width: 991.98px) {
  .d-flex.vh-100 > .flex-fill {
    margin-left: 0 !important;
  }
}

/* Modal Fixes */
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 1040 !important;
}

.modal {
  z-index: 1050 !important;
}

.modal.show {
  display: block !important;
}

.modal-dialog {
  margin: 1.75rem auto !important;
  max-width: 500px !important;
}

.modal-content {
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
