'use client'

import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import {
  XMarkIcon,
  HomeIcon,
  DocumentTextIcon,
  VideoCameraIcon,
  QueueListIcon,
  Cog6ToothIcon,
  PlayIcon,
  UserIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '../../contexts/AuthContext'

type ActiveView = 'dashboard' | 'quizzes' | 'generate' | 'jobs' | 'settings' | 'profile'

interface SidebarProps {
  activeView: ActiveView
  setActiveView: (view: ActiveView) => void
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}

export function Sidebar({ activeView, setActiveView, isOpen, setIsOpen }: SidebarProps) {
  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setIsOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button type="button" className="-m-2.5 p-2.5" onClick={() => setIsOpen(false)}>
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <SidebarContent activeView={activeView} setActiveView={setActiveView} />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="d-none d-lg-flex position-fixed top-0 start-0 h-100" style={{ width: '280px', zIndex: 1050 }}>
        <SidebarContent activeView={activeView} setActiveView={setActiveView} />
      </div>
    </>
  )
}

function SidebarContent({ activeView, setActiveView }: { activeView: ActiveView; setActiveView: (view: ActiveView) => void }) {
  const { user, isAuthenticated } = useAuth()

  const navigation = [
    { name: 'Dashboard', id: 'dashboard', icon: HomeIcon },
    { name: 'Quiz Management', id: 'quizzes', icon: DocumentTextIcon },
    { name: 'Video Generator', id: 'generate', icon: VideoCameraIcon },
    { name: 'Video Jobs', id: 'jobs', icon: QueueListIcon },
    { name: 'Profile', id: 'profile', icon: UserIcon },
    { name: 'Settings', id: 'settings', icon: Cog6ToothIcon },
  ]

  return (
    <div className="d-flex flex-column h-100 w-100 glass shadow-lg border-end">
      {/* Logo Section */}
      <div className="p-4 border-bottom">
        <div className="d-flex align-items-center">
          <div className="d-flex align-items-center justify-content-center rounded-3 gradient-primary shadow-sm me-3" style={{ width: '48px', height: '48px' }}>
            <PlayIcon className="text-white" style={{ width: '28px', height: '28px' }} />
          </div>
          <div>
            <h1 className="h4 fw-bold mb-0 gradient-primary text-white" style={{
              background: 'linear-gradient(135deg, #dc3545 0%, #e91e63 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              Xvion
            </h1>
            <small className="text-muted fw-medium">Video Generator</small>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-fill p-3">
        <div className="d-flex flex-column gap-2">
          {navigation.map((item) => (
            <button
              key={item.name}
              onClick={() => {
                console.log('Switching to view:', item.id);
                setActiveView(item.id as ActiveView);
              }}
              className={`btn d-flex align-items-center gap-3 p-3 text-start w-100 hover-lift ${
                activeView === item.id
                  ? 'btn-primary shadow-sm'
                  : 'btn-outline-light text-dark border-0 hover-bg-light'
              }`}
              style={{
                borderRadius: '12px',
                transition: 'all 0.2s ease',
                backgroundColor: activeView === item.id ? undefined : 'transparent'
              }}
            >
              <item.icon
                style={{ width: '20px', height: '20px' }}
                className={activeView === item.id ? 'text-white' : 'text-muted'}
              />
              <span className="fw-semibold">{item.name}</span>
              {activeView === item.id && (
                <div
                  className="ms-auto rounded-circle bg-white opacity-75"
                  style={{ width: '8px', height: '8px' }}
                />
              )}
            </button>
          ))}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-top">
        {isAuthenticated && user ? (
          <div className="card border-0 bg-light">
            <div className="card-body p-3">
              <div className="d-flex align-items-center">
                <div
                  className="rounded-circle gradient-primary d-flex align-items-center justify-content-center text-white fw-bold me-3"
                  style={{ width: '32px', height: '32px', fontSize: '14px' }}
                >
                  {user.name ? user.name.charAt(0).toUpperCase() : user.username?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div className="flex-fill">
                  <div className="fw-semibold small">{user.name || user.full_name || user.username}</div>
                  <div className="text-muted" style={{ fontSize: '12px' }}>{user.email}</div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="card border-0 bg-light">
            <div className="card-body p-3 text-center">
              <div className="text-muted small">Please sign in to continue</div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
