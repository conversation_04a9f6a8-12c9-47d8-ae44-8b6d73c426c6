'use client'

import { Fragment, useState } from 'react'
import {
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '../../contexts/AuthContext'
interface HeaderProps {
  onMenuClick: () => void
  title: string
  onShowAuthModal: (mode: 'login' | 'register' | 'forgot-password') => void
}

export function Header({ onMenuClick, title, onShowAuthModal }: HeaderProps) {
  const { user, isAuthenticated, logout, isLoading } = useAuth()

  console.log('🔍 Header: Auth state - isAuthenticated:', isAuthenticated, 'user:', user, 'isLoading:', isLoading)

  const handleLogout = () => {
    logout()
    // Optionally redirect to home page
    window.location.href = '/'
  }

  const getUserInitials = (fullName: string) => {
    return fullName
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }
  return (
    <header className="sticky-top glass border-bottom shadow-sm" style={{ zIndex: 1040 }}>
      <div className="container-fluid py-3">
        <div className="d-flex align-items-center justify-content-between">
          {/* Mobile menu button */}
          <button
            type="button"
            className="btn btn-outline-secondary d-lg-none me-3"
            onClick={onMenuClick}
            style={{ borderRadius: '12px' }}
          >
            <Bars3Icon style={{ width: '20px', height: '20px' }} />
          </button>

          {/* Title Section */}
          <div className="flex-fill">
            <h1 className="h3 fw-bold mb-1">{title}</h1>
            <p className="text-muted small mb-0">
              {getSubtitle(title)}
            </p>
          </div>

          {/* Right side controls */}
          <div className="d-flex align-items-center gap-3">
            {/* Search - only show for authenticated users */}
            {isAuthenticated && (
              <div className="position-relative" style={{ minWidth: '300px' }}>
                <div className="position-absolute top-50 start-0 translate-middle-y ps-3">
                  <svg
                    width="16"
                    height="16"
                    fill="currentColor"
                    className="text-muted"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <input
                  type="search"
                  className="form-control ps-5"
                  placeholder="Search quizzes, videos..."
                  style={{ borderRadius: '12px' }}
                />
              </div>
            )}

            {/* Authentication-dependent content */}
            {isLoading ? (
              <div className="d-flex align-items-center">
                <div className="spinner-border spinner-border-sm" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            ) : isAuthenticated ? (
              <>
                {/* Notifications */}
                <button
                  type="button"
                  className="btn btn-outline-secondary position-relative"
                  style={{ borderRadius: '12px' }}
                >
                  <BellIcon style={{ width: '20px', height: '20px' }} />
                  <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style={{ fontSize: '10px' }}>
                    3
                  </span>
                </button>

                {/* Profile dropdown */}
                <div className="dropdown">
                  <button
                    className="btn btn-outline-secondary d-flex align-items-center gap-2 dropdown-toggle"
                    type="button"
                    data-bs-toggle="dropdown"
                    style={{ borderRadius: '12px' }}
                  >
                    {user?.avatar_url ? (
                      <img
                        src={user.avatar_url}
                        alt={user.full_name}
                        className="rounded-circle"
                        style={{ width: '32px', height: '32px' }}
                      />
                    ) : (
                      <div
                        className="rounded-circle gradient-primary d-flex align-items-center justify-content-center text-white fw-bold"
                        style={{ width: '32px', height: '32px', fontSize: '14px' }}
                      >
                        {user ? getUserInitials(user.full_name || user.name || 'User') : 'U'}
                      </div>
                    )}
                    <span className="d-none d-lg-inline fw-semibold">
                      {user?.full_name || user?.name || 'User'}
                    </span>
                  </button>
                  <ul className="dropdown-menu dropdown-menu-end shadow-lg border-0" style={{ borderRadius: '12px' }}>
                    <li>
                      <div className="dropdown-item-text py-2">
                        <div className="fw-semibold">{user?.full_name || user?.name}</div>
                        <div className="text-muted small">{user?.email}</div>
                      </div>
                    </li>
                    <li><hr className="dropdown-divider" /></li>
                    <li>
                      <a className="dropdown-item py-2 d-flex align-items-center gap-2" href="/profile" style={{ borderRadius: '8px' }}>
                        <UserCircleIcon style={{ width: '16px', height: '16px' }} />
                        Your Profile
                      </a>
                    </li>
                    <li>
                      <a className="dropdown-item py-2 d-flex align-items-center gap-2" href="/settings" style={{ borderRadius: '8px' }}>
                        <Cog6ToothIcon style={{ width: '16px', height: '16px' }} />
                        Settings
                      </a>
                    </li>
                    <li><hr className="dropdown-divider" /></li>
                    <li>
                      <button
                        className="dropdown-item py-2 d-flex align-items-center gap-2 text-danger"
                        onClick={handleLogout}
                        style={{ borderRadius: '8px' }}
                      >
                        <ArrowRightOnRectangleIcon style={{ width: '16px', height: '16px' }} />
                        Sign out
                      </button>
                    </li>
                  </ul>
                </div>
              </>
            ) : (
              /* Login/Register buttons for unauthenticated users */
              <div className="d-flex align-items-center gap-2">
                <button
                  type="button"
                  className="btn btn-outline-primary"
                  onClick={() => {
                    console.log('🖱️ Header: Sign In button clicked')
                    onShowAuthModal('login')
                  }}
                  style={{ borderRadius: '12px' }}
                >
                  Sign In
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={() => {
                    console.log('🖱️ Header: Get Started button clicked')
                    onShowAuthModal('register')
                  }}
                  style={{ borderRadius: '12px' }}
                >
                  Get Started
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

    </header>
  )
}

function getSubtitle(title: string): string {
  switch (title) {
    case 'Dashboard':
      return 'Overview of your video generation activities'
    case 'Quiz Management':
      return 'Create and manage your quiz content'
    case 'Video Generator':
      return 'Generate engaging Arabic quiz videos'
    case 'Video Jobs':
      return 'Monitor video generation progress'
    case 'Settings':
      return 'Configure your application preferences'
    default:
      return 'Arabic Quiz Video Generator'
  }
}
