'use client'

import { useState, useEffect } from 'react'
import {
  DocumentTextIcon,
  VideoCameraIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  QueueListIcon,
  UserPlusIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '../../contexts/AuthContext'
import { useAuthModal } from '../../contexts/AuthModalContext'
import { quizApi, videoApi } from '../../utils/apiClient'

interface DashboardStats {
  totalQuizzes: number
  totalVideos: number
  activeJobs: number
  completedToday: number
}

interface RecentActivity {
  id: string
  type: 'quiz_created' | 'video_generated' | 'job_completed' | 'job_failed'
  title: string
  timestamp: Date
  status?: 'success' | 'error' | 'pending'
}

interface DashboardProps {
  onNavigate?: (view: string) => void;
}

export function Dashboard({ onNavigate }: DashboardProps = {}) {
  const { user, isAuthenticated } = useAuth()
  const { showModal } = useAuthModal()
  const [stats, setStats] = useState<DashboardStats>({
    totalQuizzes: 0,
    totalVideos: 0,
    activeJobs: 0,
    completedToday: 0
  })

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (isAuthenticated) {
      loadDashboardData()
    } else {
      setLoading(false)
    }
  }, [isAuthenticated])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Load user's quizzes and video jobs
      const [quizzesResponse, jobsResponse] = await Promise.all([
        quizApi.getQuizzes({ limit: 100 }),
        videoApi.getVideoJobs({ limit: 100 })
      ])

      const totalQuizzes = quizzesResponse.total || 0
      const totalVideos = jobsResponse.items?.filter(job => job.status === 'completed').length || 0
      const activeJobs = jobsResponse.items?.filter(job => job.status === 'pending' || job.status === 'processing').length || 0

      // Calculate completed today
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const completedToday = jobsResponse.items?.filter(job =>
        job.status === 'completed' && new Date(job.updated_at) >= today
      ).length || 0

      setStats({
        totalQuizzes,
        totalVideos,
        activeJobs,
        completedToday
      })

      // Set recent activity
      const activities: RecentActivity[] = []

      // Add recent quizzes
      quizzesResponse.items?.slice(0, 3).forEach(quiz => {
        activities.push({
          id: quiz.id,
          type: 'quiz_created',
          title: `Created quiz: ${quiz.title}`,
          timestamp: new Date(quiz.created_at),
          status: 'success'
        })
      })

      // Add recent video jobs
      jobsResponse.items?.slice(0, 5).forEach(job => {
        activities.push({
          id: job.id,
          type: job.status === 'completed' ? 'job_completed' : job.status === 'failed' ? 'job_failed' : 'video_generated',
          title: `Video generation ${job.status}`,
          timestamp: new Date(job.updated_at),
          status: job.status === 'completed' ? 'success' : job.status === 'failed' ? 'error' : 'pending'
        })
      })

      // Sort by timestamp
      activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      setRecentActivity(activities.slice(0, 8))

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      // Set empty data on error
      setStats({
        totalQuizzes: 0,
        totalVideos: 0,
        activeJobs: 0,
        completedToday: 0
      })
      setRecentActivity([])
    } finally {
      setLoading(false)
    }
  }

  // Show welcome screen for unauthenticated users
  if (!isAuthenticated) {
    return (
      <>
        <div className="container-fluid py-5">
          <div className="row justify-content-center">
            <div className="col-lg-8 text-center">
              {/* Hero Section */}
              <div className="mb-5">
                <h1 className="display-4 fw-bold mb-4">
                  Welcome to <span className="text-primary">Xvion</span>
                </h1>
                <p className="lead text-muted mb-4">
                  Create engaging Arabic quiz videos with AI-powered content generation.
                  Transform your educational content into interactive video experiences.
                </p>
                <div className="d-flex justify-content-center gap-3">
                  <button
                    className="btn btn-primary btn-lg px-4"
                    onClick={() => showModal('register')}
                  >
                    <UserPlusIcon className="me-2" width={20} height={20} />
                    Get Started
                  </button>
                  <button
                    className="btn btn-outline-primary btn-lg px-4"
                    onClick={() => showModal('login')}
                  >
                    Sign In
                    <ArrowRightIcon className="ms-2" width={20} height={20} />
                  </button>
                </div>
              </div>

              {/* Features */}
              <div className="row g-4 mb-5">
                <div className="col-md-4">
                  <div className="card border-0 shadow-sm h-100">
                    <div className="card-body text-center p-4">
                      <div className="rounded-circle bg-primary bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '60px', height: '60px' }}>
                        <DocumentTextIcon className="text-primary" width={30} height={30} />
                      </div>
                      <h5 className="fw-bold mb-2">Smart Quiz Creation</h5>
                      <p className="text-muted small">
                        Generate comprehensive Arabic quizzes with AI assistance and customizable templates.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card border-0 shadow-sm h-100">
                    <div className="card-body text-center p-4">
                      <div className="rounded-circle bg-success bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '60px', height: '60px' }}>
                        <VideoCameraIcon className="text-success" width={30} height={30} />
                      </div>
                      <h5 className="fw-bold mb-2">Video Generation</h5>
                      <p className="text-muted small">
                        Transform your quizzes into engaging video content with automated narration and visuals.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card border-0 shadow-sm h-100">
                    <div className="card-body text-center p-4">
                      <div className="rounded-circle bg-info bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '60px', height: '60px' }}>
                        <QueueListIcon className="text-info" width={30} height={30} />
                      </div>
                      <h5 className="fw-bold mb-2">Job Management</h5>
                      <p className="text-muted small">
                        Track video generation progress and manage your content library efficiently.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </>
    )
  }

  const statCards = [
    {
      name: 'Total Quizzes',
      value: stats.totalQuizzes,
      icon: DocumentTextIcon,
      gradient: 'from-blue-500 to-cyan-500',
      change: '+12%',
      changeType: 'positive',
      description: 'Quiz content created'
    },
    {
      name: 'Videos Generated',
      value: stats.totalVideos,
      icon: VideoCameraIcon,
      gradient: 'from-green-500 to-emerald-500',
      change: '+8%',
      changeType: 'positive',
      description: 'Videos successfully created'
    },
    {
      name: 'Active Jobs',
      value: stats.activeJobs,
      icon: ClockIcon,
      gradient: 'from-yellow-500 to-orange-500',
      change: '-2',
      changeType: 'neutral',
      description: 'Currently processing'
    },
    {
      name: 'Completed Today',
      value: stats.completedToday,
      icon: CheckCircleIcon,
      gradient: 'from-purple-500 to-pink-500',
      change: '+3',
      changeType: 'positive',
      description: 'Jobs finished today'
    }
  ]

  if (loading) {
    return (
      <div className="animate-fade-in">
        {/* Loading Welcome Section */}
        <div className="card mb-4 border-0">
          <div className="card-body p-5">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <div className="skeleton mb-3" style={{ height: '32px', width: '250px' }}></div>
                <div className="skeleton" style={{ height: '16px', width: '400px' }}></div>
              </div>
              <div className="skeleton rounded-3" style={{ height: '64px', width: '64px' }}></div>
            </div>
          </div>
        </div>

        {/* Loading Stats Grid */}
        <div className="row g-4 mb-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="col-lg-3 col-md-6">
              <div className="card h-100 border-0">
                <div className="card-body p-4">
                  <div className="d-flex align-items-center gap-3">
                    <div className="skeleton rounded-3" style={{ height: '48px', width: '48px' }}></div>
                    <div className="flex-fill">
                      <div className="skeleton mb-2" style={{ height: '16px', width: '80px' }}></div>
                      <div className="skeleton" style={{ height: '24px', width: '60px' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Loading Activity */}
        <div className="card border-0">
          <div className="card-header bg-transparent">
            <div className="skeleton" style={{ height: '24px', width: '150px' }}></div>
          </div>
          <div className="card-body p-0">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="d-flex align-items-center gap-3 p-4 border-bottom">
                <div className="skeleton rounded-circle" style={{ height: '40px', width: '40px' }}></div>
                <div className="flex-fill">
                  <div className="skeleton mb-2" style={{ height: '16px', width: '200px' }}></div>
                  <div className="skeleton" style={{ height: '12px', width: '100px' }}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="animate-fade-in">
      {/* Welcome Section */}
      <div className="card mb-4 border-0 gradient-primary text-white position-relative overflow-hidden">
        <div className="position-absolute top-0 start-0 w-100 h-100" style={{ background: 'rgba(0,0,0,0.1)' }}></div>
        <div className="card-body p-5 position-relative">
          <div className="row align-items-center">
            <div className="col-lg-8">
              <h1 className="display-5 fw-bold mb-3">Welcome to Xvion</h1>
              <p className="lead mb-4 opacity-90">
                Create engaging Arabic quiz videos with AI-powered generation
              </p>
              <div className="d-flex flex-wrap gap-4 text-white-50">
                <div className="d-flex align-items-center gap-2">
                  <div className="rounded-circle bg-success" style={{ width: '8px', height: '8px' }}></div>
                  <small className="fw-medium">System Online</small>
                </div>
                <div className="d-flex align-items-center gap-2">
                  <DocumentTextIcon style={{ width: '16px', height: '16px' }} />
                  <small className="fw-medium">{stats.totalQuizzes} Quizzes</small>
                </div>
                <div className="d-flex align-items-center gap-2">
                  <VideoCameraIcon style={{ width: '16px', height: '16px' }} />
                  <small className="fw-medium">{stats.totalVideos} Videos</small>
                </div>
              </div>
            </div>
            <div className="col-lg-4 text-end d-none d-lg-block">
              <PlayIcon style={{ width: '80px', height: '80px' }} className="opacity-75" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="row g-4 mb-4">
        {statCards.map((stat, index) => (
          <div key={stat.name} className="col-lg-3 col-md-6">
            <div className="card h-100 hover-lift border-0 shadow-sm">
              <div className="card-body p-4">
                <div className="d-flex align-items-start justify-content-between">
                  <div>
                    <div
                      className="rounded-3 p-3 mb-3 shadow-sm"
                      style={{
                        background: `linear-gradient(135deg, ${getGradientColors(stat.gradient)})`,
                        width: 'fit-content'
                      }}
                    >
                      <stat.icon style={{ width: '24px', height: '24px' }} className="text-white" />
                    </div>
                    <h6 className="text-muted text-uppercase fw-semibold small mb-1">
                      {stat.name}
                    </h6>
                    <p className="text-muted small mb-0">{stat.description}</p>
                  </div>
                  <div className="text-end">
                    <h2 className="fw-bold mb-2">{stat.value}</h2>
                    <span className={`badge ${getBadgeClass(stat.changeType)}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="card mb-4 border-0 shadow-sm animate-slide-in">
        <div className="card-header bg-transparent border-bottom">
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <h5 className="card-title mb-1">Recent Activity</h5>
              <p className="text-muted small mb-0">Latest updates from your video generation</p>
            </div>
            <button className="btn btn-outline-primary btn-sm">View All</button>
          </div>
        </div>
        <div className="card-body p-0">
          {recentActivity.map((activity, index) => (
            <div
              key={activity.id}
              className="d-flex align-items-center justify-content-between p-4 border-bottom hover-bg-light"
            >
              <div className="d-flex align-items-center gap-3">
                <div className={`rounded-3 p-2 ${getActivityBgClass(activity.status || 'pending')}`}>
                  {activity.status === 'success' && <CheckCircleIcon style={{ width: '20px', height: '20px' }} />}
                  {activity.status === 'error' && <ExclamationTriangleIcon style={{ width: '20px', height: '20px' }} />}
                  {activity.status === 'pending' && <ClockIcon style={{ width: '20px', height: '20px' }} />}
                </div>
                <div>
                  <h6 className="mb-1 fw-semibold">{activity.title}</h6>
                  <small className="text-muted">{formatTimeAgo(activity.timestamp)}</small>
                </div>
              </div>
              <span className={`badge ${getBadgeClass(activity.status === 'success' ? 'positive' : activity.status === 'error' ? 'negative' : 'neutral')}`}>
                {activity.status}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card border-0 shadow-sm animate-slide-in">
        <div className="card-body p-5 text-center">
          <h5 className="card-title mb-2">Quick Actions</h5>
          <p className="text-muted mb-4">Get started with these common tasks</p>
          <div className="row g-3">
            <div className="col-lg-4">
              <button
                className="btn btn-primary w-100 d-flex align-items-center justify-content-center gap-2 hover-lift"
                onClick={() => {
                  console.log('Create New Quiz clicked');
                  if (onNavigate) {
                    onNavigate('quizzes');
                  } else {
                    alert('Navigate to Quiz Management to create a new quiz!');
                  }
                }}
              >
                <DocumentTextIcon style={{ width: '20px', height: '20px' }} />
                Create New Quiz
              </button>
            </div>
            <div className="col-lg-4">
              <button
                className="btn btn-outline-primary w-100 d-flex align-items-center justify-content-center gap-2 hover-lift"
                onClick={() => {
                  console.log('Generate Video clicked');
                  if (onNavigate) {
                    onNavigate('generate');
                  } else {
                    alert('Navigate to Video Generator to create videos!');
                  }
                }}
              >
                <VideoCameraIcon style={{ width: '20px', height: '20px' }} />
                Generate Video
              </button>
            </div>
            <div className="col-lg-4">
              <button
                className="btn btn-outline-secondary w-100 d-flex align-items-center justify-content-center gap-2 hover-lift"
                onClick={() => {
                  console.log('View All Jobs clicked');
                  if (onNavigate) {
                    onNavigate('jobs');
                  } else {
                    alert('Navigate to Video Jobs to view all processing jobs!');
                  }
                }}
              >
                <QueueListIcon style={{ width: '20px', height: '20px' }} />
                View All Jobs
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function formatTimeAgo(date: Date): string {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`

  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays}d ago`
}

function getGradientColors(gradient: string): string {
  const gradientMap: { [key: string]: string } = {
    'from-blue-500 to-cyan-500': '#3b82f6, #06b6d4',
    'from-green-500 to-emerald-500': '#10b981, #059669',
    'from-yellow-500 to-orange-500': '#f59e0b, #ea580c',
    'from-purple-500 to-pink-500': '#8b5cf6, #ec4899'
  }
  return gradientMap[gradient] || '#dc3545, #e91e63'
}

function getBadgeClass(changeType: string): string {
  switch (changeType) {
    case 'positive':
      return 'bg-success text-white'
    case 'negative':
      return 'bg-danger text-white'
    default:
      return 'bg-secondary text-white'
  }
}

function getActivityBgClass(status: string): string {
  switch (status) {
    case 'success':
      return 'bg-success-subtle text-success'
    case 'error':
      return 'bg-danger-subtle text-danger'
    default:
      return 'bg-warning-subtle text-warning'
  }
}
