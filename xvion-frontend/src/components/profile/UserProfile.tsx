'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  UserCircleIcon,
  PencilIcon,
  KeyIcon,
  ShieldCheckIcon,
  EnvelopeIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '../../contexts/AuthContext'
import { authApi } from '../../utils/apiClient'

// Validation schemas
const profileSchema = z.object({
  full_name: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  username: z.string().min(3, 'Username must be at least 3 characters'),
})

const passwordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirm_password: z.string()
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
})

type ProfileFormData = z.infer<typeof profileSchema>
type PasswordFormData = z.infer<typeof passwordSchema>

export function UserProfile() {
  const { user, updateUser, refreshUser } = useAuth()
  const [activeTab, setActiveTab] = useState<'profile' | 'security'>('profile')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  })

  // Profile form
  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      full_name: user?.full_name || '',
      email: user?.email || '',
      username: user?.username || '',
    }
  })

  // Password form
  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema)
  })

  // Update form values when user data changes
  useEffect(() => {
    if (user) {
      profileForm.reset({
        full_name: user.full_name,
        email: user.email,
        username: user.username,
      })
    }
  }, [user, profileForm])

  // Handle profile update
  const handleProfileUpdate = async (data: ProfileFormData) => {
    setIsLoading(true)
    setMessage(null)

    try {
      const updatedUser = await authApi.updateProfile(data)
      updateUser(updatedUser)
      setMessage({ type: 'success', text: 'Profile updated successfully!' })
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to update profile' })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle password change
  const handlePasswordChange = async (data: PasswordFormData) => {
    setIsLoading(true)
    setMessage(null)

    try {
      await authApi.changePassword({
        current_password: data.current_password,
        new_password: data.new_password
      })
      passwordForm.reset()
      setMessage({ type: 'success', text: 'Password changed successfully!' })
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to change password' })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle email verification resend
  const handleResendVerification = async () => {
    setIsLoading(true)
    setMessage(null)

    try {
      await authApi.resendVerification()
      setMessage({ type: 'success', text: 'Verification email sent!' })
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to send verification email' })
    } finally {
      setIsLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    )
  }

  // Don't render if user is not available
  if (!user) {
    return (
      <div className="container-fluid py-4">
        <div className="row justify-content-center">
          <div className="col-lg-8 text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3 text-muted">Loading profile...</p>
          </div>
        </div>
      </div>
    )
  }

  const getUserInitials = (user: any) => {
    const name = user.full_name || user.name || user.username || 'User'
    return name.split(' ').map((n: string) => n.charAt(0)).join('').toUpperCase().slice(0, 2)
  }

  return (
    <div className="container-fluid py-4">
      <div className="row justify-content-center">
        <div className="col-lg-8">
          {/* Header */}
          <div className="d-flex align-items-center mb-4">
            <div className="me-3">
              {user.avatar_url ? (
                <img
                  src={user.avatar_url}
                  alt={user.full_name || user.name || 'User'}
                  className="rounded-circle"
                  style={{ width: '60px', height: '60px' }}
                />
              ) : (
                <div
                  className="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold"
                  style={{ width: '60px', height: '60px', fontSize: '24px' }}
                >
                  {getUserInitials(user)}
                </div>
              )}
            </div>
            <div>
              <h2 className="h3 fw-bold mb-1">{user.full_name || user.name || user.username}</h2>
              <p className="text-muted mb-0">@{user.username}</p>
              <div className="d-flex align-items-center gap-2 mt-1">
                {user.is_verified ? (
                  <span className="badge bg-success d-flex align-items-center gap-1">
                    <CheckCircleIcon width={14} height={14} />
                    Verified
                  </span>
                ) : (
                  <span className="badge bg-warning d-flex align-items-center gap-1">
                    <XCircleIcon width={14} height={14} />
                    Unverified
                  </span>
                )}
                <span className="badge bg-secondary">{user.role}</span>
              </div>
            </div>
          </div>

          {/* Message Alert */}
          {message && (
            <div className={`alert alert-${message.type === 'success' ? 'success' : 'danger'} d-flex align-items-center mb-4`} role="alert">
              {message.type === 'success' ? (
                <CheckCircleIcon className="flex-shrink-0 me-2" width={20} height={20} />
              ) : (
                <XCircleIcon className="flex-shrink-0 me-2" width={20} height={20} />
              )}
              <div>{message.text}</div>
            </div>
          )}

          {/* Tabs */}
          <ul className="nav nav-tabs mb-4" role="tablist">
            <li className="nav-item" role="presentation">
              <button
                className={`nav-link ${activeTab === 'profile' ? 'active' : ''}`}
                onClick={() => setActiveTab('profile')}
                type="button"
              >
                <UserCircleIcon className="me-2" width={16} height={16} />
                Profile Information
              </button>
            </li>
            <li className="nav-item" role="presentation">
              <button
                className={`nav-link ${activeTab === 'security' ? 'active' : ''}`}
                onClick={() => setActiveTab('security')}
                type="button"
              >
                <ShieldCheckIcon className="me-2" width={16} height={16} />
                Security
              </button>
            </li>
          </ul>

          {/* Tab Content */}
          <div className="tab-content">
            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <div className="card border-0 shadow-sm">
                <div className="card-body p-4">
                  <h5 className="card-title mb-4">
                    <PencilIcon className="me-2" width={20} height={20} />
                    Edit Profile
                  </h5>

                  <form onSubmit={profileForm.handleSubmit(handleProfileUpdate)}>
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label htmlFor="full_name" className="form-label fw-semibold">
                          Full Name
                        </label>
                        <input
                          type="text"
                          className={`form-control ${profileForm.formState.errors.full_name ? 'is-invalid' : ''}`}
                          id="full_name"
                          {...profileForm.register('full_name')}
                          disabled={isLoading}
                        />
                        {profileForm.formState.errors.full_name && (
                          <div className="invalid-feedback">
                            {profileForm.formState.errors.full_name.message}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6 mb-3">
                        <label htmlFor="username" className="form-label fw-semibold">
                          Username
                        </label>
                        <input
                          type="text"
                          className={`form-control ${profileForm.formState.errors.username ? 'is-invalid' : ''}`}
                          id="username"
                          {...profileForm.register('username')}
                          disabled={isLoading}
                        />
                        {profileForm.formState.errors.username && (
                          <div className="invalid-feedback">
                            {profileForm.formState.errors.username.message}
                          </div>
                        )}
                      </div>

                      <div className="col-12 mb-3">
                        <label htmlFor="email" className="form-label fw-semibold">
                          Email Address
                        </label>
                        <div className="input-group">
                          <input
                            type="email"
                            className={`form-control ${profileForm.formState.errors.email ? 'is-invalid' : ''}`}
                            id="email"
                            {...profileForm.register('email')}
                            disabled={isLoading}
                          />
                          {!user.is_verified && (
                            <button
                              type="button"
                              className="btn btn-outline-warning"
                              onClick={handleResendVerification}
                              disabled={isLoading}
                            >
                              <EnvelopeIcon width={16} height={16} className="me-1" />
                              Verify
                            </button>
                          )}
                        </div>
                        {profileForm.formState.errors.email && (
                          <div className="invalid-feedback">
                            {profileForm.formState.errors.email.message}
                          </div>
                        )}
                        {!user.is_verified && (
                          <div className="form-text text-warning">
                            Your email address is not verified. Click "Verify" to resend verification email.
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="d-flex justify-content-end">
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={isLoading || !profileForm.formState.isDirty}
                      >
                        {isLoading ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Updating...
                          </>
                        ) : (
                          'Update Profile'
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div className="card border-0 shadow-sm">
                <div className="card-body p-4">
                  <h5 className="card-title mb-4">
                    <KeyIcon className="me-2" width={20} height={20} />
                    Change Password
                  </h5>

                  <form onSubmit={passwordForm.handleSubmit(handlePasswordChange)}>
                    <div className="mb-3">
                      <label htmlFor="current_password" className="form-label fw-semibold">
                        Current Password
                      </label>
                      <div className="position-relative">
                        <input
                          type={showPasswords.current ? 'text' : 'password'}
                          className={`form-control pe-5 ${passwordForm.formState.errors.current_password ? 'is-invalid' : ''}`}
                          id="current_password"
                          {...passwordForm.register('current_password')}
                          disabled={isLoading}
                        />
                        <button
                          type="button"
                          className="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                          onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}
                          style={{ border: 'none', background: 'none' }}
                        >
                          {showPasswords.current ? (
                            <EyeSlashIcon width={20} height={20} />
                          ) : (
                            <EyeIcon width={20} height={20} />
                          )}
                        </button>
                      </div>
                      {passwordForm.formState.errors.current_password && (
                        <div className="invalid-feedback">
                          {passwordForm.formState.errors.current_password.message}
                        </div>
                      )}
                    </div>

                    <div className="mb-3">
                      <label htmlFor="new_password" className="form-label fw-semibold">
                        New Password
                      </label>
                      <div className="position-relative">
                        <input
                          type={showPasswords.new ? 'text' : 'password'}
                          className={`form-control pe-5 ${passwordForm.formState.errors.new_password ? 'is-invalid' : ''}`}
                          id="new_password"
                          {...passwordForm.register('new_password')}
                          disabled={isLoading}
                        />
                        <button
                          type="button"
                          className="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                          onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
                          style={{ border: 'none', background: 'none' }}
                        >
                          {showPasswords.new ? (
                            <EyeSlashIcon width={20} height={20} />
                          ) : (
                            <EyeIcon width={20} height={20} />
                          )}
                        </button>
                      </div>
                      {passwordForm.formState.errors.new_password && (
                        <div className="invalid-feedback">
                          {passwordForm.formState.errors.new_password.message}
                        </div>
                      )}
                    </div>

                    <div className="mb-4">
                      <label htmlFor="confirm_password" className="form-label fw-semibold">
                        Confirm New Password
                      </label>
                      <div className="position-relative">
                        <input
                          type={showPasswords.confirm ? 'text' : 'password'}
                          className={`form-control pe-5 ${passwordForm.formState.errors.confirm_password ? 'is-invalid' : ''}`}
                          id="confirm_password"
                          {...passwordForm.register('confirm_password')}
                          disabled={isLoading}
                        />
                        <button
                          type="button"
                          className="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                          onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
                          style={{ border: 'none', background: 'none' }}
                        >
                          {showPasswords.confirm ? (
                            <EyeSlashIcon width={20} height={20} />
                          ) : (
                            <EyeIcon width={20} height={20} />
                          )}
                        </button>
                      </div>
                      {passwordForm.formState.errors.confirm_password && (
                        <div className="invalid-feedback">
                          {passwordForm.formState.errors.confirm_password.message}
                        </div>
                      )}
                    </div>

                    <div className="d-flex justify-content-end">
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Changing...
                          </>
                        ) : (
                          'Change Password'
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
