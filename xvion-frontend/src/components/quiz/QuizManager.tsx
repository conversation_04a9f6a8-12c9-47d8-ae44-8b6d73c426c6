'use client'

import { useState, useEffect } from 'react'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  DocumentArrowUpIcon,
  DocumentArrowDownIcon,
  EyeIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
// Removed cn import as we're using Bootstrap classes
// Using inline type definitions for now

interface Quiz {
  id: string
  title: string
  description: string
  questionsCount: number
  language: string
  createdAt: string
  status: 'draft' | 'published'
}

export function QuizManager() {
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedQuiz, setSelectedQuiz] = useState<Quiz | null>(null)
  const [, setShowCreateModal] = useState(false)

  useEffect(() => {
    // Simulate loading quizzes
    setTimeout(() => {
      setQuizzes([
        {
          id: '1',
          title: 'Arabic Animals Quiz',
          description: 'Test your knowledge of animals in Arabic',
          questionsCount: 5,
          language: 'ar',
          createdAt: '2024-01-15',
          status: 'published'
        },
        {
          id: '2',
          title: 'Islamic History Quiz',
          description: 'Questions about Islamic history and culture',
          questionsCount: 8,
          language: 'ar',
          createdAt: '2024-01-10',
          status: 'draft'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const filteredQuizzes = quizzes.filter(quiz =>
    quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    quiz.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="animate-fade-in">
        <div className="row g-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="col-md-6 col-lg-4">
              <div className="card border-0 shadow-sm">
                <div className="card-body p-4">
                  <div className="skeleton mb-3" style={{ height: '20px', width: '75%' }}></div>
                  <div className="skeleton mb-2" style={{ height: '16px', width: '100%' }}></div>
                  <div className="skeleton" style={{ height: '16px', width: '50%' }}></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="animate-fade-in">
      {/* Header */}
      <div className="d-flex flex-column flex-sm-row align-items-start align-items-sm-center justify-content-between mb-4">
        <div className="mb-3 mb-sm-0">
          <h1 className="h2 fw-bold mb-2">Quiz Management</h1>
          <p className="text-muted mb-0">Create and manage your Arabic quiz content</p>
        </div>
        <div className="d-flex gap-2">
          <button className="btn btn-outline-secondary d-flex align-items-center gap-2">
            <DocumentArrowUpIcon style={{ width: '16px', height: '16px' }} />
            Import CSV
          </button>
          <button
            className="btn btn-primary d-flex align-items-center gap-2"
            onClick={() => {
              console.log('Create Quiz clicked');
              setShowCreateModal(true);
            }}
          >
            <PlusIcon style={{ width: '16px', height: '16px' }} />
            Create Quiz
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="relative">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clipRule="evenodd" />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search quizzes..."
            className="input pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Quiz List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            All Quizzes ({filteredQuizzes.length})
          </h2>
        </div>
        
        {filteredQuizzes.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No quizzes found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating a new quiz.'}
            </p>
            {!searchTerm && (
              <div className="mt-6">
                <button 
                  className="btn-primary"
                  onClick={() => setShowCreateModal(true)}
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create Quiz
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="card-body p-0">
            {filteredQuizzes.map((quiz, index) => (
              <div key={quiz.id} className={`p-4 hover-bg-light ${index < filteredQuizzes.length - 1 ? 'border-bottom' : ''}`}>
                <div className="d-flex align-items-center justify-content-between">
                  <div className="flex-fill">
                    <div className="d-flex align-items-center gap-3 mb-2">
                      <h5 className="mb-0 fw-semibold">{quiz.title}</h5>
                      <span className={`badge rounded-pill ${
                        quiz.language === 'ar' ? 'bg-success' : 'bg-primary'
                      }`}>
                        {quiz.language === 'ar' ? 'Arabic' : 'English'}
                      </span>
                    </div>
                    {quiz.description && (
                      <p className="text-muted mb-2">{quiz.description}</p>
                    )}
                    <div className="d-flex align-items-center gap-3 small text-muted">
                      <span>{quiz.questionsCount} questions</span>
                      <span>•</span>
                      <span>Updated {quiz.createdAt}</span>
                    </div>
                  </div>

                  <div className="d-flex align-items-center gap-2">
                    <button 
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                      onClick={() => setSelectedQuiz(quiz)}
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg">
                      <PencilIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg">
                      <DocumentArrowDownIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg">
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Quiz Preview Modal */}
      {selectedQuiz && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">{selectedQuiz.title}</h3>
              <button 
                className="text-gray-400 hover:text-gray-600"
                onClick={() => setSelectedQuiz(null)}
              >
                <span className="sr-only">Close</span>
                ×
              </button>
            </div>
            
            <div className="space-y-4">
              {selectedQuiz.description && (
                <p className="text-gray-600">{selectedQuiz.description}</p>
              )}
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Questions ({selectedQuiz.questionsCount})</h4>
                <div className="space-y-3">
                  <div className="border rounded-lg p-4">
                    <p className="text-gray-600">
                      This quiz contains {selectedQuiz.questionsCount} questions.
                      Use the edit function to view and modify individual questions.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
