'use client'

import { useState } from 'react'
import { 
  KeyIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'

export function Settings() {
  const [activeTab, setActiveTab] = useState('api-keys')
  const [showKeys, setShowKeys] = useState<{[key: string]: boolean}>({})

  const tabs = [
    { id: 'api-keys', name: 'API Keys', icon: KeyIcon },
    { id: 'accounts', name: 'Accounts', icon: UserCircleIcon },
    { id: 'general', name: 'General', icon: Cog6ToothIcon },
  ]

  const toggleKeyVisibility = (keyName: string) => {
    setShowKeys(prev => ({ ...prev, [keyName]: !prev[keyName] }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-1">Manage your application settings and API keys</p>
      </div>

      <div className="bg-white rounded-lg shadow">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'api-keys' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">API Keys</h3>
                <p className="text-sm text-gray-600 mb-6">
                  Configure your API keys for various services used by the video generator.
                </p>
              </div>

              <div className="space-y-4">
                {[
                  { name: 'DeepL Translation', key: 'deepl', placeholder: 'Enter DeepL API key' },
                  { name: 'ElevenLabs TTS', key: 'elevenlabs', placeholder: 'Enter ElevenLabs API key' },
                  { name: 'OpenAI', key: 'openai', placeholder: 'Enter OpenAI API key' },
                  { name: 'Unsplash', key: 'unsplash', placeholder: 'Enter Unsplash access key' },
                  { name: 'Pexels', key: 'pexels', placeholder: 'Enter Pexels API key' },
                  { name: 'Pixabay', key: 'pixabay', placeholder: 'Enter Pixabay API key' },
                ].map((apiKey) => (
                  <div key={apiKey.key} className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                    <label className="text-sm font-medium text-gray-700">
                      {apiKey.name}
                    </label>
                    <div className="relative">
                      <input
                        type={showKeys[apiKey.key] ? 'text' : 'password'}
                        placeholder={apiKey.placeholder}
                        className="input pr-10"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => toggleKeyVisibility(apiKey.key)}
                      >
                        {showKeys[apiKey.key] ? (
                          <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                        ) : (
                          <EyeIcon className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    <button className="btn-outline text-sm">
                      Test Connection
                    </button>
                  </div>
                ))}
              </div>

              <div className="pt-4 border-t">
                <button className="btn-primary">
                  Save API Keys
                </button>
              </div>
            </div>
          )}

          {activeTab === 'accounts' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Account Templates</h3>
                <p className="text-sm text-gray-600 mb-6">
                  Manage account templates for video generation.
                </p>
              </div>

              <div className="space-y-4">
                {[
                  { name: 'Account 1', displayName: '@AGL LA3BAH', colorMode: 'mode1' },
                  { name: 'Account 2', displayName: '@XVION_QUIZ', colorMode: 'mode2' },
                ].map((account, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Account Name
                        </label>
                        <input
                          type="text"
                          value={account.name}
                          className="input"
                          readOnly
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Display Name
                        </label>
                        <input
                          type="text"
                          value={account.displayName}
                          className="input"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Color Mode
                        </label>
                        <select className="input" value={account.colorMode}>
                          <option value="mode1">Mode 1</option>
                          <option value="mode2">Mode 2</option>
                          <option value="mode3">Mode 3</option>
                          <option value="mode4">Mode 4</option>
                        </select>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="pt-4 border-t">
                <div className="flex space-x-3">
                  <button className="btn-primary">
                    Save Changes
                  </button>
                  <button className="btn-outline">
                    Add Account
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">General Settings</h3>
                <p className="text-sm text-gray-600 mb-6">
                  Configure general application settings.
                </p>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Default Video Duration (seconds)
                    </label>
                    <input
                      type="number"
                      defaultValue="60"
                      className="input"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Default Question Count
                    </label>
                    <input
                      type="number"
                      defaultValue="6"
                      className="input"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Output Quality
                  </label>
                  <select className="input">
                    <option value="high">High (1080p)</option>
                    <option value="medium">Medium (720p)</option>
                    <option value="low">Low (480p)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Language
                  </label>
                  <select className="input">
                    <option value="en">English</option>
                    <option value="ar">Arabic</option>
                  </select>
                </div>
              </div>

              <div className="pt-4 border-t">
                <button className="btn-primary">
                  Save Settings
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
