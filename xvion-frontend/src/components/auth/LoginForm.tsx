'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { EyeIcon, EyeSlashIcon, XMarkIcon } from '@heroicons/react/24/outline'

// Validation schema
const loginSchema = z.object({
  emailOrUsername: z.string().min(1, 'Email or username is required'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional()
})

type LoginFormData = z.infer<typeof loginSchema>

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => Promise<void>
  onSwitchToRegister: () => void
  onForgotPassword: () => void
  onClose?: () => void
  isLoading?: boolean
  error?: string
}

export function LoginForm({
  onSubmit,
  onSwitchToRegister,
  onForgotPassword,
  onClose,
  isLoading = false,
  error
}: LoginFormProps) {
  const [showPassword, setShowPassword] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema)
  })

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      // Error handling is done by parent component
    }
  }

  return (
    <div className="card border-0 shadow-lg position-relative" style={{ width: '400px', maxWidth: '90vw', margin: '0 auto' }}>
      {/* Close Button */}
      {onClose && (
        <button
          type="button"
          className="btn-close position-absolute top-0 end-0 m-3"
          onClick={onClose}
          style={{ zIndex: 10 }}
          aria-label="Close"
        >
          <XMarkIcon width={20} height={20} />
        </button>
      )}

      <div className="card-body p-5">
        {/* Header */}
        <div className="text-center mb-4">
          <h2 className="h3 fw-bold text-primary mb-2">Welcome Back</h2>
          <p className="text-muted">Sign in to your Xvion account</p>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="alert alert-danger d-flex align-items-center mb-4" role="alert">
            <svg className="bi flex-shrink-0 me-2" width="24" height="24" role="img" aria-label="Danger:">
              <use xlinkHref="#exclamation-triangle-fill"/>
            </svg>
            <div>{error}</div>
          </div>
        )}

        {/* Login Form */}
        <form onSubmit={handleSubmit(handleFormSubmit)}>
          {/* Email or Username */}
          <div className="mb-3">
            <label htmlFor="emailOrUsername" className="form-label fw-semibold">
              Email or Username
            </label>
            <input
              type="text"
              className={`form-control ${errors.emailOrUsername ? 'is-invalid' : ''}`}
              id="emailOrUsername"
              placeholder="Enter your email or username"
              {...register('emailOrUsername')}
              disabled={isLoading || isSubmitting}
            />
            {errors.emailOrUsername && (
              <div className="invalid-feedback">
                {errors.emailOrUsername.message}
              </div>
            )}
          </div>

          {/* Password */}
          <div className="mb-3">
            <label htmlFor="password" className="form-label fw-semibold">
              Password
            </label>
            <div className="position-relative">
              <input
                type={showPassword ? 'text' : 'password'}
                className={`form-control pe-5 ${errors.password ? 'is-invalid' : ''}`}
                id="password"
                placeholder="Enter your password"
                {...register('password')}
                disabled={isLoading || isSubmitting}
              />
              <button
                type="button"
                className="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading || isSubmitting}
                style={{ border: 'none', background: 'none' }}
              >
                {showPassword ? (
                  <EyeSlashIcon className="w-5 h-5 text-muted" width={20} height={20} />
                ) : (
                  <EyeIcon className="w-5 h-5 text-muted" width={20} height={20} />
                )}
              </button>
            </div>
            {errors.password && (
              <div className="invalid-feedback">
                {errors.password.message}
              </div>
            )}
          </div>

          {/* Remember Me & Forgot Password */}
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div className="form-check">
              <input
                className="form-check-input"
                type="checkbox"
                id="rememberMe"
                {...register('rememberMe')}
                disabled={isLoading || isSubmitting}
              />
              <label className="form-check-label" htmlFor="rememberMe">
                Remember me
              </label>
            </div>
            <button
              type="button"
              className="btn btn-link p-0 text-decoration-none"
              onClick={onForgotPassword}
              disabled={isLoading || isSubmitting}
            >
              Forgot password?
            </button>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="btn btn-primary w-100 py-2 fw-semibold"
            disabled={isLoading || isSubmitting}
          >
            {isLoading || isSubmitting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Signing in...
              </>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        {/* Divider */}
        <div className="text-center my-4">
          <span className="text-muted">or</span>
        </div>

        {/* Switch to Register */}
        <div className="text-center">
          <p className="mb-0 text-muted">
            Don't have an account?{' '}
            <button
              type="button"
              className="btn btn-link p-0 text-decoration-none fw-semibold"
              onClick={onSwitchToRegister}
              disabled={isLoading || isSubmitting}
            >
              Sign up
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}

// SVG Icons for Bootstrap alerts
export function AlertIcons() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" style={{ display: 'none' }}>
      <symbol id="check-circle-fill" fill="currentColor" viewBox="0 0 16 16">
        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.061L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
      </symbol>
      <symbol id="info-fill" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
      </symbol>
      <symbol id="exclamation-triangle-fill" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
      </symbol>
    </svg>
  )
}
