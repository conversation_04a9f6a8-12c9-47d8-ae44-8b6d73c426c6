'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { EyeIcon, EyeSlashIcon, CheckCircleIcon, XCircleIcon, XMarkIcon } from '@heroicons/react/24/outline'

// Validation schema
const registerSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  username: z.string()
    .min(3, 'Username must be at least 3 characters long')
    .max(20, 'Username must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, hyphens, and underscores'),
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters long')
    .max(100, 'Full name must be less than 100 characters'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions')
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type RegisterFormData = z.infer<typeof registerSchema>

interface RegisterFormProps {
  onSubmit: (data: RegisterFormData) => Promise<void>
  onSwitchToLogin: () => void
  onClose?: () => void
  isLoading?: boolean
  error?: string
}

export function RegisterForm({
  onSubmit,
  onSwitchToLogin,
  onClose,
  isLoading = false,
  error
}: RegisterFormProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema)
  })

  const password = watch('password', '')

  // Password strength checker
  const getPasswordStrength = (password: string) => {
    let strength = 0
    if (password.length >= 8) strength++
    if (/[A-Z]/.test(password)) strength++
    if (/[a-z]/.test(password)) strength++
    if (/[0-9]/.test(password)) strength++
    if (/[^A-Za-z0-9]/.test(password)) strength++
    return strength
  }

  const passwordStrength = getPasswordStrength(password)
  const strengthLabels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong']
  const strengthColors = ['danger', 'danger', 'warning', 'info', 'success']

  const handleFormSubmit = async (data: RegisterFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      // Error handling is done by parent component
    }
  }

  return (
    <div className="card border-0 shadow-lg position-relative" style={{ width: '450px', maxWidth: '90vw', margin: '0 auto' }}>
      {/* Close Button */}
      {onClose && (
        <button
          type="button"
          className="btn-close position-absolute top-0 end-0 m-3"
          onClick={onClose}
          style={{ zIndex: 10 }}
          aria-label="Close"
        >
          <XMarkIcon width={20} height={20} />
        </button>
      )}

      <div className="card-body p-5">
        {/* Header */}
        <div className="text-center mb-4">
          <h2 className="h3 fw-bold text-primary mb-2">Create Account</h2>
          <p className="text-muted">Join Xvion to start creating amazing quiz videos</p>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="alert alert-danger d-flex align-items-center mb-4" role="alert">
            <XCircleIcon className="flex-shrink-0 me-2" width={24} height={24} />
            <div>{error}</div>
          </div>
        )}

        {/* Registration Form */}
        <form onSubmit={handleSubmit(handleFormSubmit)}>
          {/* Email */}
          <div className="mb-3">
            <label htmlFor="email" className="form-label fw-semibold">
              Email Address
            </label>
            <input
              type="email"
              className={`form-control ${errors.email ? 'is-invalid' : ''}`}
              id="email"
              placeholder="Enter your email address"
              {...register('email')}
              disabled={isLoading || isSubmitting}
            />
            {errors.email && (
              <div className="invalid-feedback">
                {errors.email.message}
              </div>
            )}
          </div>

          {/* Username */}
          <div className="mb-3">
            <label htmlFor="username" className="form-label fw-semibold">
              Username
            </label>
            <input
              type="text"
              className={`form-control ${errors.username ? 'is-invalid' : ''}`}
              id="username"
              placeholder="Choose a username"
              {...register('username')}
              disabled={isLoading || isSubmitting}
            />
            {errors.username && (
              <div className="invalid-feedback">
                {errors.username.message}
              </div>
            )}
            <div className="form-text">
              Username can only contain letters, numbers, hyphens, and underscores
            </div>
          </div>

          {/* Full Name */}
          <div className="mb-3">
            <label htmlFor="fullName" className="form-label fw-semibold">
              Full Name
            </label>
            <input
              type="text"
              className={`form-control ${errors.fullName ? 'is-invalid' : ''}`}
              id="fullName"
              placeholder="Enter your full name"
              {...register('fullName')}
              disabled={isLoading || isSubmitting}
            />
            {errors.fullName && (
              <div className="invalid-feedback">
                {errors.fullName.message}
              </div>
            )}
          </div>

          {/* Password */}
          <div className="mb-3">
            <label htmlFor="password" className="form-label fw-semibold">
              Password
            </label>
            <div className="position-relative">
              <input
                type={showPassword ? 'text' : 'password'}
                className={`form-control pe-5 ${errors.password ? 'is-invalid' : ''}`}
                id="password"
                placeholder="Create a strong password"
                {...register('password')}
                disabled={isLoading || isSubmitting}
              />
              <button
                type="button"
                className="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading || isSubmitting}
                style={{ border: 'none', background: 'none' }}
              >
                {showPassword ? (
                  <EyeSlashIcon className="w-5 h-5 text-muted" width={20} height={20} />
                ) : (
                  <EyeIcon className="w-5 h-5 text-muted" width={20} height={20} />
                )}
              </button>
            </div>
            {errors.password && (
              <div className="invalid-feedback">
                {errors.password.message}
              </div>
            )}
            
            {/* Password Strength Indicator */}
            {password && (
              <div className="mt-2">
                <div className="d-flex justify-content-between align-items-center mb-1">
                  <small className="text-muted">Password strength:</small>
                  <small className={`text-${strengthColors[passwordStrength - 1] || 'muted'}`}>
                    {strengthLabels[passwordStrength - 1] || 'Too weak'}
                  </small>
                </div>
                <div className="progress" style={{ height: '4px' }}>
                  <div
                    className={`progress-bar bg-${strengthColors[passwordStrength - 1] || 'secondary'}`}
                    role="progressbar"
                    style={{ width: `${(passwordStrength / 5) * 100}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className="mb-3">
            <label htmlFor="confirmPassword" className="form-label fw-semibold">
              Confirm Password
            </label>
            <div className="position-relative">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                className={`form-control pe-5 ${errors.confirmPassword ? 'is-invalid' : ''}`}
                id="confirmPassword"
                placeholder="Confirm your password"
                {...register('confirmPassword')}
                disabled={isLoading || isSubmitting}
              />
              <button
                type="button"
                className="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading || isSubmitting}
                style={{ border: 'none', background: 'none' }}
              >
                {showConfirmPassword ? (
                  <EyeSlashIcon className="w-5 h-5 text-muted" width={20} height={20} />
                ) : (
                  <EyeIcon className="w-5 h-5 text-muted" width={20} height={20} />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <div className="invalid-feedback">
                {errors.confirmPassword.message}
              </div>
            )}
          </div>

          {/* Terms and Conditions */}
          <div className="mb-4">
            <div className="form-check">
              <input
                className={`form-check-input ${errors.acceptTerms ? 'is-invalid' : ''}`}
                type="checkbox"
                id="acceptTerms"
                {...register('acceptTerms')}
                disabled={isLoading || isSubmitting}
              />
              <label className="form-check-label" htmlFor="acceptTerms">
                I agree to the{' '}
                <a href="/terms" className="text-decoration-none" target="_blank" rel="noopener noreferrer">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="/privacy" className="text-decoration-none" target="_blank" rel="noopener noreferrer">
                  Privacy Policy
                </a>
              </label>
              {errors.acceptTerms && (
                <div className="invalid-feedback">
                  {errors.acceptTerms.message}
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="btn btn-primary w-100 py-2 fw-semibold"
            disabled={isLoading || isSubmitting}
          >
            {isLoading || isSubmitting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Creating account...
              </>
            ) : (
              'Create Account'
            )}
          </button>
        </form>

        {/* Divider */}
        <div className="text-center my-4">
          <span className="text-muted">or</span>
        </div>

        {/* Switch to Login */}
        <div className="text-center">
          <p className="mb-0 text-muted">
            Already have an account?{' '}
            <button
              type="button"
              className="btn btn-link p-0 text-decoration-none fw-semibold"
              onClick={onSwitchToLogin}
              disabled={isLoading || isSubmitting}
            >
              Sign in
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
