'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { ArrowLeftIcon, EnvelopeIcon, XMarkIcon } from '@heroicons/react/24/outline'

// Validation schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address')
})

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

interface ForgotPasswordFormProps {
  onSubmit: (data: ForgotPasswordFormData) => Promise<void>
  onBackToLogin: () => void
  onClose?: () => void
  isLoading?: boolean
  error?: string
  success?: boolean
}

export function ForgotPasswordForm({
  onSubmit,
  onBackToLogin,
  onClose,
  isLoading = false,
  error,
  success = false
}: ForgotPasswordFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema)
  })

  const handleFormSubmit = async (data: ForgotPasswordFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      // Error handling is done by parent component
    }
  }

  if (success) {
    return (
      <div className="card border-0 shadow-lg position-relative" style={{ width: '400px', maxWidth: '90vw', margin: '0 auto' }}>
        {/* Close Button */}
        {onClose && (
          <button
            type="button"
            className="btn-close position-absolute top-0 end-0 m-3"
            onClick={onClose}
            style={{ zIndex: 10 }}
            aria-label="Close"
          >
            <XMarkIcon width={20} height={20} />
          </button>
        )}

        <div className="card-body p-5 text-center">
          {/* Success Icon */}
          <div className="mb-4">
            <div className="rounded-circle bg-success bg-opacity-10 d-inline-flex align-items-center justify-content-center" style={{ width: '80px', height: '80px' }}>
              <EnvelopeIcon className="text-success" width={40} height={40} />
            </div>
          </div>

          {/* Success Message */}
          <h2 className="h4 fw-bold text-success mb-3">Check Your Email</h2>
          <p className="text-muted mb-4">
            We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.
          </p>

          {/* Back to Login */}
          <button
            type="button"
            className="btn btn-outline-primary d-inline-flex align-items-center"
            onClick={onBackToLogin}
          >
            <ArrowLeftIcon className="me-2" width={16} height={16} />
            Back to Login
          </button>

          {/* Resend Instructions */}
          <div className="mt-4">
            <p className="small text-muted mb-2">Didn't receive the email?</p>
            <button
              type="button"
              className="btn btn-link p-0 text-decoration-none small"
              onClick={() => window.location.reload()}
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card border-0 shadow-lg position-relative" style={{ width: '400px', maxWidth: '90vw', margin: '0 auto' }}>
      {/* Close Button */}
      {onClose && (
        <button
          type="button"
          className="btn-close position-absolute top-0 end-0 m-3"
          onClick={onClose}
          style={{ zIndex: 10 }}
          aria-label="Close"
        >
          <XMarkIcon width={20} height={20} />
        </button>
      )}

      <div className="card-body p-5">
        {/* Header */}
        <div className="text-center mb-4">
          <div className="mb-3">
            <div className="rounded-circle bg-primary bg-opacity-10 d-inline-flex align-items-center justify-content-center" style={{ width: '60px', height: '60px' }}>
              <EnvelopeIcon className="text-primary" width={30} height={30} />
            </div>
          </div>
          <h2 className="h3 fw-bold text-primary mb-2">Forgot Password?</h2>
          <p className="text-muted">
            No worries! Enter your email address and we'll send you a link to reset your password.
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="alert alert-danger d-flex align-items-center mb-4" role="alert">
            <svg className="bi flex-shrink-0 me-2" width="24" height="24" role="img" aria-label="Danger:">
              <use xlinkHref="#exclamation-triangle-fill"/>
            </svg>
            <div>{error}</div>
          </div>
        )}

        {/* Forgot Password Form */}
        <form onSubmit={handleSubmit(handleFormSubmit)}>
          {/* Email */}
          <div className="mb-4">
            <label htmlFor="email" className="form-label fw-semibold">
              Email Address
            </label>
            <div className="position-relative">
              <input
                type="email"
                className={`form-control ps-5 ${errors.email ? 'is-invalid' : ''}`}
                id="email"
                placeholder="Enter your email address"
                {...register('email')}
                disabled={isLoading || isSubmitting}
              />
              <EnvelopeIcon 
                className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" 
                width={20} 
                height={20} 
              />
            </div>
            {errors.email && (
              <div className="invalid-feedback">
                {errors.email.message}
              </div>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="btn btn-primary w-100 py-2 fw-semibold mb-3"
            disabled={isLoading || isSubmitting}
          >
            {isLoading || isSubmitting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Sending reset link...
              </>
            ) : (
              'Send Reset Link'
            )}
          </button>

          {/* Back to Login */}
          <button
            type="button"
            className="btn btn-outline-secondary w-100 d-flex align-items-center justify-content-center"
            onClick={onBackToLogin}
            disabled={isLoading || isSubmitting}
          >
            <ArrowLeftIcon className="me-2" width={16} height={16} />
            Back to Login
          </button>
        </form>

        {/* Help Text */}
        <div className="text-center mt-4">
          <p className="small text-muted mb-0">
            Remember your password?{' '}
            <button
              type="button"
              className="btn btn-link p-0 text-decoration-none small fw-semibold"
              onClick={onBackToLogin}
              disabled={isLoading || isSubmitting}
            >
              Sign in
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}

// Reset Password Form (for when user clicks the reset link)
const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

interface ResetPasswordFormProps {
  token: string
  onSubmit: (data: ResetPasswordFormData) => Promise<void>
  isLoading?: boolean
  error?: string
  success?: boolean
}

export function ResetPasswordForm({ 
  token,
  onSubmit, 
  isLoading = false,
  error,
  success = false
}: ResetPasswordFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: { token }
  })

  const handleFormSubmit = async (data: ResetPasswordFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      // Error handling is done by parent component
    }
  }

  if (success) {
    return (
      <div className="card border-0 shadow-lg" style={{ maxWidth: '400px', margin: '0 auto' }}>
        <div className="card-body p-5 text-center">
          <div className="mb-4">
            <div className="rounded-circle bg-success bg-opacity-10 d-inline-flex align-items-center justify-content-center" style={{ width: '80px', height: '80px' }}>
              <svg className="text-success" width={40} height={40} fill="currentColor" viewBox="0 0 16 16">
                <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
              </svg>
            </div>
          </div>
          <h2 className="h4 fw-bold text-success mb-3">Password Reset Successful</h2>
          <p className="text-muted mb-4">
            Your password has been successfully reset. You can now sign in with your new password.
          </p>
          <a href="/login" className="btn btn-primary">
            Go to Login
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="card border-0 shadow-lg" style={{ maxWidth: '400px', margin: '0 auto' }}>
      <div className="card-body p-5">
        <div className="text-center mb-4">
          <h2 className="h3 fw-bold text-primary mb-2">Reset Password</h2>
          <p className="text-muted">Enter your new password below</p>
        </div>

        {error && (
          <div className="alert alert-danger mb-4" role="alert">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit(handleFormSubmit)}>
          <input type="hidden" {...register('token')} />
          
          <div className="mb-3">
            <label htmlFor="newPassword" className="form-label fw-semibold">
              New Password
            </label>
            <input
              type="password"
              className={`form-control ${errors.newPassword ? 'is-invalid' : ''}`}
              id="newPassword"
              placeholder="Enter your new password"
              {...register('newPassword')}
              disabled={isLoading || isSubmitting}
            />
            {errors.newPassword && (
              <div className="invalid-feedback">
                {errors.newPassword.message}
              </div>
            )}
          </div>

          <div className="mb-4">
            <label htmlFor="confirmPassword" className="form-label fw-semibold">
              Confirm New Password
            </label>
            <input
              type="password"
              className={`form-control ${errors.confirmPassword ? 'is-invalid' : ''}`}
              id="confirmPassword"
              placeholder="Confirm your new password"
              {...register('confirmPassword')}
              disabled={isLoading || isSubmitting}
            />
            {errors.confirmPassword && (
              <div className="invalid-feedback">
                {errors.confirmPassword.message}
              </div>
            )}
          </div>

          <button
            type="submit"
            className="btn btn-primary w-100 py-2 fw-semibold"
            disabled={isLoading || isSubmitting}
          >
            {isLoading || isSubmitting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Resetting password...
              </>
            ) : (
              'Reset Password'
            )}
          </button>
        </form>
      </div>
    </div>
  )
}
