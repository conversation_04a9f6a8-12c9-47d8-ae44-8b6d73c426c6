'use client'

import { useAuthModal } from '@/contexts/AuthModalContext'
import { AuthModal } from './AuthModal'

export function GlobalAuthModal() {
  const { isOpen, mode, hideModal } = useAuthModal()

  return (
    <AuthModal
      isOpen={isOpen}
      initialMode={mode}
      onClose={hideModal}
      onLoginSuccess={(user, token) => {
        hideModal()
        console.log('✅ Global Login successful:', user)
      }}
      onRegisterSuccess={(user) => {
        hideModal()
        console.log('✅ Global Registration successful:', user)
      }}
    />
  )
}
