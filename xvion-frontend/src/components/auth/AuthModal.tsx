'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { LoginForm } from './LoginForm'
import { RegisterForm } from './RegisterForm'
import { ForgotPasswordForm } from './ForgotPasswordForm'
import { useAuth } from '../../contexts/AuthContext'

// Global singleton to prevent multiple modals
let activeModalId: string | null = null

type AuthMode = 'login' | 'register' | 'forgot-password'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  initialMode?: AuthMode
  onLoginSuccess?: (user: any, token: string) => void
  onRegisterSuccess?: (user: any) => void
}

export function AuthModal({
  isOpen,
  onClose,
  initialMode = 'login',
  onLoginSuccess,
  onRegisterSuccess
}: AuthModalProps) {
  const { login } = useAuth()
  const [mode, setMode] = useState<AuthMode>(initialMode)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [forgotPasswordSuccess, setForgotPasswordSuccess] = useState(false)
  const [mounted, setMounted] = useState(false)

  // Generate unique ID for debugging
  const modalId = useState(() => Math.random().toString(36).substring(2, 11))[0]

  console.log(`🔍 AuthModal [${modalId}]: Rendered with isOpen=${isOpen}, mode=${mode}, initialMode=${initialMode}`)

  useEffect(() => {
    console.log(`🔧 AuthModal [${modalId}]: Setting mounted to true`)
    setMounted(true)
  }, [])

  // Manage global modal singleton
  useEffect(() => {
    if (isOpen) {
      if (activeModalId && activeModalId !== modalId) {
        console.log(`⚠️ AuthModal [${modalId}]: Another modal [${activeModalId}] is already active, not rendering`)
        return
      }
      activeModalId = modalId
      console.log(`🔓 AuthModal [${modalId}]: Became active modal`)
    } else {
      if (activeModalId === modalId) {
        activeModalId = null
        console.log(`🔒 AuthModal [${modalId}]: Released active modal`)
      }
    }
  }, [isOpen, modalId])

  // Sync internal mode with initialMode prop
  useEffect(() => {
    if (isOpen) {
      console.log('🔄 AuthModal: Syncing mode with initialMode:', initialMode)
      setMode(initialMode)
      setError(null)
      setIsLoading(false)
      setForgotPasswordSuccess(false)
    }
  }, [isOpen, initialMode])

  // Reset state when modal opens/closes
  const handleClose = () => {
    setMode(initialMode)
    setError(null)
    setIsLoading(false)
    setForgotPasswordSuccess(false)
    onClose()
  }

  // Handle login
  const handleLogin = async (data: any) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('http://localhost:8001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email_or_username: data.emailOrUsername,
          password: data.password,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.detail || 'Login failed')
      }

      console.log('🎉 Login successful, calling AuthContext login with:', {
        tokenPreview: result.access_token.substring(0, 20) + '...',
        user: result.user
      })

      // Use AuthContext login function to update state
      login(result.access_token, result.user)

      console.log('✅ AuthContext login called, checking localStorage...')
      console.log('📦 localStorage auth_token:', localStorage.getItem('auth_token')?.substring(0, 20) + '...')
      console.log('📦 localStorage user:', localStorage.getItem('user'))

      // Call success callback
      if (onLoginSuccess) {
        onLoginSuccess(result.user, result.access_token)
      }

      handleClose()
    } catch (err: any) {
      setError(err.message || 'An error occurred during login')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle registration
  const handleRegister = async (data: any) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('http://localhost:8001/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
          username: data.username,
          full_name: data.fullName,
          password: data.password,
          confirm_password: data.confirmPassword,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.detail || 'Registration failed')
      }

      // Call success callback
      if (onRegisterSuccess) {
        onRegisterSuccess(result)
      }

      // Switch to login mode with success message
      setMode('login')
      setError(null)
      // You might want to show a success message here
    } catch (err: any) {
      setError(err.message || 'An error occurred during registration')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle forgot password
  const handleForgotPassword = async (data: any) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('http://localhost:8001/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.detail || 'Failed to send reset email')
      }

      setForgotPasswordSuccess(true)
    } catch (err: any) {
      setError(err.message || 'An error occurred while sending reset email')
    } finally {
      setIsLoading(false)
    }
  }

  console.log(`🔍 AuthModal [${modalId}]: Render check - isOpen=${isOpen}, mounted=${mounted}, activeModalId=${activeModalId}`)

  if (!isOpen) {
    console.log(`🚫 AuthModal [${modalId}]: Modal is closed`)
    return null
  }

  // Skip mounted check for now to debug
  if (!mounted) {
    console.log(`⚠️ AuthModal [${modalId}]: Not mounted yet, but continuing anyway`)
  }

  // Check if this modal should render (singleton pattern)
  if (activeModalId !== modalId) {
    console.log(`🚫 AuthModal [${modalId}]: Not active modal (active: ${activeModalId})`)
    return null
  }

  console.log(`✅ AuthModal [${modalId}]: RENDERING MODAL with mode=${mode}`)

  console.log(`🎨 AuthModal [${modalId}]: Creating modal content`)

  const modalContent = (
    <>
      {/* Backdrop */}
      <div
        className="modal-backdrop fade show"
        onClick={handleClose}
        style={{
          zIndex: 99998,
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(0, 0, 0, 0.5)'
        }}
      ></div>

      {/* Modal Content Container */}
      <div
        style={{
          zIndex: 99999,
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px'
        }}
        onClick={handleClose}
      >
        <div onClick={(e) => e.stopPropagation()}>
          {(() => {
            console.log(`📋 AuthModal [${modalId}]: Rendering form for mode=${mode}`)

            if (mode === 'login') {
              return (
                <LoginForm
                  onSubmit={handleLogin}
                  onClose={handleClose}
                  onSwitchToRegister={() => {
                    console.log(`🔄 AuthModal [${modalId}]: Switching to register mode`)
                    setMode('register')
                    setError(null)
                  }}
                  onForgotPassword={() => {
                    console.log(`🔄 AuthModal [${modalId}]: Switching to forgot-password mode`)
                    setMode('forgot-password')
                    setError(null)
                    setForgotPasswordSuccess(false)
                  }}
                  isLoading={isLoading}
                  error={error || undefined}
                />
              )
            }

            if (mode === 'register') {
              return (
                <RegisterForm
                  onSubmit={handleRegister}
                  onClose={handleClose}
                  onSwitchToLogin={() => {
                    console.log(`🔄 AuthModal [${modalId}]: Switching to login mode`)
                    setMode('login')
                    setError(null)
                  }}
                  isLoading={isLoading}
                  error={error || undefined}
                />
              )
            }

            if (mode === 'forgot-password') {
              return (
                <ForgotPasswordForm
                  onSubmit={handleForgotPassword}
                  onClose={handleClose}
                  onBackToLogin={() => {
                    console.log(`🔄 AuthModal [${modalId}]: Switching back to login mode`)
                    setMode('login')
                    setError(null)
                    setForgotPasswordSuccess(false)
                  }}
                  isLoading={isLoading}
                  error={error || undefined}
                  success={forgotPasswordSuccess}
                />
              )
            }

            return null
          })()}
        </div>
      </div>
    </>
  )

  console.log(`🚀 AuthModal [${modalId}]: Creating portal to document.body`)
  return createPortal(modalContent, document.body)
}



