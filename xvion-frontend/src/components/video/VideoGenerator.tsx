'use client'

import { useState, useEffect } from 'react'
import { PlayIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { useAuth } from '../../contexts/AuthContext'
import { quizApi, videoApi } from '../../utils/apiClient'

interface Quiz {
  id: string
  title: string
  description: string
  questions: any[]
  created_at: string
}

export function VideoGenerator() {
  const { user, isAuthenticated } = useAuth()
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [selectedQuiz, setSelectedQuiz] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [generatedJobId, setGeneratedJobId] = useState<string | null>(null)

  // Load user's quizzes
  useEffect(() => {
    if (isAuthenticated) {
      loadQuizzes()
    }
  }, [isAuthenticated])

  const loadQuizzes = async () => {
    try {
      setIsLoading(true)
      const response = await quizApi.getQuizzes({ limit: 100 })
      // Handle both array response and paginated response
      const quizData = Array.isArray(response) ? response : (response.items || [])
      setQuizzes(quizData)
    } catch (error: any) {
      console.error('Failed to load quizzes:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to load quizzes' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleGenerateVideo = async () => {
    if (!selectedQuiz) {
      setMessage({ type: 'error', text: 'Please select a quiz first' })
      return
    }

    try {
      setIsGenerating(true)
      setMessage(null)

      const response = await videoApi.generateVideo({
        quiz_id: selectedQuiz
      })

      setGeneratedJobId(response.job_id)
      setMessage({ type: 'success', text: 'Video generation started! Check the Video Jobs page to monitor progress.' })
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to start video generation' })
    } finally {
      setIsGenerating(false)
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="container-fluid py-5">
        <div className="row justify-content-center">
          <div className="col-lg-6 text-center">
            <ExclamationTriangleIcon className="mx-auto mb-4 text-warning" width={64} height={64} />
            <h3 className="mb-3">Authentication Required</h3>
            <p className="text-muted mb-4">
              Please sign in to access the video generator and create amazing quiz videos.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container-fluid py-4">
      <div className="row justify-content-center">
        <div className="col-lg-10">
          {/* Header */}
          <div className="d-flex align-items-center mb-4">
            <PlayIcon className="me-3 text-primary" width={32} height={32} />
            <div>
              <h2 className="h3 fw-bold mb-1">Video Generator</h2>
              <p className="text-muted mb-0">Generate engaging Arabic quiz videos from your quizzes</p>
            </div>
          </div>

          {/* Message Alert */}
          {message && (
            <div className={`alert alert-${message.type === 'success' ? 'success' : 'danger'} d-flex align-items-center mb-4`} role="alert">
              {message.type === 'success' ? (
                <CheckCircleIcon className="flex-shrink-0 me-2" width={20} height={20} />
              ) : (
                <ExclamationTriangleIcon className="flex-shrink-0 me-2" width={20} height={20} />
              )}
              <div>{message.text}</div>
            </div>
          )}

          <div className="row">
            {/* Configuration Panel */}
            <div className="col-lg-6">
              <div className="card border-0 shadow-sm">
                <div className="card-body p-4">
                  <h5 className="card-title mb-4">Video Configuration</h5>

                  {/* Quiz Selection */}
                  <div className="mb-4">
                    <label htmlFor="quiz-select" className="form-label fw-semibold">
                      Select Quiz
                    </label>
                    {isLoading ? (
                      <div className="d-flex align-items-center">
                        <div className="spinner-border spinner-border-sm me-2" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                        <span className="text-muted">Loading quizzes...</span>
                      </div>
                    ) : (
                      <select
                        id="quiz-select"
                        className="form-select"
                        value={selectedQuiz}
                        onChange={(e) => setSelectedQuiz(e.target.value)}
                        disabled={isGenerating}
                      >
                        <option value="">Choose a quiz...</option>
                        {quizzes.map((quiz) => (
                          <option key={quiz.id} value={quiz.id}>
                            {quiz.title} ({quiz.questions?.length || 0} questions)
                          </option>
                        ))}
                      </select>
                    )}
                    {quizzes.length === 0 && !isLoading && (
                      <div className="form-text text-warning">
                        No quizzes found. Please create a quiz first in the Quiz Management section.
                      </div>
                    )}
                  </div>

                  {/* Generate Button */}
                  <div className="d-grid">
                    <button
                      className="btn btn-primary btn-lg"
                      onClick={handleGenerateVideo}
                      disabled={!selectedQuiz || isGenerating}
                    >
                      {isGenerating ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Generating Video...
                        </>
                      ) : (
                        <>
                          <PlayIcon className="me-2" width={20} height={20} />
                          Generate Video
                        </>
                      )}
                    </button>
                  </div>

                  {generatedJobId && (
                    <div className="mt-3">
                      <div className="alert alert-info">
                        <strong>Job ID:</strong> {generatedJobId}
                        <br />
                        <small>You can track the progress in the Video Jobs section.</small>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Preview/Info Panel */}
            <div className="col-lg-6">
              <div className="card border-0 shadow-sm">
                <div className="card-body p-4">
                  <h5 className="card-title mb-4">Selected Quiz Preview</h5>

                  {selectedQuiz ? (
                    (() => {
                      const quiz = quizzes.find(q => q.id === selectedQuiz)
                      return quiz ? (
                        <div>
                          <h6 className="fw-bold text-primary">{quiz.title}</h6>
                          <p className="text-muted mb-3">{quiz.description}</p>
                          <div className="row g-3">
                            <div className="col-6">
                              <div className="text-center p-3 bg-light rounded">
                                <div className="h4 fw-bold text-primary mb-1">{quiz.questions?.length || 0}</div>
                                <div className="small text-muted">Questions</div>
                              </div>
                            </div>
                            <div className="col-6">
                              <div className="text-center p-3 bg-light rounded">
                                <div className="h4 fw-bold text-success mb-1">~{Math.ceil((quiz.questions?.length || 0) * 0.5)}</div>
                                <div className="small text-muted">Minutes</div>
                              </div>
                            </div>
                          </div>
                          <div className="mt-3">
                            <small className="text-muted">
                              Created: {new Date(quiz.created_at).toLocaleDateString()}
                            </small>
                          </div>
                        </div>
                      ) : null
                    })()
                  ) : (
                    <div className="text-center py-5">
                      <PlayIcon className="mx-auto mb-3 text-muted" width={48} height={48} />
                      <p className="text-muted">Select a quiz to see the preview</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
