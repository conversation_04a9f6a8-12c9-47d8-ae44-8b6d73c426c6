'use client'

import { useState, useEffect } from 'react'
import {
  Clock<PERSON><PERSON>,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowDownTrayIcon,
  XMarkIcon,
  PlayIcon,
  DocumentTextIcon,
  CalendarIcon,
  ArrowPathIcon,
  FilmIcon,
  SparklesIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import {
  CheckCircleIcon as CheckCircleIconSolid,
  ClockI<PERSON> as ClockIconSolid,
  ExclamationTriangleIcon as ExclamationTriangleIconSolid
} from '@heroicons/react/24/solid'
import { videoApi } from '@/utils/apiClient'

interface VideoJob {
  id: string
  quizId: string
  quizTitle?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  startedAt?: string
  completedAt?: string
  outputPath?: string
  error?: string
  logs?: string[]
  config?: {
    accountName?: string
    displayName?: string
    colorMode?: string
  }
}

export function VideoJobs() {
  const [jobs, setJobs] = useState<VideoJob[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [downloadingJobs, setDownloadingJobs] = useState<Set<string>>(new Set())

  const loadJobs = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('🔄 Loading video jobs...')

      const response = await videoApi.getVideoJobs({ limit: 100 })
      console.log('📊 Video jobs response:', response)

      // The API returns a direct array of jobs
      const jobsData = Array.isArray(response) ? response : []
      console.log('📋 Jobs data:', jobsData)

      setJobs(jobsData)
    } catch (error: any) {
      console.error('❌ Error loading video jobs:', error)
      setError(error.message || 'Failed to load video jobs')
    } finally {
      setLoading(false)
    }
  }

  const downloadVideo = async (jobId: string, jobTitle?: string) => {
    try {
      setDownloadingJobs(prev => new Set(prev).add(jobId))

      const response = await fetch(`http://localhost:8001/api/videos/jobs/${jobId}/download`)

      if (!response.ok) {
        throw new Error('Failed to download video')
      }

      // Create blob and download
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${jobTitle || 'quiz-video'}-${jobId.slice(0, 8)}.mp4`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      console.log('✅ Video downloaded successfully')
    } catch (error: any) {
      console.error('❌ Error downloading video:', error)
      alert('Failed to download video: ' + error.message)
    } finally {
      setDownloadingJobs(prev => {
        const newSet = new Set(prev)
        newSet.delete(jobId)
        return newSet
      })
    }
  }

  const retryJob = async (jobId: string) => {
    try {
      const response = await fetch(`http://localhost:8001/api/videos/jobs/${jobId}/retry`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to retry job')
      }

      // Refresh the jobs list
      loadJobs()
      console.log('✅ Job retry initiated successfully')
    } catch (error: any) {
      console.error('❌ Error retrying job:', error)
      alert('Failed to retry job: ' + error.message)
    }
  }

  useEffect(() => {
    loadJobs()
  }, [])

  // Auto-refresh jobs every 5 seconds if there are active jobs
  useEffect(() => {
    const activeJobs = jobs.filter(job => job.status === 'pending' || job.status === 'processing')
    if (activeJobs.length > 0) {
      const interval = setInterval(loadJobs, 5000)
      return () => clearInterval(interval)
    }
  }, [jobs])

  const getStatusIcon = (status: VideoJob['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIconSolid className="text-success" style={{ width: '20px', height: '20px' }} />
      case 'processing':
        return <ArrowPathIcon className="text-primary" style={{ width: '20px', height: '20px', animation: 'spin 1s linear infinite' }} />
      case 'pending':
        return <ClockIconSolid className="text-warning" style={{ width: '20px', height: '20px' }} />
      case 'failed':
        return <ExclamationTriangleIconSolid className="text-danger" style={{ width: '20px', height: '20px' }} />
    }
  }

  const getStatusText = (status: VideoJob['status']) => {
    switch (status) {
      case 'completed':
        return 'Completed'
      case 'processing':
        return 'Processing'
      case 'pending':
        return 'Pending'
      case 'failed':
        return 'Failed'
    }
  }

  const getStatusColor = (status: VideoJob['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-success-subtle text-success border border-success'
      case 'processing':
        return 'bg-primary-subtle text-primary border border-primary'
      case 'pending':
        return 'bg-warning-subtle text-warning border border-warning'
      case 'failed':
        return 'bg-danger-subtle text-danger border border-danger'
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString()
  }

  const formatDuration = (startedAt?: string, completedAt?: string) => {
    if (!startedAt || !completedAt) return 'N/A'
    const start = new Date(startedAt)
    const end = new Date(completedAt)
    const duration = Math.round((end.getTime() - start.getTime()) / 1000)
    return `${duration}s`
  }

  if (loading) {
    return (
      <div className="container-fluid py-5">
        <div className="row g-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="col-lg-6">
              <div className="card border-0 shadow-sm">
                <div className="card-body p-4">
                  <div className="d-flex align-items-center mb-3">
                    <div className="spinner-border spinner-border-sm text-primary me-3" role="status"></div>
                    <div className="placeholder-glow flex-fill">
                      <span className="placeholder col-6"></span>
                    </div>
                  </div>
                  <div className="placeholder-glow">
                    <span className="placeholder col-4"></span>
                    <span className="placeholder col-8"></span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="container-fluid py-5 animate-fade-in">
      {/* Hero Header */}
      <div className="text-center mb-5">
        <div className="d-inline-flex align-items-center justify-content-center rounded-4 gradient-primary shadow-lg mb-4" style={{ width: '80px', height: '80px' }}>
          <FilmIcon className="text-white" style={{ width: '40px', height: '40px' }} />
        </div>
        <h1 className="display-4 fw-bold mb-3">
          <span className="gradient-primary text-white" style={{
            background: 'linear-gradient(135deg, #dc3545 0%, #e91e63 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Video Generation Studio
          </span>
        </h1>
        <p className="lead text-muted mb-4 mx-auto" style={{ maxWidth: '600px' }}>
          Monitor your AI-powered Arabic quiz video generation in real-time
        </p>

        {/* Action Buttons */}
        <div className="d-flex justify-content-center gap-3 mb-5">
          <button
            onClick={loadJobs}
            disabled={loading}
            className="btn btn-primary btn-lg d-flex align-items-center gap-2 hover-lift"
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                Refreshing...
              </>
            ) : (
              <>
                <ArrowPathIcon style={{ width: '20px', height: '20px' }} />
                Refresh Jobs
              </>
            )}
          </button>

          <button
            onClick={() => window.location.href = '/video-generator'}
            className="btn btn-outline-primary btn-lg d-flex align-items-center gap-2 hover-lift"
          >
            <SparklesIcon style={{ width: '20px', height: '20px' }} />
            Generate New Video
          </button>
        </div>
      </div>

      {/* Beautiful Stats Cards */}
      <div className="row g-4 mb-5">
        <div className="col-lg-3 col-md-6">
          <div className="card border-0 shadow-sm hover-lift glass">
            <div className="card-body p-4">
              <div className="d-flex align-items-center justify-content-between">
                <div className="d-flex align-items-center justify-content-center rounded-3 bg-primary shadow-sm" style={{ width: '48px', height: '48px' }}>
                  <DocumentTextIcon className="text-white" style={{ width: '24px', height: '24px' }} />
                </div>
                <div className="text-end">
                  <h3 className="display-6 fw-bold mb-0">{jobs.length}</h3>
                  <small className="text-muted fw-medium">Total Jobs</small>
                </div>
              </div>
              <div className="mt-3">
                <div className="progress" style={{ height: '4px' }}>
                  <div className="progress-bar bg-primary" style={{ width: '100%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-3 col-md-6">
          <div className="card border-0 shadow-sm hover-lift glass">
            <div className="card-body p-4">
              <div className="d-flex align-items-center justify-content-between">
                <div className="d-flex align-items-center justify-content-center rounded-3 bg-success shadow-sm" style={{ width: '48px', height: '48px' }}>
                  <CheckCircleIconSolid className="text-white" style={{ width: '24px', height: '24px' }} />
                </div>
                <div className="text-end">
                  <h3 className="display-6 fw-bold mb-0">
                    {jobs.filter(job => job.status === 'completed').length}
                  </h3>
                  <small className="text-muted fw-medium">Completed</small>
                </div>
              </div>
              <div className="mt-3">
                <div className="progress" style={{ height: '4px' }}>
                  <div className="progress-bar bg-success" style={{ width: '100%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-3 col-md-6">
          <div className="card border-0 shadow-sm hover-lift glass">
            <div className="card-body p-4">
              <div className="d-flex align-items-center justify-content-between">
                <div className="d-flex align-items-center justify-content-center rounded-3 bg-info shadow-sm" style={{ width: '48px', height: '48px' }}>
                  <ArrowPathIcon className="text-white" style={{ width: '24px', height: '24px', animation: 'spin 1s linear infinite' }} />
                </div>
                <div className="text-end">
                  <h3 className="display-6 fw-bold mb-0">
                    {jobs.filter(job => job.status === 'processing').length}
                  </h3>
                  <small className="text-muted fw-medium">Processing</small>
                </div>
              </div>
              <div className="mt-3">
                <div className="progress" style={{ height: '4px' }}>
                  <div className="progress-bar bg-info" style={{ width: '100%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-3 col-md-6">
          <div className="card border-0 shadow-sm hover-lift glass">
            <div className="card-body p-4">
              <div className="d-flex align-items-center justify-content-between">
                <div className="d-flex align-items-center justify-content-center rounded-3 bg-warning shadow-sm" style={{ width: '48px', height: '48px' }}>
                  <ClockIconSolid className="text-white" style={{ width: '24px', height: '24px' }} />
                </div>
                <div className="text-end">
                  <h3 className="display-6 fw-bold mb-0">
                    {jobs.filter(job => job.status === 'pending').length}
                  </h3>
                  <small className="text-muted fw-medium">Pending</small>
                </div>
              </div>
              <div className="mt-3">
                <div className="progress" style={{ height: '4px' }}>
                  <div className="progress-bar bg-warning" style={{ width: '100%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      {error ? (
        <div className="card border-0 shadow-sm">
          <div className="card-body text-center py-5">
            <ExclamationTriangleIcon className="text-danger mb-4" style={{ width: '64px', height: '64px' }} />
            <h3 className="h4 fw-bold mb-3">Error loading jobs</h3>
            <p className="text-muted mb-4">{error}</p>
            <button
              onClick={loadJobs}
              className="btn btn-primary d-flex align-items-center gap-2 mx-auto hover-lift"
            >
              <ArrowPathIcon style={{ width: '16px', height: '16px' }} />
              Try Again
            </button>
          </div>
        </div>
      ) : loading ? (
        <div className="card border-0 shadow-sm">
          <div className="card-body text-center py-5">
            <div className="spinner-border text-primary mb-4" style={{ width: '3rem', height: '3rem' }} role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <h3 className="h4 fw-bold mb-3">Loading jobs...</h3>
            <p className="text-muted">Please wait while we fetch your video generation jobs</p>
          </div>
        </div>
      ) : jobs.length === 0 ? (
        <div className="card border-0 shadow-sm">
          <div className="card-body text-center py-5">
            <PlayIcon className="text-muted mb-4" style={{ width: '64px', height: '64px' }} />
            <h3 className="h4 fw-bold mb-3">No jobs found</h3>
            <p className="text-muted mb-4">
              Start generating videos to see your jobs here.
            </p>
            <button
              onClick={() => window.location.href = '#'}
              className="btn btn-primary d-flex align-items-center gap-2 mx-auto hover-lift"
            >
              Generate Video
            </button>
          </div>
        </div>
      ) : (
        <div className="row g-4">
          {jobs.map((job) => (
            <div key={job.id} className="col-lg-6">
              <div className="card border-0 shadow-sm hover-lift">
                {/* Card Header */}
                <div className="card-header bg-transparent border-bottom">
                  <div className="d-flex align-items-start justify-content-between">
                    <div className="d-flex align-items-start gap-3">
                      {getStatusIcon(job.status)}
                      <div>
                        <h5 className="card-title mb-1 fw-bold">
                          {job.quizTitle || `Quiz Video`}
                        </h5>
                        <small className="text-muted">
                          Job ID: {job.id.slice(0, 8)}...
                        </small>
                      </div>
                    </div>
                    <span className={`badge rounded-pill ${getStatusColor(job.status)}`}>
                      {getStatusText(job.status)}
                    </span>
                  </div>
                </div>

                {/* Card Body */}
                <div className="card-body p-4">
                  {/* Progress Bar */}
                  {job.status === 'processing' && (
                    <div className="mb-4">
                      <div className="d-flex justify-content-between small text-muted mb-2">
                        <span>Progress</span>
                        <span>{job.progress}%</span>
                      </div>
                      <div className="progress" style={{ height: '8px' }}>
                        <div
                          className="progress-bar bg-primary progress-bar-striped progress-bar-animated"
                          style={{ width: `${job.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Job Details */}
                  <div className="row g-3 small">
                    <div className="col-6">
                      <p className="text-muted mb-1">Account</p>
                      <p className="fw-medium mb-0">
                        {job.config?.accountName || 'Default'}
                      </p>
                    </div>
                    <div className="col-6">
                      <p className="text-muted mb-1">Display Name</p>
                      <p className="fw-medium mb-0">
                        {job.config?.displayName || '@DEFAULT'}
                      </p>
                    </div>
                    <div className="col-6">
                      <p className="text-muted mb-1">Started At</p>
                      <p className="fw-medium mb-0 d-flex align-items-center">
                        <CalendarIcon className="text-muted me-1" style={{ width: '16px', height: '16px' }} />
                        {formatDate(job.startedAt)}
                      </p>
                    </div>
                    <div className="col-6">
                      <p className="text-muted mb-1">Duration</p>
                      <p className="fw-medium mb-0">
                        {formatDuration(job.startedAt, job.completedAt)}
                      </p>
                    </div>
                  </div>

                  {/* Error Message */}
                  {job.status === 'failed' && job.error && (
                    <div className="alert alert-danger d-flex align-items-center mt-3 mb-0" role="alert">
                      <ExclamationTriangleIcon className="me-2" style={{ width: '16px', height: '16px' }} />
                      <small>{job.error}</small>
                    </div>
                  )}
                </div>

                {/* Card Footer */}
                <div className="card-footer bg-light border-top">
                  <div className="d-flex align-items-center justify-content-between">
                    <small className="text-muted">
                      Quiz ID: {job.quizId.slice(0, 8)}...
                    </small>
                    <div className="d-flex align-items-center gap-2">
                      {job.status === 'completed' && (
                        <button
                          onClick={() => downloadVideo(job.id, job.quizTitle)}
                          disabled={downloadingJobs.has(job.id)}
                          className="btn btn-success btn-sm d-flex align-items-center gap-2 hover-lift"
                        >
                          {downloadingJobs.has(job.id) ? (
                            <>
                              <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                              Downloading...
                            </>
                          ) : (
                            <>
                              <ArrowDownTrayIcon style={{ width: '16px', height: '16px' }} />
                              Download
                            </>
                          )}
                        </button>
                      )}
                      {job.status === 'failed' && (
                        <button
                          onClick={() => retryJob(job.id)}
                          className="btn btn-warning btn-sm d-flex align-items-center gap-2 hover-lift"
                        >
                          <ArrowPathIcon style={{ width: '16px', height: '16px' }} />
                          Retry
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
