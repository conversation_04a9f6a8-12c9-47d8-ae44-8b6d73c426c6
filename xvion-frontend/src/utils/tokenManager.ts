// Token management utilities

interface TokenData {
  access_token: string
  token_type: string
  expires_in: number
  refresh_token?: string
}

interface DecodedToken {
  sub: string // user id
  email: string
  username: string
  role: string
  exp: number // expiration timestamp
  iat: number // issued at timestamp
}

class TokenManager {
  private static instance: TokenManager
  private refreshTimer: NodeJS.Timeout | null = null

  private constructor() {}

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager()
    }
    return TokenManager.instance
  }

  // Store token data
  setTokenData(tokenData: TokenData): void {
    localStorage.setItem('auth_token', tokenData.access_token)
    localStorage.setItem('token_type', tokenData.token_type)
    
    if (tokenData.refresh_token) {
      localStorage.setItem('refresh_token', tokenData.refresh_token)
    }

    // Calculate expiration time
    const expirationTime = Date.now() + (tokenData.expires_in * 1000)
    localStorage.setItem('token_expires_at', expirationTime.toString())

    // Set up automatic refresh
    this.scheduleTokenRefresh(tokenData.expires_in)
  }

  // Get current access token
  getAccessToken(): string | null {
    return localStorage.getItem('auth_token')
  }

  // Get refresh token
  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token')
  }

  // Check if token exists
  hasToken(): boolean {
    return !!this.getAccessToken()
  }

  // Check if token is expired
  isTokenExpired(): boolean {
    const expirationTime = localStorage.getItem('token_expires_at')
    if (!expirationTime) return true

    const now = Date.now()
    const expTime = parseInt(expirationTime, 10)
    
    // Consider token expired if it expires in the next 5 minutes
    return now >= (expTime - 5 * 60 * 1000)
  }

  // Decode JWT token (client-side only for reading claims, not for validation)
  decodeToken(token?: string): DecodedToken | null {
    const tokenToUse = token || this.getAccessToken()
    if (!tokenToUse) return null

    try {
      const base64Url = tokenToUse.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      )

      return JSON.parse(jsonPayload) as DecodedToken
    } catch (error) {
      console.error('Error decoding token:', error)
      return null
    }
  }

  // Get user info from token
  getUserFromToken(): DecodedToken | null {
    return this.decodeToken()
  }

  // Refresh access token
  async refreshAccessToken(): Promise<boolean> {
    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      this.clearTokens()
      return false
    }

    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: refreshToken,
        }),
      })

      if (response.ok) {
        const tokenData: TokenData = await response.json()
        this.setTokenData(tokenData)
        return true
      } else {
        // Refresh token is invalid, clear all tokens
        this.clearTokens()
        return false
      }
    } catch (error) {
      console.error('Error refreshing token:', error)
      this.clearTokens()
      return false
    }
  }

  // Schedule automatic token refresh
  private scheduleTokenRefresh(expiresIn: number): void {
    // Clear existing timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
    }

    // Schedule refresh 5 minutes before expiration
    const refreshTime = (expiresIn - 5 * 60) * 1000
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(async () => {
        const success = await this.refreshAccessToken()
        if (!success) {
          // Redirect to login if refresh fails
          window.location.href = '/login'
        }
      }, refreshTime)
    }
  }

  // Clear all tokens
  clearTokens(): void {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('token_type')
    localStorage.removeItem('token_expires_at')
    localStorage.removeItem('user')

    // Clear refresh timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  // Get authorization header
  getAuthHeader(): { Authorization: string } | {} {
    const token = this.getAccessToken()
    const tokenType = localStorage.getItem('token_type') || 'Bearer'
    
    if (token) {
      return { Authorization: `${tokenType} ${token}` }
    }
    
    return {}
  }

  // Initialize token manager (call this on app startup)
  initialize(): void {
    // Check if token exists and is valid
    if (this.hasToken() && !this.isTokenExpired()) {
      // Set up refresh timer for existing token
      const expirationTime = localStorage.getItem('token_expires_at')
      if (expirationTime) {
        const expTime = parseInt(expirationTime, 10)
        const now = Date.now()
        const remainingTime = Math.max(0, expTime - now)
        const expiresInSeconds = Math.floor(remainingTime / 1000)
        
        if (expiresInSeconds > 0) {
          this.scheduleTokenRefresh(expiresInSeconds)
        }
      }
    } else if (this.hasToken()) {
      // Token exists but is expired, try to refresh
      this.refreshAccessToken()
    }
  }

  // Check if user has specific role
  hasRole(role: string): boolean {
    const tokenData = this.getUserFromToken()
    return tokenData?.role === role
  }

  // Check if user has any of the specified roles
  hasAnyRole(roles: string[]): boolean {
    const tokenData = this.getUserFromToken()
    return tokenData ? roles.includes(tokenData.role) : false
  }

  // Get time until token expires (in seconds)
  getTimeUntilExpiration(): number {
    const expirationTime = localStorage.getItem('token_expires_at')
    if (!expirationTime) return 0

    const now = Date.now()
    const expTime = parseInt(expirationTime, 10)
    
    return Math.max(0, Math.floor((expTime - now) / 1000))
  }

  // Check if token will expire soon (within specified minutes)
  willExpireSoon(minutes: number = 5): boolean {
    const timeUntilExpiration = this.getTimeUntilExpiration()
    return timeUntilExpiration <= (minutes * 60)
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance()

// Utility functions
export const getAuthHeaders = () => tokenManager.getAuthHeader()

export const isAuthenticated = () => tokenManager.hasToken() && !tokenManager.isTokenExpired()

export const getUserFromToken = () => tokenManager.getUserFromToken()

export const hasRole = (role: string) => tokenManager.hasRole(role)

export const hasAnyRole = (roles: string[]) => tokenManager.hasAnyRole(roles)

// Initialize token manager
if (typeof window !== 'undefined') {
  tokenManager.initialize()
}

export default tokenManager
