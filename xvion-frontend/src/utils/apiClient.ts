// Remove tokenManager import - we'll use localStorage directly for consistency with AuthContext

// API Response types
interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
  detail?: string
}

interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// API Client class
class ApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>

  constructor(baseURL: string = 'http://localhost:8001/api') {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  // Private method to make requests
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    // Get auth token from localStorage (consistent with AuthContext)
    const authToken = localStorage.getItem('auth_token')

    // Merge headers
    const headers = {
      ...this.defaultHeaders,
      ...(authToken ? { 'Authorization': `Bearer ${authToken}` } : {}),
      ...options.headers,
    }

    const config: RequestInit = {
      ...options,
      headers,
    }

    try {
      const response = await fetch(url, config)
      
      // Handle different response types
      let data: any
      const contentType = response.headers.get('content-type')
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.text()
      }

      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401) {
          // Clear auth data (consistent with AuthContext)
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user')
          console.log('🚪 API Client: 401 error, cleared auth data')
          throw new ApiError('Authentication required', response.status, data)
        }

        // Extract error message
        const errorMessage = data?.detail || data?.message || data?.error || `HTTP ${response.status}`
        throw new ApiError(errorMessage, response.status, data)
      }

      return data
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        0,
        null
      )
    }
  }

  // GET request
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    let url = endpoint
    
    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }

    return this.request<T>(url, { method: 'GET' })
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // Upload file
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    // Don't set Content-Type header for FormData, let browser set it
    const headers = {
      ...tokenManager.getAuthHeader(),
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      headers,
      body: formData,
    })
  }
}

// Create API client instance
export const apiClient = new ApiClient()

// Auth API methods
export const authApi = {
  login: (credentials: { email_or_username: string; password: string }) =>
    apiClient.post<{ access_token: string; token_type: string; user: any }>('/auth/login', credentials),

  register: (userData: {
    email: string
    username: string
    full_name: string
    password: string
    confirm_password: string
  }) => apiClient.post<{ message: string; user: any }>('/auth/register', userData),

  logout: () => apiClient.post('/auth/logout'),

  refreshToken: (refreshToken: string) =>
    apiClient.post<{ access_token: string; token_type: string }>('/auth/refresh', {
      refresh_token: refreshToken,
    }),

  getCurrentUser: () => apiClient.get<any>('/auth/me'),

  updateProfile: (data: any) => apiClient.patch<any>('/auth/profile', data),

  changePassword: (data: { current_password: string; new_password: string }) =>
    apiClient.patch('/auth/change-password', data),

  requestPasswordReset: (email: string) =>
    apiClient.post('/auth/request-password-reset', { email }),

  resetPassword: (data: { token: string; new_password: string }) =>
    apiClient.post('/auth/reset-password', data),

  verifyEmail: (token: string) => apiClient.post('/auth/verify-email', { token }),

  resendVerification: () => apiClient.post('/auth/resend-verification'),
}

// Quiz API methods
export const quizApi = {
  getQuizzes: (params?: { skip?: number; limit?: number }) =>
    apiClient.get<PaginatedResponse<any>>('/quizzes', params),

  getQuiz: (id: string) => apiClient.get<any>(`/quizzes/${id}`),

  createQuiz: (data: any) => apiClient.post<any>('/quizzes', data),

  updateQuiz: (id: string, data: any) => apiClient.put<any>(`/quizzes/${id}`, data),

  deleteQuiz: (id: string) => apiClient.delete(`/quizzes/${id}`),

  generateQuiz: (data: any) => apiClient.post<any>('/quizzes/generate', data),
}

// Video API methods
export const videoApi = {
  generateVideo: (data: { quiz_id: string; [key: string]: any }) =>
    apiClient.post<{ job_id: string; message: string }>('/videos/generate', data),

  getVideoJobs: (params?: { skip?: number; limit?: number }) =>
    apiClient.get<any[]>('/jobs', params),

  getVideoJob: (jobId: string) => apiClient.get<any>(`/jobs/${jobId}`),

  downloadVideo: (jobId: string) => {
    const authToken = localStorage.getItem('auth_token')
    const url = `http://localhost:8001/api/videos/jobs/${jobId}/download`

    return fetch(url, {
      headers: authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
    })
  }
}

// Settings API methods
export const settingsApi = {
  getSettings: () => apiClient.get<any>('/settings'),

  updateSettings: (data: any) => apiClient.patch<any>('/settings', data),
}

// Export types
export type { ApiResponse, PaginatedResponse }
