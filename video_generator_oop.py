"""
Object-Oriented Video Generator V2.0.0
Refactored from the original Video Generator V2.0.0.py for API integration
"""

import os
import subprocess
import json
import shutil
import random
import string
import cv2
import csv
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import requests
import time
import urllib.parse
import hashlib
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import tempfile
import arabic_reshaper
import bidi.algorithm
from bidi.algorithm import get_display


class VideoGeneratorConfig:
    """Configuration class for video generation"""
    
    def __init__(self):
        # Default configuration
        self.QUESTION_DURATION = 11  # Each question gets 11 seconds
        self.ANSWER_DISPLAY_TIME = 2.5  # Last 3 seconds show answer
        self.LAST_ANSWER_TIME = 8.5  # Answer appears at 8 seconds
        self.GREEN_REVEAL_TIME = 8.0  # Show green at 8 seconds
        
        # Text properties
        self.text_properties = {
            "question": {"fontsize": 48, "color": "white"},
            "option": {"fontsize": 36, "color": "white"},
            "answer": {"fontsize": 32, "color": "lime"}
        }
        
        # Arabic support
        self.ARABIC_SUPPORT = True


class ArabicTextProcessor:
    """Handles Arabic text processing and font loading"""
    
    @staticmethod
    def load_arabic_font(font_size, fallback_size=None):
        """Load Arabic font with proper fallback handling"""
        if fallback_size is None:
            fallback_size = font_size

        # List of Arabic font paths to try
        arabic_fonts = [
            "./assets/Fonts/NotoSansArabic-Bold.ttf",
            "./assets/Fonts/arial-unicode-ms.ttf",
            "./assets/Fonts/DejaVuSans-Bold.ttf",
            "NotoSansArabic-Bold.ttf",
            "arial-unicode.ttf",
            "DejaVuSans.ttf"
        ]

        for font_path in arabic_fonts:
            try:
                font = ImageFont.truetype(font_path, font_size)
                # Test if font can handle Arabic characters
                test_img = Image.new('RGB', (100, 100), (255, 255, 255))
                test_draw = ImageDraw.Draw(test_img)
                test_draw.textbbox((0, 0), 'أ', font=font)  # Test with Arabic letter
                print(f"✅ Successfully loaded Arabic font: {font_path}")
                return font
            except Exception as e:
                print(f"❌ Failed to load font {font_path}: {e}")
                continue

        print("⚠️ No Arabic fonts found, using default with smaller size")
        try:
            return ImageFont.load_default()
        except:
            return None

    @staticmethod
    def process_arabic_text(text):
        """Process Arabic text for proper display with reshaping and bidirectional support"""
        try:
            # Check if text contains Arabic characters
            if any('\u0600' <= char <= '\u06FF' or '\u0750' <= char <= '\u077F' for char in text):
                # Reshape Arabic text (connect letters properly)
                reshaped_text = arabic_reshaper.reshape(text)
                # Handle bidirectional text (RTL)
                display_text = get_display(reshaped_text)
                return display_text
            else:
                return text
        except Exception as e:
            print(f"⚠️ Arabic text processing failed: {e}")
            return text


class BackgroundVideoProcessor:
    """Handles background video processing"""
    
    @staticmethod
    def get_random_background_video(background_folder):
        """Get a random background video from the folder"""
        background_files = [f for f in os.listdir(background_folder) 
                          if f.startswith('A (') and f.endswith('.mp4')]
        if not background_files:
            raise Exception(f"No background videos found in {background_folder}")
        
        return os.path.join(background_folder, random.choice(background_files))
    
    @staticmethod
    def create_stable_background_video(input_video_path, output_path, duration=66):
        """Create a stable background video like the original script"""
        command = [
            'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
            '-stream_loop', '-1',  # Loop the video
            '-i', input_video_path,
            '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1',
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-t', str(duration),  # Duration in seconds
            '-r', '30',  # 30 FPS
            output_path
        ]
        
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"Failed to create stable background: {result.stderr}")
        
        print(f"   ✅ Created stable background ({duration}s): {output_path}")
        return output_path


class QuizDataProcessor:
    """Handles quiz data loading and processing"""
    
    @staticmethod
    def load_questions_from_csv(csv_file_path, num_questions=6):
        """Load questions from CSV file like the original script"""
        questions = []
        correct_answers = []
        answer_options = []
        
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.reader(file)
            rows = [tuple(row) for row in csv_reader if row and any(cell.strip() for cell in row)]
            
            # Skip header if present
            if rows and rows[0][0].lower().startswith("question"):
                rows = rows[1:]
            
            # Randomly select questions
            if len(rows) < num_questions:
                raise ValueError(f"Not enough questions in CSV. Need {num_questions}, have {len(rows)}")
            
            selected_rows = random.sample(rows, num_questions)
            
            for row in selected_rows:
                if len(row) >= 5:
                    questions.append(row[0])  # Question text
                    answer_options.append([row[1], row[2], row[3]])  # Options A, B, C
                    correct_answers.append(row[4])  # Correct answer
        
        return questions, correct_answers, answer_options
    
    @staticmethod
    def create_csv_from_quiz_data(questions, correct_answers, answer_options, output_path):
        """Create a CSV file from quiz data"""
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            # Write header
            writer.writerow(['Question', 'Option A', 'Option B', 'Option C', 'Correct Answer'])
            
            # Write quiz data
            for i, question in enumerate(questions):
                if i < len(correct_answers) and i < len(answer_options):
                    options = answer_options[i] if isinstance(answer_options[i], list) else ['A) Option 1', 'B) Option 2', 'C) Option 3']
                    # Ensure we have 3 options
                    while len(options) < 3:
                        options.append(f"Option {len(options) + 1}")
                    
                    writer.writerow([
                        question,
                        options[0] if len(options) > 0 else "Option A",
                        options[1] if len(options) > 1 else "Option B", 
                        options[2] if len(options) > 2 else "Option C",
                        correct_answers[i] if i < len(correct_answers) else options[0]
                    ])
        
        print(f"   ✅ Created CSV file: {output_path}")
        return output_path


class AccountConfig:
    """Account configuration class"""
    
    def __init__(self, account_name, display_name, background_folder, font_type, 
                 color_mode="mode1", output_folder="./output", 
                 photo_x=360, photo_y=500, photo_width=1200, photo_height=400,
                 text_box_path="./assets/Text Boxes/1.png"):
        self.account_name = account_name
        self.display_name = display_name
        self.background_folder = background_folder
        self.font_type = font_type
        self.color_mode = color_mode
        self.output_folder = output_folder
        self.photo_x = photo_x
        self.photo_y = photo_y
        self.photo_width = photo_width
        self.photo_height = photo_height
        self.text_box_path = text_box_path
    
    def to_dict(self):
        """Convert to dictionary format"""
        return {
            "name": self.account_name,
            "display_name": self.display_name,
            "background_folder": self.background_folder,
            "font_type": self.font_type,
            "color_mode": self.color_mode,
            "output_folder": self.output_folder,
            "photo_x": self.photo_x,
            "photo_y": self.photo_y,
            "photo_width": self.photo_width,
            "photo_height": self.photo_height,
            "text_box": self.text_box_path
        }


class VideoGenerator:
    """Main Video Generator class - OOP version of Video Generator V2.0.0.py"""
    
    def __init__(self, config=None):
        self.config = config or VideoGeneratorConfig()
        self.arabic_processor = ArabicTextProcessor()
        self.background_processor = BackgroundVideoProcessor()
        self.quiz_processor = QuizDataProcessor()
        
    def generate_random_filename(self):
        """Generate a random filename for the video"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=8)) + '.mp4'

    def create_account_name_text_box(self, display_name, account_index, temp_dir):
        """Create account name text box like the original script"""
        try:
            # Create a gradient text box image for the account name
            width, height = 800, 100
            img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Load Arabic font
            font = self.arabic_processor.load_arabic_font(36)
            if not font:
                font = ImageFont.load_default()

            # Process Arabic text
            processed_name = self.arabic_processor.process_arabic_text(display_name)

            # Create gradient background
            for y in range(height):
                alpha = int(255 * (1 - y / height * 0.5))  # Gradient effect
                draw.rectangle([0, y, width, y+1], fill=(0, 0, 0, alpha))

            # Add border
            draw.rectangle([5, 5, width-5, height-5], outline=(255, 255, 255, 255), width=2)

            # Draw text
            text_bbox = draw.textbbox((0, 0), processed_name, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            text_x = (width - text_width) // 2
            text_y = (height - text_height) // 2

            # Add shadow effect
            draw.text((text_x + 2, text_y + 2), processed_name, font=font, fill=(0, 0, 0, 128))
            # Main text
            draw.text((text_x, text_y), processed_name, font=font, fill=(255, 255, 255, 255))

            # Save the image
            output_path = os.path.join(temp_dir, f"account_name_box_{account_index}.png")
            img.save(output_path)
            print(f"   ✅ Created account name text box: {output_path}")
            return output_path

        except Exception as e:
            print(f"   ❌ Failed to create account name text box: {e}")
            return None

    def create_question_overlay(self, question_text, temp_dir, question_index):
        """Create question text overlay"""
        try:
            # Process Arabic text
            processed_question = self.arabic_processor.process_arabic_text(question_text)

            # Create question overlay image
            width, height = 1400, 300
            img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Load font
            font = self.arabic_processor.load_arabic_font(self.config.text_properties["question"]["fontsize"])
            if not font:
                font = ImageFont.load_default()

            # Create background box
            draw.rectangle([20, 20, width-20, height-20], fill=(0, 0, 0, 200), outline=(255, 255, 0, 255), width=4)

            # Handle text wrapping for long questions
            words = processed_question.split()
            lines = []
            current_line = []

            for word in words:
                test_line = ' '.join(current_line + [word])
                text_bbox = draw.textbbox((0, 0), test_line, font=font)
                if text_bbox[2] - text_bbox[0] <= width - 80:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        lines.append(word)

            if current_line:
                lines.append(' '.join(current_line))

            # Draw lines
            line_height = 60
            start_y = (height - len(lines) * line_height) // 2

            for i, line in enumerate(lines):
                text_bbox = draw.textbbox((0, 0), line, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_x = (width - text_width) // 2
                text_y = start_y + i * line_height

                # Shadow
                draw.text((text_x + 2, text_y + 2), line, font=font, fill=(0, 0, 0, 128))
                # Main text
                draw.text((text_x, text_y), line, font=font, fill=(255, 255, 255, 255))

            # Save the image
            output_path = os.path.join(temp_dir, f"question_overlay_{question_index}.png")
            img.save(output_path)
            print(f"   ✅ Created question overlay: {output_path}")
            return output_path

        except Exception as e:
            print(f"   ❌ Failed to create question overlay: {e}")
            return None

    def create_answer_overlay(self, answer_text, temp_dir, question_index):
        """Create answer text overlay"""
        try:
            # Process Arabic text
            processed_answer = self.arabic_processor.process_arabic_text(f"الإجابة: {answer_text}")

            # Create answer overlay image
            width, height = 1000, 150
            img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Load font
            font = self.arabic_processor.load_arabic_font(self.config.text_properties["answer"]["fontsize"])
            if not font:
                font = ImageFont.load_default()

            # Create green background box
            draw.rectangle([15, 15, width-15, height-15], fill=(0, 128, 0, 220), outline=(0, 255, 0, 255), width=3)

            # Draw text
            text_bbox = draw.textbbox((0, 0), processed_answer, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            text_x = (width - text_width) // 2
            text_y = (height - text_height) // 2

            # Shadow
            draw.text((text_x + 2, text_y + 2), processed_answer, font=font, fill=(0, 0, 0, 128))
            # Main text
            draw.text((text_x, text_y), processed_answer, font=font, fill=(255, 255, 255, 255))

            # Save the image
            output_path = os.path.join(temp_dir, f"answer_overlay_{question_index}.png")
            img.save(output_path)
            print(f"   ✅ Created answer overlay: {output_path}")
            return output_path

        except Exception as e:
            print(f"   ❌ Failed to create answer overlay: {e}")
            return None

    def create_final_video_with_overlays(self, background_video_path, questions, correct_answers,
                                       answer_options, account_config, output_path, num_questions=6):
        """Create the final video with all overlays like the original script"""

        print(f"🎬 Creating final video with {num_questions} questions...")

        # Create temporary directory for processing
        temp_dir = tempfile.mkdtemp(prefix="video_gen_")

        try:
            # Step 1: Create stable background video
            stable_bg_path = os.path.join(temp_dir, "background_stable.mp4")
            total_duration = num_questions * self.config.QUESTION_DURATION
            self.background_processor.create_stable_background_video(
                background_video_path, stable_bg_path, duration=total_duration
            )

            # Step 2: Create account name text box
            account_text_box_path = self.create_account_name_text_box(
                account_config.display_name, 1, temp_dir
            )

            # Step 3: Create overlays for each question
            question_overlays = []
            answer_overlays = []

            # Ensure we have enough questions - repeat if necessary
            extended_questions = questions * ((num_questions // len(questions)) + 1) if questions else []
            extended_answers = correct_answers * ((num_questions // len(correct_answers)) + 1) if correct_answers else []

            for i in range(num_questions):
                question_idx = i % len(questions) if questions else 0
                answer_idx = i % len(correct_answers) if correct_answers else 0

                # Create question overlay
                question_text = extended_questions[i] if i < len(extended_questions) else f"Question {i+1}"
                question_overlay = self.create_question_overlay(question_text, temp_dir, i)
                if question_overlay:
                    question_overlays.append(question_overlay)

                # Create answer overlay
                answer_text = extended_answers[i] if i < len(extended_answers) else f"Answer {i+1}"
                answer_overlay = self.create_answer_overlay(answer_text, temp_dir, i)
                if answer_overlay:
                    answer_overlays.append(answer_overlay)

            print(f"   Created {len(question_overlays)} question overlays and {len(answer_overlays)} answer overlays")

            # Step 4: Compose final video with FFmpeg
            return self.compose_final_video_with_ffmpeg(
                stable_bg_path, account_text_box_path, question_overlays,
                answer_overlays, questions, output_path, num_questions
            )

        finally:
            # Clean up temporary directory
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    def compose_final_video_with_ffmpeg(self, stable_bg_path, account_text_box_path,
                                      question_overlays, answer_overlays, questions,
                                      output_path, num_questions):
        """Compose the final video using FFmpeg with multiple overlays"""

        print(f"   🎬 Composing final video with FFmpeg...")

        # Build FFmpeg command
        command = [
            'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
            '-i', stable_bg_path,  # Input 0: Background video
        ]

        # Add overlay inputs
        input_index = 1

        # Add account name text box
        if account_text_box_path and os.path.exists(account_text_box_path):
            command.extend(['-i', account_text_box_path])
            account_input_index = input_index
            input_index += 1
        else:
            account_input_index = None

        # Add question overlays
        question_input_indices = []
        for overlay_path in question_overlays:
            if overlay_path and os.path.exists(overlay_path):
                command.extend(['-i', overlay_path])
                question_input_indices.append(input_index)
                input_index += 1

        # Add answer overlays
        answer_input_indices = []
        for overlay_path in answer_overlays:
            if overlay_path and os.path.exists(overlay_path):
                command.extend(['-i', overlay_path])
                answer_input_indices.append(input_index)
                input_index += 1

        # Build filter complex
        filter_parts = []
        current_output = "[0:v]"

        # Add account name overlay (always visible)
        if account_input_index is not None:
            filter_parts.append(f"{current_output}[{account_input_index}:v]overlay=(W-w)/2:50[account]")
            current_output = "[account]"

        # Add question and answer overlays with timing
        for i in range(min(num_questions, len(question_input_indices), len(answer_input_indices))):
            question_start = i * self.config.QUESTION_DURATION
            question_end = question_start + self.config.QUESTION_DURATION
            answer_start = question_start + self.config.LAST_ANSWER_TIME

            # Question overlay (visible for the entire question duration)
            if i < len(question_input_indices):
                filter_parts.append(
                    f"{current_output}[{question_input_indices[i]}:v]overlay=(W-w)/2:(H-h)/2-50:"
                    f"enable='between(t,{question_start},{question_end})'[q{i}]"
                )
                current_output = f"[q{i}]"

            # Answer overlay (visible from answer_start to question_end)
            if i < len(answer_input_indices):
                filter_parts.append(
                    f"{current_output}[{answer_input_indices[i]}:v]overlay=(W-w)/2:(H-h)/2+100:"
                    f"enable='between(t,{answer_start},{question_end})'[a{i}]"
                )
                current_output = f"[a{i}]"

        # Add filter complex to command
        if filter_parts:
            command.extend(['-filter_complex', ';'.join(filter_parts)])
            command.extend(['-map', current_output])
        else:
            command.extend(['-map', '0:v'])

        # Add audio and output settings
        total_duration = num_questions * self.config.QUESTION_DURATION
        command.extend([
            '-map', '0:a',
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-t', str(total_duration),  # Total duration: 6 questions × 11 seconds = 66 seconds
            '-r', '30',
            output_path
        ])

        print(f"   🎬 Creating {total_duration}-second video ({num_questions} questions × {self.config.QUESTION_DURATION}s each)")

        print(f"   🎬 Running FFmpeg with {len(filter_parts)} overlay filters...")
        result = subprocess.run(command, capture_output=True, text=True)

        if result.returncode != 0:
            raise Exception(f"Failed to compose final video: {result.stderr}")

        print(f"   ✅ Final video composed successfully: {output_path}")
        return output_path

    def generate_video_from_quiz_data(self, questions, correct_answers, answer_options,
                                    account_config, output_path, num_questions=6):
        """Main method to generate video from quiz data - API integration point"""

        print(f"🎬 Starting OOP Video Generator V2.0.0...")
        print(f"   Account: {account_config.account_name} ({account_config.display_name})")
        print(f"   Questions: {len(questions)}")
        print(f"   Output: {output_path}")

        try:
            # Step 1: Get random background video
            background_video = self.background_processor.get_random_background_video(
                account_config.background_folder
            )
            print(f"   Selected background: {background_video}")

            # Step 2: Create the final video with all overlays
            result_path = self.create_final_video_with_overlays(
                background_video, questions, correct_answers, answer_options,
                account_config, output_path, num_questions
            )

            print(f"✅ OOP Video Generator completed successfully!")
            print(f"   Output file: {result_path}")

            # Verify the output file exists and has reasonable size
            if os.path.exists(result_path):
                file_size = os.path.getsize(result_path)
                print(f"   File size: {file_size:,} bytes ({file_size / 1024 / 1024:.1f} MB)")

                if file_size < 100000:  # Less than 100KB is suspicious
                    print("   ⚠️ Warning: Output file is very small, may indicate an issue")

                return result_path
            else:
                raise Exception("Output file was not created")

        except Exception as e:
            print(f"❌ OOP Video Generator failed: {str(e)}")
            raise

    def generate_video_from_csv(self, csv_file_path, account_config, output_path, num_questions=6):
        """Generate video from CSV file like the original script"""

        print(f"🎬 Loading questions from CSV: {csv_file_path}")

        # Load questions from CSV
        questions, correct_answers, answer_options = self.quiz_processor.load_questions_from_csv(
            csv_file_path, num_questions
        )

        print(f"   Loaded {len(questions)} questions from CSV")

        # Generate video
        return self.generate_video_from_quiz_data(
            questions, correct_answers, answer_options, account_config, output_path, num_questions
        )


# Factory function for easy API integration
def create_video_generator():
    """Factory function to create a VideoGenerator instance"""
    return VideoGenerator()


def create_account_config_from_api_data(account_name, display_name, background_folder,
                                       font_type, color_mode="mode1", output_folder="./output"):
    """Create AccountConfig from API data"""
    return AccountConfig(
        account_name=account_name,
        display_name=display_name,
        background_folder=background_folder,
        font_type=font_type,
        color_mode=color_mode,
        output_folder=output_folder
    )
