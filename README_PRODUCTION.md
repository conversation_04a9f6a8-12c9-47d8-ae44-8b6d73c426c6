# 🎬 Xvion - Production Ready Arabic Quiz Video Generator

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![Next.js](https://img.shields.io/badge/Next.js-15.5+-black.svg)](https://nextjs.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

A production-ready, enterprise-grade Arabic quiz video generator with comprehensive monitoring, security, and scalability features.

## 🚀 Production Features

### 🏗️ Architecture & Infrastructure
- **Docker Containerization**: Multi-stage builds with optimized images
- **Microservices Architecture**: Separate frontend, backend, and worker services
- **Load Balancing**: Nginx reverse proxy with SSL termination
- **Database**: PostgreSQL with connection pooling and migrations
- **Caching**: Redis for sessions, rate limiting, and background jobs
- **Background Processing**: Celery workers for async video generation

### 🔒 Security & Compliance
- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive data validation and sanitization
- **Rate Limiting**: API rate limiting and DDoS protection
- **CORS Configuration**: Secure cross-origin resource sharing
- **SSL/TLS**: HTTPS encryption with security headers
- **Environment Isolation**: Secure configuration management

### 📊 Monitoring & Observability
- **Health Checks**: Comprehensive system health monitoring
- **Metrics Collection**: Prometheus metrics for performance tracking
- **Error Tracking**: Sentry integration for error monitoring
- **Structured Logging**: Centralized logging with rotation
- **Performance Monitoring**: Request timing and resource usage tracking
- **Alerting**: Automated alerts for system issues

### 🔄 DevOps & Deployment
- **CI/CD Pipeline**: GitHub Actions for automated testing and deployment
- **Automated Testing**: Unit tests, integration tests, and security scans
- **Database Migrations**: Automated schema management
- **Blue-Green Deployment**: Zero-downtime deployment strategy
- **Backup & Recovery**: Automated database and file backups
- **Environment Management**: Development, staging, and production environments

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │────│  Next.js Frontend│────│   FastAPI API   │
│   (Port 80/443) │    │   (Port 3000)    │    │   (Port 8001)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Celery Worker  │────│   PostgreSQL    │
                       │  (Background)   │    │   (Port 5432)   │
                       └─────────────────┘    └─────────────────┘
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │      Redis      │────│   Monitoring    │
                       │   (Port 6379)   │    │ (Prometheus)    │
                       └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Production Deployment

### Prerequisites
- Docker & Docker Compose
- 4GB+ RAM, 10GB+ disk space
- Domain name with SSL certificates

### 1. Clone and Configure
```bash
git clone <repository-url>
cd xvion-video-generator

# Copy and configure environment
cp .env.example .env
nano .env  # Edit with your configuration
```

### 2. SSL Setup
```bash
# Place your SSL certificates
mkdir -p ssl
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem

# Or generate self-signed for testing
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes
```

### 3. Deploy
```bash
# Make deployment script executable
chmod +x deploy.sh

# Deploy to production
./deploy.sh production
```

### 4. Verify Deployment
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs -f

# Health check
curl https://your-domain.com/health
```

## 🛠️ Development Setup

### Local Development with Docker
```bash
# Start development environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

# Access services
# Frontend: http://localhost:3000
# Backend: http://localhost:8001
# Database: localhost:5432
```

### Manual Development Setup
```bash
# Backend setup
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python api_server_production.py

# Frontend setup
cd xvion-frontend
npm install
npm run dev
```

## 📋 Configuration

### Environment Variables (.env)
```bash
# Application
NODE_ENV=production
PYTHON_ENV=production
SECRET_KEY=your-secret-key-min-32-chars
JWT_SECRET=your-jwt-secret

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/xvion_db

# Redis
REDIS_URL=redis://localhost:6379/0

# API Keys
DEEPL_AUTH_KEY=your_deepl_key
ELEVENLABS_API_KEY=your_elevenlabs_key
UNSPLASH_API_KEY_1=your_unsplash_key
PIXABAY_API_KEY=your_pixabay_key
PEXELS_API_KEY=your_pexels_key

# Monitoring
SENTRY_DSN=your_sentry_dsn
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Security
CORS_ORIGINS=https://your-domain.com,http://localhost:3000
RATE_LIMIT_PER_MINUTE=60
MAX_FILE_SIZE_MB=100

# Server
HOST=0.0.0.0
PORT=8001
WORKERS=4
```

### Docker Compose Services
- **nginx**: Reverse proxy with SSL termination
- **frontend**: Next.js application
- **backend**: FastAPI server
- **postgres**: PostgreSQL database
- **redis**: Redis cache and message broker
- **celery-worker**: Background task processor
- **celery-beat**: Scheduled task manager

## 🔍 Monitoring & Health Checks

### Health Endpoints
- **Application Health**: `/health`
- **Database Health**: `/health/database`
- **Redis Health**: `/health/redis`
- **System Health**: `/health/system`

### Metrics (Prometheus)
- HTTP request metrics
- Database connection pool
- Video generation times
- System resource usage
- Error rates and response times

### Logging
- Structured JSON logging
- Log rotation and retention
- Error tracking with Sentry
- Performance monitoring

## 🧪 Testing

### Run Tests
```bash
# Backend tests
pytest tests/ -v --cov=.

# Frontend tests
cd xvion-frontend
npm test

# Integration tests
docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit
```

### Test Coverage
- Unit tests for core functionality
- Integration tests for API endpoints
- End-to-end tests for user workflows
- Security tests for vulnerabilities
- Performance tests for load handling

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
1. **Code Quality**: Linting, formatting, type checking
2. **Security Scanning**: Vulnerability scanning with Trivy
3. **Testing**: Unit tests, integration tests, coverage reports
4. **Building**: Docker image builds and registry push
5. **Deployment**: Automated deployment to staging/production

### Deployment Stages
- **Development**: Feature branches, local testing
- **Staging**: Integration testing, user acceptance testing
- **Production**: Blue-green deployment with health checks

## 📊 Performance & Scalability

### Performance Optimizations
- Database query optimization
- Redis caching for frequent data
- CDN for static assets
- Image optimization and compression
- Lazy loading and code splitting

### Scalability Features
- Horizontal scaling with load balancer
- Database connection pooling
- Background job processing
- Stateless application design
- Container orchestration ready

## 🔒 Security Features

### Authentication & Authorization
- JWT token-based authentication
- Role-based access control
- Session management with Redis
- Password hashing with bcrypt

### Input Validation & Sanitization
- Request data validation
- File upload security
- SQL injection prevention
- XSS protection

### Network Security
- HTTPS enforcement
- CORS configuration
- Rate limiting
- Security headers

## 📚 API Documentation

### Interactive Documentation
- **Swagger UI**: https://your-domain.com/api/docs
- **ReDoc**: https://your-domain.com/api/redoc

### Key Endpoints
```
POST /api/auth/login          # User authentication
GET  /api/quizzes            # List quizzes
POST /api/quizzes            # Create quiz
POST /api/videos/generate    # Generate video
GET  /api/jobs/{job_id}      # Check job status
GET  /api/health             # Health check
```

## 🚨 Troubleshooting

### Common Issues
1. **Database Connection**: Check DATABASE_URL and PostgreSQL service
2. **Redis Connection**: Verify Redis service and REDIS_URL
3. **SSL Issues**: Ensure certificates are properly configured
4. **Memory Issues**: Monitor system resources and adjust limits
5. **API Key Errors**: Verify all external API keys are valid

### Logs and Debugging
```bash
# View application logs
docker-compose logs -f backend

# Check system resources
docker stats

# Database logs
docker-compose logs postgres

# Redis logs
docker-compose logs redis
```

## 📞 Support & Maintenance

### Backup Strategy
- Automated daily database backups
- File system backups for uploads/output
- Configuration backups
- Disaster recovery procedures

### Maintenance Tasks
- Regular security updates
- Database maintenance and optimization
- Log cleanup and rotation
- Performance monitoring and tuning
- Dependency updates

### Support Channels
- GitHub Issues for bug reports
- Documentation wiki for guides
- Monitoring alerts for system issues
- Health check endpoints for status

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Submit a pull request
5. Ensure CI/CD pipeline passes

---

**Ready for production deployment with enterprise-grade features and monitoring.**
