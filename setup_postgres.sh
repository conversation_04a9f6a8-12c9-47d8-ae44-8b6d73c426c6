#!/bin/bash

# Setup script for PostgreSQL environment
# This script sets up the environment variables needed for PostgreSQL

echo "🔧 Setting up PostgreSQL environment..."

# Add PostgreSQL to PATH
export PATH="/usr/local/opt/postgresql@14/bin:$PATH"

# Set PostgreSQL environment variables
export PGHOST=localhost
export PGPORT=5432
export PGUSER=mohamedshady
export PGDATABASE=xvion_db

echo "✅ PostgreSQL environment configured!"
echo "📊 Database: $PGDATABASE"
echo "🔗 Host: $PGHOST:$PGPORT"
echo "👤 User: $PGUSER"

# Test connection
echo "🧪 Testing database connection..."
if psql -c "SELECT version();" > /dev/null 2>&1; then
    echo "✅ Database connection successful!"
else
    echo "❌ Database connection failed!"
    echo "Make sure PostgreSQL is running: brew services start postgresql@14"
fi
